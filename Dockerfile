#FROM node:latest
#LABEL authors="etheirystech"
#
## Install dependencies including Chromium
#RUN apt-get update && apt-get install -y \
#    chromium \
#    fonts-liberation \
#    libasound2 \
#    libatk-bridge2.0-0 \
#    libatk1.0-0 \
#    libcups2 \
#    libdbus-1-3 \
#    libgbm1 \
#    libnss3 \
#    libxcomposite1 \
#    libxdamage1 \
#    libxfixes3 \
#    libxrandr2 \
#    libxss1 \
#    libxtst6 \
#    git \
#    fonts-noto \
#    fonts-noto-cjk \
#    curl
#
## Set the working directory
#ARG APP_DIR=/app
#WORKDIR ${APP_DIR}
#
## Install dependencies
#RUN npm i -g pnpm@latest
#
## Tell Puppeteer to use the installed Chromium instead of downloading its own
#ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
#ENV PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium
#
#HEALTHCHECK --start-period=10s --interval=5s --timeout=3s \
#  CMD curl -f http://localhost:3000/api/health || exit 1
#
#CMD ["pnpm", "dev"]

# PRODUCTION

FROM node:latest
LABEL authors="etheirystech"

# Install dependencies including Chromium
RUN apt-get update && apt-get install -y \
    chromium \
    fonts-liberation \
    libasound2 \
    libatk-bridge2.0-0 \
    libatk1.0-0 \
    libcups2 \
    libdbus-1-3 \
    libgbm1 \
    libnss3 \
    libxcomposite1 \
    libxdamage1 \
    libxfixes3 \
    libxrandr2 \
    libxss1 \
    libxtst6 \
    git \
    fonts-noto \
    fonts-noto-cjk \
    curl

# Set the working directory
WORKDIR /app

# Copy the entire application code into the Docker image
COPY . .

# Install dependencies
RUN npm i -g pnpm@latest
RUN rm -rf node_modules && pnpm install
RUN pnpm add sharp
RUN pnpm build
# Tell Puppeteer to use the installed Chromium instead of downloading its own
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
ENV PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium

HEALTHCHECK --start-period=10s --interval=5s --timeout=3s \
  CMD curl -f http://localhost:3000/api/health || exit 1

CMD ["pnpm", "start"]
