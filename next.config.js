const fs = require("fs");
const path = require("path");
const vercelGitCommitSha = process.env.NEXT_PUBLIC_VERCEL_GIT_COMMIT_SHA;
const vercelDeploymentId =
  process.env.VERCEL_DEPLOYMENT_ID ||
  process.env.NEXT_PUBLIC_VERCEL_DEPLOYMENT_ID;
/** @type {import('next').NextConfig} */

const coreConfig = {
  experimental: {
    optimizePackageImports: ["@mantine/core", "@mantine/hooks"],
    // webVitalsAttribution: ["LCP", "FCP"],
  },
  poweredByHeader: false,
  // For SVG testing when you want to import them as objects.
  /*  webpack(config) {
    config.module.rules.push({
      test: /\.svg$/i,
      issuer: /\.[jt]sx?$/,
      use: ["@svgr/webpack"],
    });
    return config;
  },*/
  // Generate build ID for service worker versioning
  generateBuildId: async () => {
    if (
      process.env.NEXT_PUBLIC_VERCEL_GIT_COMMIT_SHA ||
      process.env.CI ||
      process.env.VERCEL
    ) {
      let newVersion;

      if (vercelGitCommitSha) {
        // Use Git commit SHA for Vercel deployments
        newVersion = `vercel-${vercelGitCommitSha}`;
      } else if (vercelDeploymentId) {
        // Use deployment ID as fallback
        newVersion = `vercel-${vercelDeploymentId}`;
      } else {
        // Local development fallback
        newVersion = `build-${Date.now()}`;
      }

      // Replace BUILD_ID placeholder in service worker
      const swPath = path.join(process.cwd(), "public", "sw.js");

      try {
        // Read the current service worker file
        let swContent = fs.readFileSync(swPath, "utf8");

        // Replace the VERSION constant with the new version
        swContent = swContent.replace(/\{\{BUILD_ID}}/g, newVersion);

        // Write the updated content back to the file
        fs.writeFileSync(swPath, swContent, "utf8");

        // console.log(`✅ Service worker version updated to: ${newVersion}`);
      } catch (error) {
        console.error("❌ Error updating service worker version:", error);
        process.exit(1);
      }
    }

    return (
      process.env.NEXT_PUBLIC_VERCEL_GIT_COMMIT_SHA || `build-${Date.now()}`
    );
  },

  // logging: {
  //   fetches: true,
  // },
  allowedDevOrigins: ["dev.etheirystech.com", "dev.meguminrs.com"],
  async rewrites() {
    return [
      {
        source: "/ingest/static/:path*",
        destination: "https://eu-assets.i.posthog.com/static/:path*",
      },
      {
        source: "/ingest/:path*",
        destination: "https://eu.i.posthog.com/:path*",
      },
      {
        source: "/ingest/decide",
        destination: "https://eu.i.posthog.com/decide",
      },
    ];
  },
  // This is required to support PostHog trailing slash API requests
  skipTrailingSlashRedirect: true,
  images: {
    minimumCacheTTL: 2678400,
    remotePatterns: [
      new URL(
        "https://koiszylnuxowacrxsevn.supabase.co/storage/v1/object/public/avatars/**",
      ),
      new URL("https://lh3.googleusercontent.com/**"),
      new URL("https://r2.ultimatetcgcm.com/**"),
    ],
  },
};

// Injected content via Sentry wizard below

const { withSentryConfig } = require("@sentry/nextjs");

module.exports = withSentryConfig(coreConfig, {
  // For all available options, see:
  // https://github.com/getsentry/sentry-webpack-plugin#options

  org: "etheirystech",
  project: "ultimate-tcg-card-maker",

  // Only print logs for uploading source maps in CI
  silent: !process.env.CI,

  // For all available options, see:
  // https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/

  // Upload a larger set of source maps for prettier stack traces (increases build time)
  widenClientFileUpload: true,

  // Automatically annotate React components to show their full name in breadcrumbs and session replay
  reactComponentAnnotation: {
    enabled: true,
  },

  // Route browser requests to Sentry through a Next.js rewrite to circumvent ad-blockers.
  // This can increase your server load as well as your hosting bill.
  // Note: Check that the configured route will not match with your Next.js middleware, otherwise reporting of client-
  // side errors will fail.

  // tunnelRoute: "/monitoring",

  // Hides source maps from generated client bundles
  hideSourceMaps: true,

  // Automatically tree-shake Sentry logger statements to reduce bundle size
  disableLogger: true,

  // Enables automatic instrumentation of Vercel Cron Monitors. (Does not yet work with App Router route handlers.)
  // See the following for more information:
  // https://docs.sentry.io/product/crons/
  // https://vercel.com/docs/cron-jobs
  automaticVercelMonitors: true,
  sourcemaps: {
    deleteSourcemapsAfterUpload: true,
  },
});
