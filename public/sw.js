// Service Worker for Update Detection
const CACHE_NAME = "app-cache-v1";
// Minimal service worker that won't fail
const VERSION = "{{BUILD_ID}}";

self.addEventListener("install", () => self.skipWaiting());
self.addEventListener("activate", () => self.clients.claim());
self.addEventListener("message", (event) => {
  if (event.data?.type === "SKIP_WAITING") {
    self.skipWaiting();
  }
});

// Catch all errors
self.addEventListener("error", (e) => e.preventDefault());
self.addEventListener("unhandledrejection", (e) => e.preventDefault());
