"use client";
import { AppShell, <PERSON> } from "@mantine/core";
import Link from "next/link";
import React from "react";
import { useDisclosure } from "@/hooks/useDisclosure";
import { usePathname } from "next/navigation";

export default function GlobalLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [opened, { toggle }] = useDisclosure();
  const path = usePathname();

  if (path && path.startsWith("/getCard")) {
    return <>{children}</>;
  }
  return (
    <>
      <AppShell
        header={{ height: 60 }}
        navbar={
          path !== "/"
            ? {
                width: 300,
                breakpoint: "md",
                collapsed: { mobile: !opened },
              }
            : undefined
        }
        padding="md"
      >
        <AppShell.Header className={"flex items-center gap-4 pl-4"}>
          {path !== "/" && (
            <Burger
              opened={opened}
              onClick={toggle}
              hiddenFrom="md"
              size="lg"
            />
          )}

          {path !== "/" && (
            <Link
              href={"/"}
              className={
                "rounded-sm text-neutral-900 transition-colors duration-200 active:bg-neutral-200 lg:hover:bg-neutral-200 lg:active:bg-neutral-300 dark:text-neutral-100 dark:active:bg-[#25262B] dark:lg:hover:bg-[#25262B] dark:lg:active:bg-[#373A40]"
              }
              onClick={() => {}}
            >
              Ultimate TCG Card Maker
            </Link>
          )}
        </AppShell.Header>
        {path !== "/" && (
          <AppShell.Navbar p={"md"}>
            <div className={"flex flex-col"}>
              <>
                <Link
                  href={"/one-piece/character"}
                  onClick={() => {
                    toggle();
                  }}
                  className={
                    "rounded-sm px-2! py-2! text-neutral-900 transition-colors duration-200 active:bg-neutral-200 lg:hover:bg-neutral-200 lg:active:bg-neutral-300 dark:text-neutral-100 dark:active:bg-[#25262B] dark:lg:hover:bg-[#25262B] dark:lg:active:bg-[#373A40]"
                  }
                >
                  Character
                </Link>
                <Link
                  href={"/one-piece/leader"}
                  onClick={toggle}
                  className={
                    "rounded-sm px-2! py-2! text-neutral-900 transition-colors duration-200 active:bg-neutral-200 lg:hover:bg-neutral-200 lg:active:bg-neutral-300 dark:text-neutral-100 dark:active:bg-[#25262B] dark:lg:hover:bg-[#25262B] dark:lg:active:bg-[#373A40]"
                  }
                >
                  Leader
                </Link>
                <Link
                  href={"/one-piece/event"}
                  onClick={() => {
                    toggle();
                  }}
                  className={
                    "rounded-sm px-2! py-2! text-neutral-900 transition-colors duration-200 active:bg-neutral-200 lg:hover:bg-neutral-200 lg:active:bg-neutral-300 dark:text-neutral-100 dark:active:bg-[#25262B] dark:lg:hover:bg-[#25262B] dark:lg:active:bg-[#373A40]"
                  }
                >
                  Event
                </Link>
                <Link
                  href={"/one-piece/stage"}
                  onClick={() => {
                    toggle();
                  }}
                  className={
                    "rounded-sm px-2! py-2! text-neutral-900 transition-colors duration-200 active:bg-neutral-200 lg:hover:bg-neutral-200 lg:active:bg-neutral-300 dark:text-neutral-100 dark:active:bg-[#25262B] dark:lg:hover:bg-[#25262B] dark:lg:active:bg-[#373A40]"
                  }
                >
                  Stage
                </Link>
                <Link
                  href={"/one-piece/don"}
                  onClick={() => {
                    toggle();
                  }}
                  className={
                    "rounded-sm px-2! py-2! text-neutral-900 transition-colors duration-200 active:bg-neutral-200 lg:hover:bg-neutral-200 lg:active:bg-neutral-300 dark:text-neutral-100 dark:active:bg-[#25262B] dark:lg:hover:bg-[#25262B] dark:lg:active:bg-[#373A40]"
                  }
                >
                  Don
                </Link>
              </>
            </div>
          </AppShell.Navbar>
        )}
        <AppShell.Main>{children}</AppShell.Main>
      </AppShell>
    </>
  );
}
