"use client";
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import UpdateUsernameDialog from "@/app/account/UpdateUsernameDialog";
import CancelSubscriptionDialog from "@/app/account/components/CancelSubscriptionDialog";
import SubscriptionDialog from "@/app/account/components/SubscriptionDialog";
import ManageSubscriptionDialog from "@/app/account/components/ManageSubscriptionDialog";
import OldCancelSubscriptionDialog from "@/app/account/components/OldCancelSubscriptionDialog";
import AccountSubscriptionDetails from "@/app/account/components/AccountSubscriptionDetails";
import ChangeUsernameAndPasswordSection from "@/app/account/components/ChangeUsernameAndPasswordSection";
import AccountEmail from "@/app/account/components/AccountEmail";
import AccountUsername from "@/app/account/components/AccountUsername";
import AccountSignOutButton from "@/app/account/components/AccountSignOutButton";
import AccountAvatar from "@/app/account/components/AccountAvatar";
import { useAccountContext } from "@/app/account/components/AccountContextProvider";

export default function AccountForm({
  currentAvatarPath,
}: {
  currentAvatarPath: string | null;
}) {
  const router = useRouter();
  const { subId } = useAccountContext();

  return (
    <div className="form-widget mx-[2rem]! sm:mx-[3rem]! md:mx-[10%]! lg:mx-[15%]! xl:mx-[30%]!">
      <Button
        className="mt-4"
        variant="outline"
        onClick={() => {
          router.back();
        }}
      >
        Back
      </Button>
      {/* Form Content*/}
      <div
        className={
          "mt-[2rem]! mb-1.5! flex flex-col gap-2 sm:mt-[3rem]! md:mt-[4rem]! lg:mt-[5rem]!"
        }
      >
        <AccountAvatar currentAvatarPath={currentAvatarPath} />
        <AccountSubscriptionDetails />

        <AccountEmail />
        <AccountUsername />
        <ChangeUsernameAndPasswordSection />

        <AccountSignOutButton />

        {/*Subscription ID & Contact*/}
        {subId && <p id="email">Subscription ID: {subId}</p>}
        <p id="email">Contact: <EMAIL></p>
      </div>

      {/*Dialogs / Modals*/}
      <>
        <SubscriptionDialog />

        <ManageSubscriptionDialog />
        <UpdateUsernameDialog />
        <CancelSubscriptionDialog />

        <OldCancelSubscriptionDialog />
      </>
    </div>
  );
}
