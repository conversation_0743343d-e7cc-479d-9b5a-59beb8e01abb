"use client";
import React, { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, useRef, useState } from "react";
import { Database } from "@/types/supabase";
import { Avatar as ShadcnAvatar, AvatarFallback } from "@/components/ui/avatar";
import { LoadingButton } from "@/components/ui/loading-button";
import { createClient } from "@/utils/supabase/client";
import { User } from "@supabase/supabase-js";
import { removeOldAvatar } from "@/app/account/actions";
import Image from "next/image";
import { Input } from "@/components/ui/input";
import { showErrorToast } from "@/lib/toast";

type Profiles = Database["public"]["Tables"]["profiles"]["Row"];
// TODO: Update the upload so that it allows users to crop the image, compress the image and limit it to 5mb.
export default function Avatar({
  uid,
  url,
  size,
  onUpload,
  user,
  currentAvatarPath,
}: {
  uid: string;
  url: Profiles["avatar_url"] | null;
  size: number;
  onUpload: { upload: (url: string) => Promise<null | string | undefined> };
  user: User;
  currentAvatarPath: string | null;
}) {
  const supabase = createClient();
  const [avatarUrl, setAvatarUrl] = useState<Profiles["avatar_url"] | null>(
    !url?.includes("googleusercontent") ? url : null,
  );
  const [uploading, setUploading] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const handleClick: MouseEventHandler<HTMLButtonElement> = (e) => {
    e.preventDefault();
    inputRef.current?.click();
  };

  const uploadAvatar = async (
    file: File | null,
    currentAvatarPath: string | null,
  ) => {
    try {
      setUploading(true);
      await removeOldAvatar(currentAvatarPath);
      if (!file) {
        throw new Error("You must select an image to upload.");
      }

      const fileExt = file.name.split(".").pop();
      const filePath = `${uid}-${Math.random()}.${fileExt}`;

      const { error: uploadError } = await supabase.storage
        .from("avatars")
        .upload(filePath, file);

      if (uploadError) {
        throw uploadError;
      }

      const avatarUrl = await onUpload.upload(filePath);
      if (avatarUrl) {
        const {
          data: { publicUrl },
        } = await supabase.storage.from("avatars").getPublicUrl(avatarUrl);
        setAvatarUrl(publicUrl);
      }
    } catch (error: Error | unknown) {
      if (error instanceof Error) {
        showErrorToast(`Error uploading avatar! ${error.message}`);
      }
    } finally {
      setUploading(false);
    }
  };
  return (
    <div>
      {avatarUrl || user.user_metadata.avatar_url ? (
        <Image
          src={avatarUrl ? avatarUrl : user.user_metadata.avatar_url}
          width={92}
          height={92}
          className={
            "mb-4 h-[6rem] w-[6rem] rounded-full border-2 border-solid border-neutral-200 dark:border-neutral-100"
          }
          alt="Avatar"
        />
      ) : (
        <ShadcnAvatar className="mb-4 h-[6rem] w-[6rem] border-2 border-solid border-neutral-200 dark:border-neutral-100">
          <AvatarFallback>{user.email?.charAt(0).toUpperCase()}</AvatarFallback>
        </ShadcnAvatar>
      )}

      <div style={{ width: size }}>
        <div className="relative">
          <Input
            type="file"
            accept="image/*"
            hidden
            ref={inputRef}
            className="absolute inset-0 cursor-pointer"
            onChange={(e) => {
              if (e.target.files && e.target.files.length > 0) {
                uploadAvatar(e.target.files[0], currentAvatarPath);
              }
            }}
          />
          <LoadingButton
            className={
              "bg-neutral-950 dark:bg-neutral-200 hover:dark:bg-neutral-300!"
            }
            loading={uploading}
            onClick={handleClick}
          >
            Upload image
          </LoadingButton>
        </div>
      </div>
    </div>
  );
}
