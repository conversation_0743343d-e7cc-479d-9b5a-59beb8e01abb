import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import React from "react";
import { updateUsernameAction } from "@/app/account/actions";
import { showSuccessToast, showErrorToast } from "@/lib/toast";
import { useAccountContext } from "@/app/account/components/AccountContextProvider";

export default function UpdateUsernameDialog() {
  const {
    openedUpdateUsernameModal,
    closeUpdateUsernameModal,
    setLoading,
    setUsername,
    loading,
    user,
  } = useAccountContext();
  const userId = user?.id;
  const [newUsername, setNewUsername] = React.useState<string | null>(null);
  async function updateUsername({
    username,
    userId,
  }: {
    username: string | null;
    userId: string | undefined;
  }) {
    setLoading(true);
    if (username && userId) {
      try {
        if (username.length < 3) {
          throw new Error("Username must contain at least 3 characters.");
        }
        const res = await updateUsernameAction({
          newUserName: username,
          userId,
        });
        if (res.status === 200) {
          showSuccessToast("Success", {
            description: `Successfully updated username to ${username}.`,
          });
          setUsername(username);
          closeUpdateUsernameModal();
        }
        if (res.status === 409) {
          showErrorToast(`Failed`, {
            description:
              "Someone already has this username, please choose another.",
          });
        }
        setLoading(false);
      } catch (error: Error | unknown) {
        setLoading(false);
        if (error instanceof Error) {
          showErrorToast(`Failed`, {
            description: `${error?.message}`,
          });
        } else {
          showErrorToast(`Failed`, {
            description: "An unknown error occurred.",
          });
        }
      }
    } else {
      setLoading(false);
      showErrorToast(`Failed`, {
        description: "No username provided",
      });
    }
  }

  return (
    <Dialog
      open={openedUpdateUsernameModal}
      onOpenChange={(open) => !open && closeUpdateUsernameModal()}
      modal={true}
    >
      <DialogTitle />
      <DialogContent className="max-w-xl" disableCloseIcon>
        <div className="flex flex-col gap-7">
          <Input
            className="w-full"
            value={newUsername || ""}
            onChange={(e) => setNewUsername(e.target.value)}
          />
          <Button
            className="w-full"
            onClick={() =>
              updateUsername({ username: newUsername, userId: userId })
            }
            loading={loading}
          >
            Update username
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
// {
//   setLoading: React.Dispatch<React.SetStateAction<boolean>> | ((loading: boolean) => void);
//
//   setUsername: React.Dispatch<React.SetStateAction<string | null>> | ((username: string | null) => void);
// }
