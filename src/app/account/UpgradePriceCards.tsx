import PricingCard from "@/components/PricingCard";
import React, { useState } from "react";

import { showSuccessToast, showErrorToast } from "@/lib/toast";

export default function UpgradePriceCards({
  prices,
  openCheckout,
  subscriptionID,
  closeManageSubscription,
}: {
  prices: Array<{
    productName: string;
    priceId: string;
    price: string;
    interval: string;
    featureList: string[];
    name: string;
    remaining?: string | null;
    initialLimit?: string | null;
  }>;
  openCheckout: (priceId: string) => void;
  subscriptionID: string | null;
  closeManageSubscription: () => void;
}) {
  const [loading, setLoading] = useState(false);
  if (!subscriptionID) return null;
  return prices.map((price) => {
    if (price.remaining === "0") return null;
    if (price.interval === "year")
      return (
        <PricingCard
          title={`${price.name}`}
          loading={loading}
          className={
            "min-w-fit border-2 border-blue-400 md:max-w-[25rem] md:min-w-[15rem]"
          }
          buttonClassName={
            "mt-auto bg-blue-400 dark:bg-blue-400 dark:text-white dark:hover:bg-blue-500 hover:bg-blue-500"
          }
          featureList={price.featureList}
          buttonText={
            price.productName.includes("Creator") ||
            price.name.includes("Lifetime")
              ? "Upgrade"
              : "Downgrade"
          }
          buttonLink={"/account"}
          key={price.priceId}
          price={price.price}
          recurring={price.interval}
          onClick={async function () {
            try {
              setLoading(true);
              const res = await fetch("/api/paddle/modify-subscription", {
                method: "PATCH",
                headers: {
                  "Content-Type": "application/json",
                },
                body: JSON.stringify({
                  priceId: price.priceId,
                  subscriptionId: subscriptionID,
                }),
              });

              const data = await res.json();

              if (!res.ok) {
                throw new Error(
                  `Failed to update subscription: ${data.error?.detail || "Unknown error"}`,
                );
              }

              setLoading(false);
              closeManageSubscription();
              showSuccessToast(
                `${price.productName.includes("Creator") ? "Upgrade" : "Downgrade"} completed`,
                {
                  description: `Successfully ${price.productName.includes("Creator") ? "upgraded" : "downgraded"} to ${price.name}`,
                },
              );
            } catch (error: { title: string; message: string } | unknown) {
              setLoading(false);
              console.error("Error updating subscription:", error);
              if (error instanceof Error) {
                showErrorToast(`Failed`, {
                  description: `${error?.message}`,
                });
              } else {
                showErrorToast(`Failed`, {
                  description: "Something went wrong please try again.",
                });
              }
            }
          }}
          remaining={
            price.remaining && price.initialLimit
              ? {
                  remaining: price.remaining,
                  limit: price.initialLimit,
                }
              : null
          }
        />
      );
    return (
      <PricingCard
        className={"min-w-fit md:max-w-[25rem] md:min-w-[15rem]"}
        loading={loading}
        title={`${price.name}`}
        featureList={price.featureList}
        buttonText={
          price.productName.includes("Creator") ||
          price.name.includes("Lifetime")
            ? "Upgrade"
            : "Downgrade"
        }
        buttonLink={"/account"}
        key={price.priceId}
        price={price.price}
        recurring={price.interval}
        onClick={async function () {
          if (price.name.includes("Lifetime")) {
            openCheckout(price.priceId);
          } else {
            try {
              setLoading(true);
              const res = await fetch("/api/paddle/modify-subscription", {
                method: "PATCH",
                headers: {
                  "Content-Type": "application/json",
                },
                body: JSON.stringify({
                  priceId: price.priceId,
                  subscriptionId: subscriptionID,
                }),
              });

              const data = await res.json();

              if (!res.ok) {
                throw new Error(
                  `Failed to update subscription: ${data.error?.detail || "Unknown error"}`,
                );
              }

              setLoading(false);
              closeManageSubscription();
              showSuccessToast(
                `${price.productName.includes("Creator") ? "Upgrade" : "Downgrade"} completed`,
                {
                  description: `Successfully ${price.productName.includes("Creator") ? "upgraded" : "downgraded"} to ${price.name}`,
                },
              );
            } catch (error: { title: string; message: string } | unknown) {
              setLoading(false);
              console.error("Error updating subscription:", error);
              if (error instanceof Error) {
                showErrorToast(`Failed`, {
                  description: `${error?.message}`,
                });
              } else {
                showErrorToast(`Failed`, {
                  description: "Something went wrong please try again.",
                });
              }
            }
          }
        }}
        remaining={
          price.remaining && price.initialLimit
            ? { remaining: price.remaining, limit: price.initialLimit }
            : null
        }
      />
    );
  });
}
