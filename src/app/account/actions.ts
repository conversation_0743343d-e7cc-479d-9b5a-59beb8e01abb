"use server";
import { createAdminClient } from "@/utils/supabase/adminClient";
import { createClient } from "@/utils/supabase/server";

export async function removeOldAvatar(currentAvatarPath: string | null) {
  const supabase = createAdminClient();
  if (currentAvatarPath) {
    await supabase.storage.from("avatars").remove([`${currentAvatarPath}`]);
  }
}

export async function updateUsernameAction({
  newUserName,
  userId,
}: {
  newUserName: string;
  userId: string;
}) {
  const supabase = await createClient();
  return supabase
    .from("profiles")
    .update({ username: newUserName })
    .eq("id", userId)
    .select();
}

export async function updateProfilePicture({
  newAvatarURL,
  userId,
}: {
  newAvatarURL: string;
  userId: string;
}) {
  const supabase = await createClient();
  return supabase
    .from("profiles")
    .update({ avatar_url: newAvatarURL })
    .eq("id", userId)
    .select();
}
