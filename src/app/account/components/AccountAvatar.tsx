"use client";
import { updateAvatar } from "@/app/account/helpers/updateAvatar";
import Avatar from "@/app/account/Avatar";
import React from "react";
import { useAccountContext } from "@/app/account/components/AccountContextProvider";

export default function AccountAvatar({
  currentAvatarPath,
}: {
  currentAvatarPath: string | null;
}) {
  const { user, avatarUrl, setAvatarUrl, setLoading } = useAccountContext();

  if (user) {
    return (
      <Avatar
        uid={user.id}
        url={avatarUrl}
        size={150}
        user={user}
        currentAvatarPath={currentAvatarPath}
        onUpload={{
          upload: async (url) => {
            setAvatarUrl(url);
            return updateAvatar({
              newAvatarURL: url,
              userId: user.id,
              setLoading: setLoading,
            });
          },
        }}
      />
    );
  }
  return null;
}
