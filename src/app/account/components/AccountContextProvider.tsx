"use client";
import { createContext, useContext, useState } from "react";
import { useRouter } from "next/navigation";
import { useDisclosure } from "@/hooks/useDisclosure";
import { Paddle } from "@paddle/paddle-js";
import { Subscription, SubscriptionStatus } from "@/types";
import { User } from "@supabase/supabase-js";
import { createOpenCheckout } from "@/app/account/helpers/createOpenCheckout";
import { createClient } from "@/utils/supabase/client";
import { AccountContextType } from "@/app/account/types";
import { useInitializeAccountData } from "@/app/account/hooks/useInitializeAccountData";

const AccountContext = createContext<AccountContextType | null>(null);
const supabase = createClient();

export function useAccountContext() {
  const context = useContext(AccountContext);
  if (!context) {
    throw new Error("useAccountContext must be used within an AccountProvider");
  }
  return context;
}

export function AccountContextProvider({
  children,
  user,
  userIpAddress,
  avatarURL,
  initialUsername,
  initialSubDate,
  initialSubStatus,
  initialSubID,
  initialOG,
  subscription,
}: {
  children: React.ReactNode;
  user: User | null;
  userIpAddress: string;
  avatarURL: string;
  initialUsername: string | null;
  initialSubStatus: SubscriptionStatus;
  initialSubDate: string | null;
  initialSubID: string | null;
  initialOG: boolean | null;
  subscription: Subscription;
}) {
  const [paddle, setPaddle] = useState<Paddle>();
  const [productPrices, setProductPrices] = useState<
    Array<{
      productName: string;
      priceId: string;
      price: string;
      interval: string;
      featureList: string[];
      name: string;
      remaining?: string | null;
      initialLimit?: string | null;
    }>
  >([]);

  const [loading, setLoading] = useState(false);
  const [username, setUsername] = useState<string | null>(initialUsername);
  const [subStatus, setSubStatus] =
    useState<SubscriptionStatus>(initialSubStatus);
  const [OG, setOG] = useState<boolean | null>(initialOG);
  const [subId, setSubId] = useState<string | null>(initialSubID);
  const [avatarUrl, setAvatarUrl] = useState<string | null>(avatarURL);
  const [subDate, setSubDate] = useState<string | null>(initialSubDate);
  const [
    openedSubscription,
    { open: openSubscription, close: closeSubscription },
  ] = useDisclosure(false);

  const proPrices = productPrices.filter((price) =>
    price.productName.includes("Pro"),
  );
  const creatorPrices = productPrices.filter(
    (price) =>
      price.productName.includes("Creator") || price.name.includes("Lifetime"),
  );

  const router = useRouter();
  const [
    openedCancelSubscription,
    { open: openCancelSubscription, close: closeCancelSubscription },
  ] = useDisclosure(false);
  const [
    openedUpdateUsernameModal,
    { open: openUpdateUsernameModal, close: closeUpdateUsernameModal },
  ] = useDisclosure(false);
  const [
    openedManageSubscription,
    { open: openManageSubscription, close: closeManageSubscription },
  ] = useDisclosure(false);
  const [
    openedOldCancelSubscription,
    { open: openOldCancelSubscription, close: closeOldCancelSubscription },
  ] = useDisclosure(false);

  useInitializeAccountData({
    closeSubscription,
    user,
    router,
    supabase,
    paddle,
    OG,
    setOG,
    setSubId,
    setPaddle,
    setUsername,
    setAvatarUrl,
    setSubStatus,
    setSubDate,
    setLoading,
    setProductPrices,
    userIpAddress,
    subscription,
    closeManageSubscription,
  });

  const openCheckout = createOpenCheckout({ user, paddle, closeSubscription });

  return (
    <AccountContext.Provider
      value={{
        openedCancelSubscription,
        closeCancelSubscription,
        openCancelSubscription,
        openedUpdateUsernameModal,
        closeUpdateUsernameModal,
        openUpdateUsernameModal,
        openedManageSubscription,
        closeManageSubscription,
        openManageSubscription,
        openedOldCancelSubscription,
        closeOldCancelSubscription,
        openOldCancelSubscription,
        openedSubscription,
        closeSubscription,
        openSubscription,
        proPrices,
        creatorPrices,
        openCheckout,
        subscription,
        subId,
        subStatus,
        router,
        loading,
        setLoading,
        username,
        setUsername,
        subDate,
        setSubDate,
        OG,
        setOG,
        setSubId,
        avatarUrl,
        setAvatarUrl,
        setSubStatus,
        user,
      }}
    >
      {children}
    </AccountContext.Provider>
  );
}
