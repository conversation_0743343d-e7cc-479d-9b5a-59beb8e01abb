"use client";
import { SubStatus } from "@/app/account/components/SubStatus";
import ResumeSubscription from "@/components/ResumeSubscription";
import { OpenManageSubModalButton } from "@/app/account/components/OpenManageSubModalButton";
import { OpenSubModalButton } from "@/app/account/components/OpenSubModalButton";
import SubscriptionExpirationTimer from "@/components/SubscriptionExpirationTimer";
import React from "react";
import { useAccountContext } from "@/app/account/components/AccountContextProvider";

export default function AccountSubscriptionDetails() {
  const {
    subscription,
    subStatus,
    subDate,
    subId,
    openManageSubscription,
    openOldCancelSubscription,
    openSubscription,
  } = useAccountContext();
  return (
    <>
      <div className={"flex flex-wrap items-center gap-2"}>
        <SubStatus
          subStatus={subStatus}
          subDate={subDate}
          subscription={subscription}
        />
        <ResumeSubscription
          subDate={subDate}
          subStatus={subStatus}
          subId={subId}
          subscription={subscription}
          updatePaymentMethod={subscription.updatePaymentMethod}
        />

        <OpenManageSubModalButton
          openOldCancelSubscription={openOldCancelSubscription}
          subStatus={subStatus}
          subId={subId}
          subscription={subscription}
          openManageSubscription={openManageSubscription}
        />
        <OpenSubModalButton
          subDate={subDate}
          subStatus={subStatus}
          openSubscription={openSubscription}
          subId={subId}
        />
      </div>
      <SubscriptionExpirationTimer subscription={subscription} />
    </>
  );
}
