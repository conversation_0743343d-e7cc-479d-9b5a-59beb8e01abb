import React from "react";
import { useAccountContext } from "@/app/account/components/AccountContextProvider";

export default function AccountUsername() {
  const { username, user } = useAccountContext();
  const email = user?.email;
  return (
    <div className={"flex items-center gap-2"}>
      <label htmlFor="username">Username:</label>
      <p>{username ? username : `${email?.split("@")[0]}`}</p>
    </div>
  );
}
