"use client";
import { <PERSON><PERSON>, DialogContent, DialogTitle } from "@/components/ui/dialog";
import {
  <PERSON>,
  CardContent,
  <PERSON><PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/Card";
import React, { useState } from "react";
import { createClient } from "@/utils/supabase/client";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { pauseSubscription } from "@/app/account/helpers/pauseSubscription";
import { cancelSubscription } from "@/app/account/helpers/cancelSubscription";
import { useAccountContext } from "@/app/account/components/AccountContextProvider";

export default function CancelSubscriptionDialog() {
  const {
    openedCancelSubscription,
    closeCancelSubscription,
    closeManageSubscription,
  } = useAccountContext();
  return (
    <Dialog
      open={openedCancelSubscription}
      onOpenChange={closeCancelSubscription}
      modal={true}
    >
      <DialogTitle />
      <DialogContent className="max-w-[780px]!">
        <p className={"mb-5! text-neutral-800 dark:text-neutral-50"}>
          Are you sure you want to cancel the subscription?
        </p>
        <div
          className={
            "flex max-w-5xl grow flex-col flex-wrap items-stretch gap-4 px-4! sm:flex-row"
          }
        >
          <Card className="flex-1 basis-1/3 md:basis-0">
            <CardHeader>
              <CardTitle className="text-xl">Pause</CardTitle>
            </CardHeader>
            <CardContent className="flex flex-col gap-2">
              <p>
                Your subscription will be paused and you will not be charged for
                the next billing cycle.
              </p>
              <p>
                Your plan features will remain active until the end of the
                current billing cycle.
              </p>
              <p>You will be able to resume the subscription at any time.</p>
              <p>
                If you resume the subscription after the next billing cycle, you
                will be charged immediately and that time will become your new
                billing cycle start.
              </p>
              <p>
                If you resume the subscription before the next billing cycle,
                you will be charged at the start of the next billing cycle.
              </p>
            </CardContent>
            <CardFooter>
              <CancelButton
                closeCancelSubscription={closeCancelSubscription}
                closeManageSubscription={closeManageSubscription}
                type={"pause"}
              />
            </CardFooter>
          </Card>
          <Card className="flex-1 basis-1/3 md:basis-0">
            <CardHeader>
              <CardTitle className="text-xl">Cancel</CardTitle>
            </CardHeader>
            <CardContent className="flex flex-col gap-2">
              <p>
                Your subscription will be canceled and you will not be charged
                for the next billing cycle.
              </p>
              <p>
                Your plan features will remain active until the end of the
                current billing cycle.
              </p>
              <p>
                You will be able to resume the subscription before the next
                billing cycle.
              </p>
              <p>
                If you resume the subscription you will be charged at the start
                of the next billing cycle.
              </p>
            </CardContent>
            <CardFooter>
              <CancelButton
                closeCancelSubscription={closeCancelSubscription}
                closeManageSubscription={closeManageSubscription}
                type={"cancel"}
              />
            </CardFooter>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
}

function CancelButton({
  closeCancelSubscription,
  closeManageSubscription,
  type,
}: {
  closeCancelSubscription: () => void;
  closeManageSubscription: () => void;
  type: "pause" | "cancel";
}) {
  const supabase = createClient();
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  if (type === "pause") {
    return (
      <Button
        className="mt-auto w-full text-neutral-50"
        variant="destructive"
        loading={loading}
        onClick={() =>
          pauseSubscription({
            closeCancelSubscription,
            closeManageSubscription,
            setLoading,
            router,
            supabase,
          })
        }
      >
        Pause Subscription
      </Button>
    );
  }
  if (type === "cancel") {
    return (
      <Button
        className="mt-auto w-full text-neutral-50"
        variant="destructive"
        loading={loading}
        onClick={() =>
          cancelSubscription({
            closeCancelSubscription,
            closeManageSubscription,
            setLoading,
            router,
            supabase,
          })
        }
      >
        Cancel Subscription
      </Button>
    );
  }
  return null;
}
