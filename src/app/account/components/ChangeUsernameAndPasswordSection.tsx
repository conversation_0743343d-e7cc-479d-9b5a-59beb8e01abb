"use client";
import { But<PERSON> } from "@/components/ui/button";
import React from "react";
import { useAccountContext } from "@/app/account/components/AccountContextProvider";

export default function ChangeUsernameAndPasswordSection() {
  const { user, router, openUpdateUsernameModal } = useAccountContext();
  return (
    <div className={"flex justify-start gap-2"}>
      <div className={"flex flex-col items-start gap-2"}>
        <Button className="w-full" onClick={openUpdateUsernameModal}>
          Update username
        </Button>
        {/* Only show password change button for email/password authentication */}
        {user?.identities &&
          user?.identities?.length === 1 &&
          user?.identities?.some(
            (identity) => identity.provider === "email",
          ) && (
            <Button
              className="w-full"
              onClick={() => {
                router.push("/reset-password");
              }}
            >
              Change password
            </Button>
          )}
      </div>
    </div>
  );
}
