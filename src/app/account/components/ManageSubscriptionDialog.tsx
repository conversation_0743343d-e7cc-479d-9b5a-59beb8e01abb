"use client";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import UpgradePriceCards from "@/app/account/UpgradePriceCards";
import { Button } from "@/components/ui/button";
import React from "react";
import { useAccountContext } from "@/app/account/components/AccountContextProvider";

export default function ManageSubscriptionDialog() {
  const {
    openedManageSubscription,
    closeManageSubscription,
    proPrices,
    creatorPrices,
    openCheckout,
    subId,
    loading,
    subscription,
    openCancelSubscription,
    openOldCancelSubscription,
    subStatus,
    router,
  } = useAccountContext();
  return (
    <Dialog
      open={openedManageSubscription}
      onOpenChange={closeManageSubscription}
      modal={true}
    >
      <DialogTitle />
      <DialogContent className="max-w-[780px]! pt-10">
        <div className={"flex flex-col items-center gap-4"}>
          <div
            className={
              "flex flex-col gap-2 rounded-md border-2 border-solid border-amber-400 bg-amber-50 p-2 dark:border-amber-500 dark:bg-amber-900"
            }
          >
            <p>
              Upgrading your subscription as well as downgrading from monthly to
              yearly subscription will reset your billing date to today, with
              the next charge occurring one month or one year from now,
              depending on your selected plan.
            </p>
            <p>
              All upgrades or changes from monthly to yearly will be{" "}
              <span className={"font-bold underline underline-offset-2"}>
                charged immediately
              </span>
              .{" "}
              <span>
                Upgrades and changes are prorated, which means that you will
                receive a credit for the remaining time of your subscription,
                which will be applied to your current charge.
              </span>{" "}
              <span>Downgrades will not be charged.</span>
            </p>
          </div>

          <div
            className={
              "flex max-w-5xl grow flex-col flex-wrap items-stretch justify-center gap-4 px-4! sm:min-h-[18rem] sm:flex-row"
            }
          >
            <UpgradePriceCards
              prices={
                subscription.subscriptionName.includes("Pro")
                  ? creatorPrices
                  : proPrices
              }
              openCheckout={openCheckout}
              subscriptionID={subId}
              closeManageSubscription={closeManageSubscription}
            />
          </div>
          <div
            className={"flex w-full flex-col justify-between gap-4 sm:flex-row"}
          >
            <Button
              className="bg-green-600 text-neutral-50 hover:bg-green-700"
              loading={loading}
              onClick={() => {
                if (subscription.updatePaymentMethod)
                  router.push(`${subscription.updatePaymentMethod}`);
              }}
            >
              Update payment method
            </Button>
            <OpenCancelSubModalButton
              openCancelSubscription={openCancelSubscription}
              openOldCancelSubscription={openOldCancelSubscription}
              subStatus={subStatus}
              subId={subId}
            />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

function OpenCancelSubModalButton({
  openCancelSubscription,
  openOldCancelSubscription,
  subStatus,
  subId,
}: {
  openCancelSubscription: () => void;
  openOldCancelSubscription: () => void;
  subStatus: string | null;
  subId: string | null;
}) {
  if (subStatus && subId && subId.startsWith("sub_")) {
    return (
      subStatus === "ACTIVE" && (
        <Button
          variant="destructive"
          onClick={openCancelSubscription}
          className={"text-neutral-50"}
        >
          Cancel Subscription
        </Button>
      )
    );
  } else if (subStatus) {
    return (
      subStatus === "ACTIVE" && (
        <Button
          variant="destructive"
          onClick={openOldCancelSubscription}
          className={"text-neutral-50"}
        >
          Cancel Subscription
        </Button>
      )
    );
  }

  return null;
}
