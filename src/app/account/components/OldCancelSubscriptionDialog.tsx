"use client";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import React, { useState } from "react";
import { createClient } from "@/utils/supabase/client";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { cancelSubscriptionPaypal } from "@/app/account/helpers/cancelSubscriptionPaypal";
import { useAccountContext } from "@/app/account/components/AccountContextProvider";

export default function OldCancelSubscriptionDialog() {
  const { openedOldCancelSubscription, closeOldCancelSubscription } =
    useAccountContext();
  return (
    <Dialog
      open={openedOldCancelSubscription}
      onOpenChange={closeOldCancelSubscription}
      modal={true}
    >
      <DialogTitle />
      <DialogContent className="max-w-[780px]! bg-white">
        <p className={"mb-5! text-neutral-800"}>
          Are you sure you want to cancel the subscription?
        </p>
        <p className={"mb-5! text-neutral-800"}>
          Your subscription will be cancelled immediately and you will need to
          re-subscribe to access the features.
        </p>
        <OldCancelButton closeCancelSubscription={closeOldCancelSubscription} />
      </DialogContent>
    </Dialog>
  );
}

function OldCancelButton({
  closeCancelSubscription,
}: {
  closeCancelSubscription: () => void;
}) {
  const supabase = createClient();
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  return (
    <Button
      variant="destructive"
      loading={loading}
      className={"text-neutral-50"}
      onClick={() =>
        cancelSubscriptionPaypal({
          setLoading,
          closeCancelSubscription,
          router,
          supabase,
        })
      }
    >
      Cancel Subscription
    </Button>
  );
}
