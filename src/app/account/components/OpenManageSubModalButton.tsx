"use client";
import { Subscription } from "@/types";
import { But<PERSON> } from "@/components/ui/button";
import React from "react";

export function OpenManageSubModalButton({
  openOldCancelSubscription,
  subStatus,
  subId,
  openManageSubscription,
  subscription,
}: {
  openManageSubscription: () => void;
  openOldCancelSubscription: () => void;
  subStatus: string | null;
  subId: string | null;
  subscription: Subscription;
}) {
  // Paddle
  if (subStatus && subId && subId.startsWith("sub_")) {
    // Don't show manage button if there is a scheduled change
    if (subscription.scheduledChange?.effective_at) {
      return null;
    }
    // Show manage button if there is an active subscription
    return (
      subStatus === "ACTIVE" && (
        <Button
          className="transition-colors duration-100"
          onClick={openManageSubscription}
        >
          Manage Subscription
        </Button>
      )
    );
    // PayPal
  } else if (subStatus && subId?.startsWith("I-")) {
    return (
      subStatus === "ACTIVE" && (
        <Button
          variant="destructive"
          onClick={openOldCancelSubscription}
          className={"text-neutral-50"}
        >
          Cancel Subscription
        </Button>
      )
    );
  }

  return null;
}
