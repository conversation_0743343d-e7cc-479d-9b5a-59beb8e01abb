"use client";
import { SubscriptionStatus } from "@/types";
import { Button } from "@/components/ui/button";
import React from "react";

export function OpenSubModalButton({
  subDate,
  subStatus,
  openSubscription,
  subId,
}: {
  subDate: string | null;
  subStatus: SubscriptionStatus;
  openSubscription: () => void;
  subId: string | null;
}) {
  // Not subscribed
  if (!subStatus) {
    return (
      <Button variant="default" onClick={openSubscription}>
        Subscribe
      </Button>
    );
  }
  // Paddle canceled sub
  if (
    subId &&
    subId.startsWith("sub_") &&
    subStatus &&
    subStatus === "CANCELLED"
  ) {
    return (
      <Button variant="default" onClick={openSubscription}>
        Subscribe
      </Button>
    );
  }
  // PayPal canceled sub
  if (
    subId &&
    !subId.startsWith("sub_") &&
    subStatus &&
    subDate &&
    new Date(subDate) < new Date() &&
    subStatus === "CANCELLED"
  ) {
    return (
      <Button variant="default" onClick={openSubscription}>
        Subscribe
      </Button>
    );
  }
  // PayPal inactive sub
  if (subStatus !== "ACTIVE" && subId && !subId.startsWith("sub_")) {
    return (
      <Button variant="default" onClick={openSubscription}>
        Subscribe
      </Button>
    );
  }
}
