"use client";

import { Subscription, SubscriptionStatus } from "@/types";
import React from "react";

export function SubStatus({
  subStatus,
  subscription,
}: {
  subStatus: SubscriptionStatus;
  subDate: string | null;
  subscription: Subscription;
}) {
  const statusColor = {
    ACTIVE: "text-green-500",
    LIFETIME: "text-green-500",
    PAUSED: "text-orange-500 dark:text-yellow-400",
    CANCELLED: "text-red-500",
    PAST_DUE: "text-red-500",
  };
  const statusText = {
    ACTIVE:
      subscription.subscriptionPlanName.length > 0
        ? subscription.subscriptionPlanName
        : "Active",
    PAUSED: "Paused",
    CANCELLED: "Inactive",
    PAST_DUE: "Past due",
    LIFETIME: "Lifetime",
  };
  if (subStatus) {
    // Paddle subscriptions
    if (
      subscription.id?.startsWith("sub_") ||
      subscription.subscriptionName === "LIFETIME"
    ) {
      return (
        <p>
          Subscription:{" "}
          <span className={` ${statusColor[subStatus]} font-bold`}>
            {statusText[subStatus]}
          </span>
        </p>
      );
    }
    // Paypal subscriptions
    if (subscription.id?.startsWith("I-")) {
      return (
        <p>
          Subscription:{" "}
          <span className={` ${statusColor[subStatus]} font-bold`}>
            {subStatus === "CANCELLED" ? "Inactive" : statusText[subStatus]}
          </span>
        </p>
      );
    }
  }
  // No subscription
  return (
    <p>
      Subscription:{" "}
      <span className={` ${statusColor.CANCELLED}`}>Inactive</span>
    </p>
  );
}
