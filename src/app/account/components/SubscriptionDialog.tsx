"use client";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import priceCards from "@/app/account/priceCards";
import React from "react";
import { useAccountContext } from "@/app/account/components/AccountContextProvider";

export default function SubscriptionDialog() {
  const {
    proPrices,
    creatorPrices,
    openCheckout,
    openedSubscription,
    closeSubscription,
  } = useAccountContext();
  return (
    <Dialog
      open={openedSubscription}
      onOpenChange={closeSubscription}
      modal={true}
    >
      <DialogTitle />
      <DialogContent className="max-w-[780px]! p-0">
        <Tabs defaultValue="1" className="mt-12 w-full">
          <div className={"flex justify-center"}>
            <TabsList className="relative mb-4 flex justify-center gap-2 border-1 bg-transparent">
              <TabsTrigger value="1" className={"min-w-20"}>
                Pro
              </TabsTrigger>
              <TabsTrigger value="2" className={"min-w-20"}>
                Creator
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="1" className="pt-6 pb-10">
            <div className="flex max-w-5xl grow flex-col flex-wrap items-stretch justify-center gap-4 px-4 sm:min-h-[18rem] sm:flex-row">
              {priceCards(proPrices, openCheckout)}
            </div>
          </TabsContent>
          <TabsContent value="2" className="pt-6 pb-10">
            <div className="flex max-w-5xl grow flex-col flex-wrap items-stretch justify-center gap-4 px-4 sm:min-h-[18rem] sm:flex-row">
              {priceCards(creatorPrices, openCheckout)}
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
