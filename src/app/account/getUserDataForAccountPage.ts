import getSubscriptionNameAndStatus from "@/helpers/getSubscriptionNameAndStatus";
import { headers } from "next/headers";
import { SupabaseClient } from "@supabase/supabase-js";
import { Database } from "@/types/supabase";

export async function getUserDataForAccountPage({
  supabase,
}: {
  supabase: SupabaseClient<Database>;
}) {
  let username = "";
  let avatarUrl = "";
  let initialSubStatus:
    | "ACTIVE"
    | "CANCELLED"
    | "PAST_DUE"
    | "PAUSED"
    | "LIFETIME"
    | null = null;
  let initialSubDate = null;
  let initialSubID = null;
  let initialOG = null;
  let avatarPath: string | null = null;
  const subscription = await getSubscriptionNameAndStatus();
  const userIpAddress = (await headers()).get("x-forwarded-for");
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (user) {
    const res = await supabase
      .from("profiles")
      .select(
        "username,avatar_url,full_name, website, subscription_status,subscription_last_payment_date, subscription_ID, OG",
      )
      .eq("id", user?.id);
    const data = res?.data?.at(0);
    if (data) {
      avatarPath = data?.avatar_url;
      username = data?.username ? data.username : "";
      if (data?.avatar_url) {
        if (data?.avatar_url.includes("googleusercontent")) {
          avatarUrl = data?.avatar_url;
        } else {
          const {
            data: { publicUrl },
          } = supabase.storage.from("avatars").getPublicUrl(data?.avatar_url);
          avatarUrl = publicUrl;
        }
      }
      if (subscription.status && data.subscription_ID?.startsWith("sub_")) {
        if (subscription.status === "active") {
          initialSubStatus = "ACTIVE";
        }
        if (subscription.status === "canceled") {
          initialSubStatus = "CANCELLED";
        }
        if (subscription.status === "paused") {
          initialSubStatus = "PAUSED";
        }
        if (subscription.status === "past_due") {
          initialSubStatus = "PAST_DUE";
        }
        if (subscription.lifetime) {
          initialSubStatus = "LIFETIME";
        }
      } else if (
        data.subscription_status === "ACTIVE" ||
        data.subscription_status === "CANCELLED" ||
        data.subscription_status === "PAST_DUE" ||
        data.subscription_status === "PAUSED" ||
        data.subscription_status === "LIFETIME"
      ) {
        initialSubStatus = data?.subscription_status;
      }
      initialSubDate = data?.subscription_last_payment_date;
      initialSubID = data?.subscription_ID;
      initialOG = data.OG;
    }

    if (data && !data.avatar_url) {
      avatarUrl = user.user_metadata.avatar_url;
    }
  }
  return {
    user,
    userIpAddress,
    avatarURL: avatarUrl,
    initialUsername: username,
    currentAvatarPath: avatarPath,
    initialSubStatus,
    initialSubDate,
    initialSubID,
    initialOG,
    subscription,
  };
}
