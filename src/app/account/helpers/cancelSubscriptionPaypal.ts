import { showErrorToast } from "@/lib/toast";
import { AppRouterInstance } from "next/dist/shared/lib/app-router-context.shared-runtime";
import { SupabaseClient } from "@supabase/supabase-js";
import { Database } from "@/types/supabase";

export async function cancelSubscriptionPaypal({
  setLoading,
  closeCancelSubscription,
  router,
  supabase,
}: {
  setLoading: React.Dispatch<React.SetStateAction<boolean>>;
  closeCancelSubscription: () => void;
  router: AppRouterInstance;
  supabase: SupabaseClient<Database>;
}) {
  setLoading(true);
  const user = await supabase.auth.getUser();

  if (user.data && user?.data?.user?.id) {
    const { data: subIDAndLastPayment, error } = await supabase
      .from("profiles")
      .select("subscription_ID, subscription_last_payment_date")
      .eq("id", user.data?.user.id);
    if (error) {
      showErrorToast(`Failed`, {
        description: "Something went wrong please try again.",
      });
    } else if (subIDAndLastPayment) {
      const res = await fetch(`/api/paypal/cancel-subscription`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        body: JSON.stringify({
          subIDAndLastPayment: subIDAndLastPayment,
        }),
      });
      if (!res.ok && res.status !== 200) {
        showErrorToast(`Failed`, {
          description: "Something went wrong please try again.",
        });
      }
    }
  }
  closeCancelSubscription();
  setLoading(false);

  router.refresh();
}
