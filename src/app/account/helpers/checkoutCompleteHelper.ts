import { CheckoutEventsData, PaddleEventData } from "@paddle/paddle-js";
import { showSuccessToast } from "@/lib/toast";
import { AppRouterInstance } from "next/dist/shared/lib/app-router-context.shared-runtime";

export function checkoutCompleteHelper({
  data,
  userId,
  closeSubscription,
  closeManageSubscription,
  router,
}: {
  data: PaddleEventData;
  userId: string | undefined;
  closeSubscription: () => void;
  closeManageSubscription: () => void;
  router: AppRouterInstance;
}) {
  if (data.name === "checkout.completed") {
    const checkoutCompleteData = data.data as CheckoutEventsData & {
      items: {
        price_name?: string;
      }[];
    };
    if (checkoutCompleteData.items[0]?.price_name === "Lifetime") {
      fetch("/api/paddle/successful-subscription", {
        method: "POST",
        headers: {
          "Content-Type": "application/json", // Set the Content-Type header to application/json
        },
        body: JSON.stringify({
          transactionId: data.data?.transaction_id,
          userId: userId,
          priceName: "Lifetime",
        }),
      });
      showSuccessToast("Success", {
        description: `Successfully subscribed.`,
      });
      closeSubscription();
      closeManageSubscription();

      router.refresh();
    } else {
      fetch("/api/paddle/successful-subscription", {
        method: "POST",
        headers: {
          "Content-Type": "application/json", // Set the Content-Type header to application/json
        },
        body: JSON.stringify({
          transactionId: data.data?.transaction_id,
          userId: userId,
        }),
      });
      showSuccessToast("Success", {
        description: `Successfully subscribed.`,
      });
      closeSubscription();
      router.refresh();
    }
  }
}
