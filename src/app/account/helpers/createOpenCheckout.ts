import { CheckoutCustomerAddress, Paddle } from "@paddle/paddle-js";

type OpenCheckoutProps = {
  closeSubscription: () => void;
  user: { email?: string } | null;
  paddle: Paddle | undefined;
};

export function createOpenCheckout({
  closeSubscription,
  user,
  paddle,
}: OpenCheckoutProps) {
  return function openCheckout(priceId: string) {
    closeSubscription();
    const email = user?.email as CheckoutCustomerAddress | undefined;
    paddle?.Checkout.open({
      items: [
        {
          priceId: `${priceId}`,
          quantity: 1,
        },
      ],
      customer: { email: `${email}` },
      settings: { showAddDiscounts: false },
    });
  };
}
