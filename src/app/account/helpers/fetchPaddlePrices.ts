// TODO: Augment Improvement Examples for Account Page
import { Paddle } from "@paddle/paddle-js";
import { MyPaddlePriceArray } from "@/types/paddleTypes";

interface FetchPaddlePricesProps {
  paddleInstance: Paddle;
  OG: boolean | null;
  userIpAddress: string;
  setProductPrices: (prices: MyPaddlePriceArray) => void;
}

export function fetchPaddlePrices({
  paddleInstance,
  OG,
  userIpAddress,
  setProductPrices,
}: FetchPaddlePricesProps) {
  paddleInstance
    ?.PricePreview({
      items: [
        {
          priceId: `${OG ? process.env.NEXT_PUBLIC_PADDLE_PRODUCT_MONTHLY_PRICING_ID : process.env.NEXT_PUBLIC_PADDLE_PRODUCT_MONTHLY_PRICING_ID_NEW}`,
          quantity: 1,
        },
        {
          priceId: `${OG ? process.env.NEXT_PUBLIC_PADDLE_PRODUCT_YEARLY_PRICING_ID : process.env.NEXT_PUBLIC_PADDLE_PRODUCT_YEARLY_PRICING_ID_NEW}`,
          quantity: 1,
        },
        {
          priceId: `${process.env.NEXT_PUBLIC_PADDLE_PRODUCT_MONTHLY_PRICING_ID_CREATOR}`,
          quantity: 1,
        },
        {
          priceId: `${process.env.NEXT_PUBLIC_PADDLE_PRODUCT_YEARLY_PRICING_ID_CREATOR}`,
          quantity: 1,
        },
        {
          priceId: `${process.env.NEXT_PUBLIC_PADDLE_PRODUCT_LIFETIME_PRICING_ID}`,
          quantity: 1,
        },
      ],
      customerIpAddress: `${userIpAddress}`,
    })
    .then((res) => {
      const prices = res.data.details.lineItems.map((i) => {
        const isLifetime = i.price.name === "Lifetime";
        const remaining = isLifetime
          ? Number(i.price.customData?.remaining) > 0
            ? `${i.price.customData?.remaining}`
            : "0"
          : "not-lifetime";
        return {
          productName: i.product.name,
          priceId: i.price.id,
          price: i.formattedTotals.total,
          interval: i.price.billingCycle?.interval
            ? i.price.billingCycle?.interval
            : "",
          featureList: i.price.description.split("\\n"),
          name: `${i.price.name}`,
          remaining: remaining,
          initialLimit: i.price.customData?.initialLimit
            ? `${i.price.customData?.initialLimit}`
            : null,
        };
      });
      setProductPrices(prices);
    });
}
