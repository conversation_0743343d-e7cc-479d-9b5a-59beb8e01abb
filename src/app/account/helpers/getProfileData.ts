import { showErrorToast } from "@/lib/toast";
import { SupabaseClient, User } from "@supabase/supabase-js";
import { Subscription, SubscriptionStatus } from "@/types";

export async function getProfileData({
  user,
  subscription,
  setLoading,
  setUsername,
  setSubStatus,
  setOG,
  setSubId,
  setAvatarUrl,
  setSubDate,
  supabase,
}: {
  user: User | null;
  subscription: Subscription;
  setLoading: (loading: boolean) => void;
  setUsername: (username: string | null) => void;
  setSubStatus: (status: SubscriptionStatus) => void;
  setOG: (og: boolean | null) => void;
  setSubId: (id: string | null) => void;
  setAvatarUrl: (url: string | null) => void;
  setSubDate: (date: string | null) => void;
  supabase: SupabaseClient;
}) {
  try {
    setLoading(true);
    if (user?.id) {
      const { data, error, status } = await supabase
        .from("profiles")
        .select(
          `full_name, username, website, avatar_url, subscription_status,subscription_last_payment_date, subscription_ID, OG`,
        )
        .eq("id", user?.id)
        .single();

      if (error && status !== 406) {
        throw error;
      }

      if (data) {
        if (data.subscription_last_payment_date) {
          const subscriptionExpirationDate = new Date(
            data.subscription_last_payment_date,
          );

          subscriptionExpirationDate.setMonth(
            new Date(data.subscription_last_payment_date).getMonth() +
              (data.subscription_ID?.startsWith("sub_") ? 0 : 1),
          );
          setSubDate(subscriptionExpirationDate.toISOString());
        }

        setUsername(data.username);
        if (subscription.status && data.subscription_ID?.startsWith("sub_")) {
          if (subscription.status === "active") {
            setSubStatus("ACTIVE");
          }
          if (subscription.status === "canceled") {
            setSubStatus("CANCELLED");
          }
          if (subscription.status === "past_due") {
            setSubStatus("PAST_DUE");
          }
          if (subscription.status === "paused") {
            setSubStatus("PAUSED");
          }
          if (subscription.lifetime) {
            setSubStatus("LIFETIME");
          }
        } else if (
          data.subscription_status === "ACTIVE" ||
          data.subscription_status === "CANCELLED" ||
          data.subscription_status === "PAST_DUE" ||
          data.subscription_status === "PAUSED" ||
          data.subscription_status === "LIFETIME"
        ) {
          setSubStatus(data.subscription_status);
        } else {
          setSubStatus(null);
        }
        if (data.OG !== null) {
          setOG(data.OG);
        } else {
          setOG(false);
        }

        setSubId(data.subscription_ID);
        setAvatarUrl(data.avatar_url);
      }
    }
  } catch {
    showErrorToast(`Failed`, {
      description: `Error loading user data! `,
    });
  } finally {
    setLoading(false);
  }
}
