// TODO: Augment Improvement Examples for Account Page
import { PaddleEventData, CheckoutEventsData } from "@paddle/paddle-js";
import { showSuccessToast } from "@/lib/toast";
import { AppRouterInstance } from "next/dist/shared/lib/app-router-context.shared-runtime";

interface HandleCheckoutCompleteProps {
  data: PaddleEventData;
  userId: string | undefined;
  closeSubscription: () => void;
  closeManageSubscription: () => void;
  router: AppRouterInstance;
}

export function handleCheckoutComplete({
  data,
  userId,
  closeSubscription,
  closeManageSubscription,
  router,
}: HandleCheckoutCompleteProps) {
  if (data.name === "checkout.completed") {
    const checkoutCompleteData = data.data as CheckoutEventsData & {
      items: {
        price_name?: string;
      }[];
    };

    if (checkoutCompleteData.items[0]?.price_name === "Lifetime") {
      fetch("/api/paddle/successful-subscription", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          transactionId: data.data?.transaction_id,
          userId: userId,
          priceName: "Lifetime",
        }),
      });

      showSuccessToast("Success", {
        description: `Successfully subscribed.`,
      });

      closeSubscription();
      closeManageSubscription();
      router.refresh();
    } else {
      fetch("/api/paddle/successful-subscription", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          transactionId: data.data?.transaction_id,
          userId: userId,
        }),
      });

      showSuccessToast("Success", {
        description: `Successfully subscribed.`,
      });

      closeSubscription();
      router.refresh();
    }
  }
}
