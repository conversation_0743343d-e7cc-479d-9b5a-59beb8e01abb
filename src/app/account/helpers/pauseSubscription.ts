import { showErrorToast } from "@/lib/toast";
import { SupabaseClient } from "@supabase/supabase-js";
import { Database } from "@/types/supabase";
import { AppRouterInstance } from "next/dist/shared/lib/app-router-context.shared-runtime";

export async function pauseSubscription({
  setLoading,
  closeCancelSubscription,
  closeManageSubscription,
  router,
  supabase,
}: {
  setLoading: React.Dispatch<React.SetStateAction<boolean>>;
  closeCancelSubscription: () => void;
  closeManageSubscription: () => void;
  router: AppRouterInstance;
  supabase: SupabaseClient<Database>;
}) {
  setLoading(true);
  const user = await supabase.auth.getUser();

  if (user.data && user?.data?.user?.id) {
    const { data, error } = await supabase
      .from("profiles")
      .select("subscription_ID")
      .eq("id", user?.data?.user?.id);

    if (error) {
      showErrorToast(`Failed`, {
        description: "Something went wrong please try again.",
      });
    } else if (data) {
      const res = await fetch(`/api/paddle/pause-subscription`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        body: JSON.stringify({
          subscriptionId: data[0].subscription_ID,
        }),
      });
      if (!res.ok && res.status !== 200) {
        showErrorToast(`Failed`, {
          description: "Something went wrong please try again.",
        });
      }
    }
  }
  closeCancelSubscription();
  closeManageSubscription();
  setLoading(false);

  router.refresh();
}
