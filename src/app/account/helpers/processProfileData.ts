// TODO: Augment Improvement Examples for Account Page
import { SubscriptionStatus, Subscription } from "@/types";
import { AccountStateActions } from "../hooks/useAccountState";

interface ProfileData {
  full_name: string | null;
  username: string | null;
  website: string | null;
  avatar_url: string | null;
  subscription_status: string | null;
  subscription_last_payment_date: string | null;
  subscription_ID: string | null;
  OG: boolean | null;
}

interface ProcessProfileDataProps {
  data: ProfileData;
  subscription: Subscription;
  actions: AccountStateActions;
}

export function processProfileData({
  data,
  subscription,
  actions,
}: ProcessProfileDataProps) {
  // Handle subscription date
  if (data.subscription_last_payment_date) {
    const subscriptionExpirationDate = new Date(
      data.subscription_last_payment_date,
    );

    subscriptionExpirationDate.setMonth(
      new Date(data.subscription_last_payment_date).getMonth() +
        (data.subscription_ID?.startsWith("sub_") ? 0 : 1),
    );
    actions.setSubDate(subscriptionExpirationDate.toISOString());
  }

  // Set username
  actions.setUsername(data.username);

  // Handle subscription status
  if (subscription.status && data.subscription_ID?.startsWith("sub_")) {
    if (subscription.status === "active") {
      actions.setSubStatus("ACTIVE");
    }
    if (subscription.status === "canceled") {
      actions.setSubStatus("CANCELLED");
    }
    if (subscription.status === "past_due") {
      actions.setSubStatus("PAST_DUE");
    }
    if (subscription.status === "paused") {
      actions.setSubStatus("PAUSED");
    }
    if (subscription.lifetime) {
      actions.setSubStatus("LIFETIME");
    }
  } else if (
    data.subscription_status === "ACTIVE" ||
    data.subscription_status === "CANCELLED" ||
    data.subscription_status === "PAST_DUE" ||
    data.subscription_status === "PAUSED" ||
    data.subscription_status === "LIFETIME"
  ) {
    actions.setSubStatus(data.subscription_status as SubscriptionStatus);
  } else {
    actions.setSubStatus(null);
  }

  // Handle OG status
  if (data.OG !== null) {
    actions.setOG(data.OG);
  } else {
    actions.setOG(false);
  }

  // Set subscription ID and avatar URL
  actions.setSubId(data.subscription_ID);
  actions.setAvatarUrl(data.avatar_url);
}
