import { updateProfilePicture } from "@/app/account/actions";
import { showErrorToast, showSuccessToast } from "@/lib/toast";

export async function updateAvatar({
  newAvatarURL,
  userId,
  setLoading,
}: {
  newAvatarURL: string;
  userId: string | undefined;
  setLoading: React.Dispatch<React.SetStateAction<boolean>>;
}) {
  setLoading(true);
  if (newAvatarURL && userId) {
    try {
      const res = await updateProfilePicture({ newAvatarURL, userId });
      if (res.status === 200) {
        showSuccessToast("Success", {
          description: `Successfully updated your profile picture.`,
        });
        if (res.data && res.data[0]) {
          return res?.data[0]?.avatar_url;
        }
      }
    } catch (error: Error | unknown) {
      if (error instanceof Error) {
        showErrorToast(`Failed`, {
          description: `${error?.message}`,
        });
      }
    }
  }
  setLoading(false);
}
/*setLoading: React.Dispatch<React.SetStateAction<boolean>> | ((loading: boolean) => void);*/
