// TODO: Augment Improvement Examples for Account Page
import { useState } from "react";
import { SubscriptionStatus } from "@/types";

export interface AccountState {
  loading: boolean;
  username: string | null;
  subStatus: SubscriptionStatus;
  OG: boolean | null;
  subId: string | null;
  avatarUrl: string | null;
  subDate: string | null;
}

export interface AccountStateActions {
  setLoading: (loading: boolean) => void;
  setUsername: (username: string | null) => void;
  setSubStatus: (status: SubscriptionStatus) => void;
  setOG: (og: boolean | null) => void;
  setSubId: (id: string | null) => void;
  setAvatarUrl: (url: string | null) => void;
  setSubDate: (date: string | null) => void;
}

export function useAccountState(
  initialUsername: string | null,
  initialSubStatus: SubscriptionStatus,
  initialOG: boolean | null,
  initialSubID: string | null,
  avatarURL: string,
  initialSubDate: string | null,
): [AccountState, AccountStateActions] {
  const [loading, setLoading] = useState(false);
  const [username, setUsername] = useState<string | null>(initialUsername);
  const [subStatus, setSubStatus] =
    useState<SubscriptionStatus>(initialSubStatus);
  const [OG, setOG] = useState<boolean | null>(initialOG);
  const [subId, setSubId] = useState<string | null>(initialSubID);
  const [avatarUrl, setAvatarUrl] = useState<string | null>(avatarURL);
  const [subDate, setSubDate] = useState<string | null>(initialSubDate);

  const state: AccountState = {
    loading,
    username,
    subStatus,
    OG,
    subId,
    avatarUrl,
    subDate,
  };

  const actions: AccountStateActions = {
    setLoading,
    setUsername,
    setSubStatus,
    setOG,
    setSubId,
    setAvatarUrl,
    setSubDate,
  };

  return [state, actions];
}
