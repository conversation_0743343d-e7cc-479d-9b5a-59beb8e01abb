import { useCallback, useEffect } from "react";
import { initializePaddle, Paddle } from "@paddle/paddle-js";
import { checkoutCompleteHelper } from "@/app/account/helpers/checkoutCompleteHelper";
import { setPricesHelper } from "@/app/account/helpers/getPrices";
import { getProfileData } from "@/app/account/helpers/getProfileData";
import { AppRouterInstance } from "next/dist/shared/lib/app-router-context.shared-runtime";
import { SupabaseClient, User } from "@supabase/supabase-js";
import { Subscription, SubscriptionStatus } from "@/types";
import { Database } from "@/types/supabase";
import { MyPaddlePriceArray } from "@/types/paddleTypes";

export function useInitializeAccountData({
  user,
  userIpAddress,
  OG,
  closeSubscription,
  closeManageSubscription,
  router,
  subscription,
  supabase,
  setLoading,
  setUsername,
  setSubStatus,
  setOG,
  setSubId,
  setAvatarUrl,
  setSubDate,
  paddle,
  setPaddle,
  setProductPrices,
}: {
  user: User | null;
  userIpAddress: string;
  OG: boolean | null;
  closeSubscription: () => void;
  closeManageSubscription: () => void;
  router: AppRouterInstance;
  subscription: Subscription;
  supabase: SupabaseClient<Database>;
  setLoading: React.Dispatch<React.SetStateAction<boolean>>;
  setUsername: React.Dispatch<React.SetStateAction<string | null>>;
  setSubStatus: React.Dispatch<React.SetStateAction<SubscriptionStatus>>;
  setOG: React.Dispatch<React.SetStateAction<boolean | null>>;
  setSubId: React.Dispatch<React.SetStateAction<string | null>>;
  setAvatarUrl: React.Dispatch<React.SetStateAction<string | null>>;
  setSubDate: React.Dispatch<React.SetStateAction<string | null>>;
  paddle: Paddle | undefined;
  setPaddle: React.Dispatch<React.SetStateAction<Paddle | undefined>>;
  setProductPrices: React.Dispatch<React.SetStateAction<MyPaddlePriceArray>>;
}) {
  const checkoutCompleteCallback = useCallback(checkoutCompleteHelper, [
    closeSubscription,
    closeManageSubscription,
    router,
    user?.id,
  ]);

  const getPricesCallback = useCallback(setPricesHelper, [OG, userIpAddress]);

  const getProfile = useCallback(getProfileData, [
    user?.id,
    subscription.lifetime,
    subscription.status,
  ]);

  useEffect(() => {
    getProfile({
      user,
      setAvatarUrl,
      setLoading,
      setOG,
      setSubDate,
      setSubId,
      setSubStatus,
      setUsername,
      supabase,
      subscription,
    });
    supabase
      .channel("profiles")
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "profiles",
          filter: `id=eq.${user?.id}`,
        },
        () => {
          router.refresh();
        },
      )
      .subscribe();
    if (!paddle && OG !== null) {
      const paddleEnv = process.env.NEXT_PUBLIC_PADDLE_ENVIRONMENT as
        | "sandbox"
        | "production";
      initializePaddle({
        environment: paddleEnv,
        token: `${process.env.NEXT_PUBLIC_PADDLE_CLIENT_TOKEN}`,
        eventCallback: (data) => {
          checkoutCompleteCallback({
            data,
            closeSubscription,
            closeManageSubscription,
            router,
            userId: user?.id,
          });
        },

        checkout: {
          settings: {
            theme: window.matchMedia("(prefers-color-scheme: dark)").matches
              ? "dark"
              : "light",
          },
        },
      })
        .then((paddleInstance: Paddle | undefined) => {
          if (paddleInstance) {
            setPaddle(paddleInstance);
            getPricesCallback({
              paddleInstance,
              OG,
              userIpAddress,
              setProductPrices,
            });
          }
        })
        .catch((error) => {
          console.error("Error initializing Paddle:", error);
        });
    }
  }, [
    user,
    getProfile,
    OG,
    paddle,
    checkoutCompleteCallback,
    router,
    getPricesCallback,
  ]);
}
