// TODO: Augment Improvement Examples for Account Page

import { useDisclosure } from "@/hooks/useDisclosure";

export interface ModalDisclosures {
  cancelSubscription: {
    opened: boolean;
    open: () => void;
    close: () => void;
  };
  updateUsernameModal: {
    opened: boolean;
    open: () => void;
    close: () => void;
  };
  manageSubscription: {
    opened: boolean;
    open: () => void;
    close: () => void;
  };
  oldCancelSubscription: {
    opened: boolean;
    open: () => void;
    close: () => void;
  };
  subscription: {
    opened: boolean;
    open: () => void;
    close: () => void;
  };
}

export function useModalDisclosures(): ModalDisclosures {
  const [
    openedCancelSubscription,
    { open: openCancelSubscription, close: closeCancelSubscription },
  ] = useDisclosure(false);

  const [
    openedUpdateUsernameModal,
    { open: openUpdateUsernameModal, close: closeUpdateUsernameModal },
  ] = useDisclosure(false);

  const [
    openedManageSubscription,
    { open: openManageSubscription, close: closeManageSubscription },
  ] = useDisclosure(false);

  const [
    openedOldCancelSubscription,
    { open: openOldCancelSubscription, close: closeOldCancelSubscription },
  ] = useDisclosure(false);

  const [
    openedSubscription,
    { open: openSubscription, close: closeSubscription },
  ] = useDisclosure(false);

  return {
    cancelSubscription: {
      opened: openedCancelSubscription,
      open: openCancelSubscription,
      close: closeCancelSubscription,
    },
    updateUsernameModal: {
      opened: openedUpdateUsernameModal,
      open: openUpdateUsernameModal,
      close: closeUpdateUsernameModal,
    },
    manageSubscription: {
      opened: openedManageSubscription,
      open: openManageSubscription,
      close: closeManageSubscription,
    },
    oldCancelSubscription: {
      opened: openedOldCancelSubscription,
      open: openOldCancelSubscription,
      close: closeOldCancelSubscription,
    },
    subscription: {
      opened: openedSubscription,
      open: openSubscription,
      close: closeSubscription,
    },
  };
}
