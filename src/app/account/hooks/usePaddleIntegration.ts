// TODO: Augment Improvement Examples for Account Page

import { useState, useCallback, useEffect } from "react";
import { User } from "@supabase/supabase-js";
import { initializePaddle, Paddle, PaddleEventData } from "@paddle/paddle-js";
import { MyPaddlePriceArray } from "@/types/paddleTypes";
import { filterPrices } from "@/helpers/filterPrices";
import { handleCheckoutComplete } from "../helpers/handleCheckoutComplete";
import { fetchPaddlePrices } from "../helpers/fetchPaddlePrices";
import { createOpenCheckout } from "../helpers/createOpenCheckout";
import { AppRouterInstance } from "next/dist/shared/lib/app-router-context.shared-runtime";

interface UsePaddleIntegrationProps {
  user: User | null;
  userIpAddress: string;
  OG: boolean | null;
  closeSubscription: () => void;
  closeManageSubscription: () => void;
  router: AppRouterInstance;
}

export function usePaddleIntegration({
  user,
  userIpAddress,
  OG,
  closeSubscription,
  closeManageSubscription,
  router,
}: UsePaddleIntegrationProps) {
  const [paddle, setPaddle] = useState<Paddle>();
  const [productPrices, setProductPrices] = useState<MyPaddlePriceArray>([]);
  const { proPrices, creatorPrices } = filterPrices(productPrices);

  const checkoutCompleteCallback = useCallback(
    (data: PaddleEventData) => {
      handleCheckoutComplete({
        data,
        userId: user?.id,
        closeSubscription,
        closeManageSubscription,
        router,
      });
    },
    [closeSubscription, closeManageSubscription, router, user?.id],
  );

  const getPricesCallback = useCallback(
    (paddleInstance: Paddle) => {
      fetchPaddlePrices({
        paddleInstance,
        OG,
        userIpAddress,
        setProductPrices,
      });
    },
    [OG, userIpAddress],
  );

  useEffect(() => {
    if (!paddle && OG !== null) {
      const paddleEnv = process.env.NEXT_PUBLIC_PADDLE_ENVIRONMENT as
        | "sandbox"
        | "production";

      initializePaddle({
        environment: paddleEnv,
        token: `${process.env.NEXT_PUBLIC_PADDLE_CLIENT_TOKEN}`,
        eventCallback: checkoutCompleteCallback,
        checkout: {
          settings: {
            theme: window.matchMedia("(prefers-color-scheme: dark)").matches
              ? "dark"
              : "light",
          },
        },
      })
        .then((paddleInstance: Paddle | undefined) => {
          if (paddleInstance) {
            setPaddle(paddleInstance);
            getPricesCallback(paddleInstance);
          }
        })
        .catch((error) => {
          console.error("Error initializing Paddle:", error);
        });
    }
  }, [OG, paddle, checkoutCompleteCallback, getPricesCallback]);

  const openCheckout = createOpenCheckout({ user, paddle, closeSubscription });

  return {
    paddle,
    productPrices,
    proPrices,
    creatorPrices,
    openCheckout,
  };
}
