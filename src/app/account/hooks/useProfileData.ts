// TODO: Augment Improvement Examples for Account Page

import { useCallback, useEffect } from "react";
import { User } from "@supabase/supabase-js";
import { createClient } from "@/utils/supabase/client";
import { Subscription } from "@/types";
import { showErrorToast } from "@/lib/toast";
import { AccountStateActions } from "./useAccountState";
import { processProfileData } from "../helpers/processProfileData";
import { AppRouterInstance } from "next/dist/shared/lib/app-router-context.shared-runtime";

const supabase = createClient();

interface UseProfileDataProps {
  user: User | null;
  subscription: Subscription;
  actions: AccountStateActions;
  router: AppRouterInstance;
}

export function useProfileData({
  user,
  subscription,
  actions,
  router,
}: UseProfileDataProps) {
  const getProfile = useCallback(async () => {
    try {
      actions.setLoading(true);
      if (user?.id) {
        const { data, error, status } = await supabase
          .from("profiles")
          .select(
            `full_name, username, website, avatar_url, subscription_status,subscription_last_payment_date, subscription_ID, OG`,
          )
          .eq("id", user?.id)
          .single();

        if (error && status !== 406) {
          throw error;
        }

        if (data) {
          processProfileData({ data, subscription, actions });
        }
      }
    } catch {
      showErrorToast(`Failed`, {
        description: `Error loading user data! `,
      });
    } finally {
      actions.setLoading(false);
    }
  }, [user?.id, actions]);
  // }, [user?.id, subscription, actions]);

  useEffect(() => {
    getProfile();

    // Subscribe to profile changes
    const channel = supabase
      .channel("profiles")
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "profiles",
          filter: `id=eq.${user?.id}`,
        },
        () => {
          router.refresh();
        },
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [
    user?.id,
    subscription.lifetime,
    subscription.status,
    subscription,
    actions,
  ]);
  // }, [user, getProfile, router]);

  return { getProfile };
}
