import AccountForm from "./AccountForm";
import { Metadata } from "next";
import { createClient } from "@/utils/supabase/server";
import PaddleRetain from "@/components/PaddleRetain";
import { permanentRedirect } from "next/navigation";
import { getUserDataForAccountPage } from "@/app/account/getUserDataForAccountPage";
import { AccountContextProvider } from "@/app/account/components/AccountContextProvider";

export const metadata: Metadata = {
  title: "Account - Ultimate TCG Card Maker",
  alternates: {
    canonical: "https://ultimatetcgcm.com/account",
  },
};
export default async function Account() {
  const supabase = await createClient();
  const session = await supabase.auth.getSession();
  if (!session.data.session) {
    permanentRedirect("/login");
    return null;
  }
  const userData = await getUserDataForAccountPage({ supabase });

  return (
    <>
      <AccountContextProvider
        user={userData.user}
        userIpAddress={`${userData.userIpAddress}`}
        avatarURL={userData.avatarURL}
        initialUsername={userData.initialUsername}
        initialSubDate={userData.initialSubDate}
        initialSubStatus={userData.initialSubStatus}
        initialSubID={userData.initialSubID}
        initialOG={userData.initialOG}
        subscription={userData.subscription}
      >
        <AccountForm currentAvatarPath={userData.currentAvatarPath} />
      </AccountContextProvider>

      {process.env.NEXT_PUBLIC_PADDLE_ENVIRONMENT === "production" && (
        <PaddleRetain />
      )}
    </>
  );
}
