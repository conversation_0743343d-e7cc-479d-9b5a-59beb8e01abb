"use client";
import PricingCard from "@/components/PricingCard";
import React from "react";

export default function priceCards(
  prices: Array<{
    productName: string;
    priceId: string;
    price: string;
    interval: string;
    featureList: string[];
    name: string;
    remaining?: string | null;
    initialLimit?: string | null;
  }>,
  openCheckout: (priceId: string) => void,
) {
  return prices.map((price) => {
    if (price.remaining === "0") return null;
    if (price.interval === "year")
      return (
        <PricingCard
          title={price.name}
          className={
            "min-h-62 min-w-fit border-2 border-blue-400 md:max-w-[25rem] md:min-w-[15rem]"
          }
          buttonClassName={"mt-auto bg-blue-400"}
          featureList={price.featureList}
          buttonText={"Subscribe"}
          buttonLink={"/account"}
          key={price.priceId}
          price={price.price}
          recurring={price.interval}
          onClick={function () {
            openCheckout(price.priceId);
          }}
          remaining={
            price.remaining && price.initialLimit
              ? {
                  remaining: price.remaining,
                  limit: price.initialLimit,
                }
              : null
          }
        />
      );
    return (
      <PricingCard
        className={"min-h-62 min-w-fit md:max-w-[25rem] md:min-w-[15rem]"}
        title={price.name}
        featureList={price.featureList}
        buttonText={"Subscribe"}
        buttonLink={"/account"}
        key={price.priceId}
        price={price.price}
        recurring={price.interval}
        onClick={function () {
          openCheckout(price.priceId);
        }}
        remaining={
          price.remaining && price.initialLimit
            ? { remaining: price.remaining, limit: price.initialLimit }
            : null
        }
      />
    );
  });
}
