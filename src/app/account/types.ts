import { MyPaddlePrice } from "@/types/paddleTypes";
import { Subscription, SubscriptionStatus } from "@/types";
import { AppRouterInstance } from "next/dist/shared/lib/app-router-context.shared-runtime";
import { SetStateAction } from "react";
import { User } from "@supabase/supabase-js";

export type AccountContextType = {
  openedCancelSubscription: boolean;
  openCancelSubscription: () => void;
  closeCancelSubscription: () => void;
  openedUpdateUsernameModal: boolean;
  openUpdateUsernameModal: () => void;
  closeUpdateUsernameModal: () => void;
  openedManageSubscription: boolean;
  openManageSubscription: () => void;
  closeManageSubscription: () => void;
  openedOldCancelSubscription: boolean;
  openOldCancelSubscription: () => void;
  closeOldCancelSubscription: () => void;
  openedSubscription: boolean;
  openSubscription: () => void;
  closeSubscription: () => void;
  loading: boolean;
  setLoading: React.Dispatch<SetStateAction<boolean>>;
  username: string | null;
  setUsername: (username: string | null) => void;
  subStatus: SubscriptionStatus;
  setSubStatus: React.Dispatch<SetStateAction<SubscriptionStatus>>;
  OG: boolean | null;
  setOG: (og: boolean | null) => void;
  subId: string | null;
  setSubId: (id: string | null) => void;
  avatarUrl: string | null;
  setAvatarUrl: (url: string | null) => void;
  subDate: string | null;
  setSubDate: (date: string | null) => void;
  proPrices: Array<MyPaddlePrice>;
  creatorPrices: Array<MyPaddlePrice>;
  openCheckout: (priceId: string) => void;
  subscription: Subscription;
  router: AppRouterInstance;
  user: User | null;
};
