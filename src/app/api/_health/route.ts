import { NextResponse } from "next/server";
export const dynamic = "force-static";

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type",
    },
  });
}

export async function GET() {
  return new NextResponse(JSON.stringify({ status: "ok" }), { status: 200 });
}
