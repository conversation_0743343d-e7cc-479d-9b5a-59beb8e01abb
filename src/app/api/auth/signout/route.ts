import { NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";

export async function POST() {
  const supabase = await createClient();

  // Check if we have a session
  const {
    data: { session },
  } = await supabase.auth.getSession();

  if (session) {
    await supabase.auth.signOut();
    return NextResponse.redirect(`${process.env.NEXT_PUBLIC_REDIRECT_URL}/`, {
      status: 302,
    });
  }
  return new NextResponse(
    JSON.stringify({
      error: { message: "Authentication required" },
    }),
    {
      status: 401,
      statusText: "Unauthorized",
      headers: {
        "Content-Type": "application/json",
      },
    },
  );
}
