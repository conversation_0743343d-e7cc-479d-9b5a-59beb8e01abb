import { NextResponse } from "next/server";
import { authenticateApiRequest } from "@/utils/auth/api-auth";

export async function POST(request: Request) {
  const auth = await authenticateApiRequest();

  if (!auth.authenticated) {
    return auth.error;
  }

  const { user, supabase } = auth;

  const { subscriptionId } = (await request.json()) as {
    subscriptionId: string;
  };
  const { data: profile } = await supabase
    .from("profiles")
    .select("subscription_ID")
    .eq("id", user?.id)
    .single();
  if (!profile?.subscription_ID) {
    return new NextResponse(
      JSON.stringify({
        error: { message: "Missing subscription id." },
      }),
      { status: 400 },
    );
  }
  if (profile.subscription_ID !== subscriptionId) {
    return new NextResponse(
      JSON.stringify({
        error: { message: "Unauthorized." },
      }),
      { status: 401 },
    );
  }
  const res = await fetch(
    `${process.env.NEXT_PUBLIC_PADDLE_API_LINK}/subscriptions/${subscriptionId}/cancel`,
    {
      headers: {
        Authorization: `Bearer ${process.env.PADDLE_BEARER_TOKEN}`,
        "Content-Type": "application/json",
      },
      method: "POST",
      body: JSON.stringify({
        effective_from: "next_billing_period",
        resume_at: null,
      }),
    },
  );
  if (res.ok) {
    const {
      data: {
        scheduled_change: { effective_at: effectiveAt },
      },
    } = (await res.json()) as {
      data: { scheduled_change: { action: "cancel"; effective_at: string } };
    };
    await supabase
      .from("profiles")
      .update({
        subscription_status: "CANCELLED",
        subscription_last_payment_date: effectiveAt,
      })
      .eq("id", user?.id)
      .select();

    return new NextResponse(JSON.stringify("Successfully canceled"));
  } else {
    return new NextResponse(
      JSON.stringify({
        error: { message: "Something went wrong." },
      }),
      { status: 400 },
    );
  }
}
