// app/api/subscription/route.ts
import { NextRequest, NextResponse } from "next/server";
import { fetchSubscription } from "@/helpers/getSubscriptionNameAndStatus";
import { authenticateApiRequest } from "@/utils/auth/api-auth";

export async function PATCH(request: NextRequest) {
  const auth = await authenticateApiRequest();

  if (!auth.authenticated) {
    return auth.error;
  }

  const { user, supabase } = auth;

  try {
    const { data: profile } = await supabase
      .from("profiles")
      .select("subscription_ID")
      .eq("id", user?.id)
      .single();
    const { priceId, subscriptionId } = await request.json();
    if (!profile?.subscription_ID) {
      return new NextResponse(
        JSON.stringify({
          error: { message: "Missing subscription id." },
        }),
        { status: 400 },
      );
    }
    if (profile.subscription_ID !== subscriptionId) {
      return new NextResponse(
        JSON.stringify({
          error: { message: "Unauthorized." },
        }),
        { status: 401 },
      );
    }
    let prorationBillingMode = "do_not_bill";

    // Create next billing date starting from tomorrow to avoid any timezone issues
    const nextBilledAt = new Date();

    if (
      priceId ===
        process.env.NEXT_PUBLIC_PADDLE_PRODUCT_MONTHLY_PRICING_ID_CREATOR ||
      priceId ===
        process.env.NEXT_PUBLIC_PADDLE_PRODUCT_MONTHLY_PRICING_ID_NEW ||
      priceId === process.env.NEXT_PUBLIC_PADDLE_PRODUCT_MONTHLY_PRICING_ID
    ) {
      nextBilledAt.setMonth(nextBilledAt.getMonth() + 1);
    } else if (
      priceId === process.env.NEXT_PUBLIC_PADDLE_PRODUCT_YEARLY_PRICING_ID ||
      priceId ===
        process.env.NEXT_PUBLIC_PADDLE_PRODUCT_YEARLY_PRICING_ID_NEW ||
      priceId ===
        process.env.NEXT_PUBLIC_PADDLE_PRODUCT_YEARLY_PRICING_ID_CREATOR
    ) {
      nextBilledAt.setFullYear(nextBilledAt.getFullYear() + 1);
    }

    if (!priceId || !subscriptionId) {
      return NextResponse.json(
        { error: { detail: "Missing priceId or subscriptionId" } },
        { status: 400 },
      );
    }
    const url = `${process.env.NEXT_PUBLIC_PADDLE_API_LINK}/subscriptions/${subscriptionId}`;

    const subscription = await fetchSubscription(subscriptionId);
    const priceName = subscription.data.items[0].price.name;

    if (
      (priceId ===
        process.env.NEXT_PUBLIC_PADDLE_PRODUCT_MONTHLY_PRICING_ID_CREATOR &&
        priceName.includes("Pro 1-month plan")) ||
      (priceId ===
        process.env.NEXT_PUBLIC_PADDLE_PRODUCT_YEARLY_PRICING_ID_CREATOR &&
        priceName.includes("Pro 1-month plan")) ||
      (priceId ===
        process.env.NEXT_PUBLIC_PADDLE_PRODUCT_YEARLY_PRICING_ID_CREATOR &&
        priceName.includes("Pro 1-year plan")) ||
      (priceId ===
        process.env.NEXT_PUBLIC_PADDLE_PRODUCT_MONTHLY_PRICING_ID_CREATOR &&
        priceName.includes("Pro 1-year plan")) ||
      (priceId ===
        process.env.NEXT_PUBLIC_PADDLE_PRODUCT_YEARLY_PRICING_ID_NEW &&
        priceName.includes("Creator 1-month plan")) ||
      (priceId ===
        process.env.NEXT_PUBLIC_PADDLE_PRODUCT_MONTHLY_PRICING_ID_CREATOR &&
        priceName === "1-month plan") ||
      (priceId ===
        process.env.NEXT_PUBLIC_PADDLE_PRODUCT_YEARLY_PRICING_ID_CREATOR &&
        priceName === "1-month plan") ||
      (priceId ===
        process.env.NEXT_PUBLIC_PADDLE_PRODUCT_YEARLY_PRICING_ID_CREATOR &&
        priceName === "1-year plan") ||
      (priceId ===
        process.env.NEXT_PUBLIC_PADDLE_PRODUCT_MONTHLY_PRICING_ID_CREATOR &&
        priceName === "1-year plan")
    ) {
      prorationBillingMode = "prorated_immediately";
      // First update the next billing date
      const dateUpdateResponse = await fetch(url, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${process.env.PADDLE_BEARER_TOKEN}`,
        },
        body: JSON.stringify({
          next_billed_at: nextBilledAt.toISOString(),
          proration_billing_mode: "do_not_bill",
        }),
      });

      if (!dateUpdateResponse.ok) {
        const dateError = await dateUpdateResponse.json();
        return NextResponse.json(
          {
            error: dateError.error || {
              detail: "Failed to update billing date",
            },
          },
          { status: dateUpdateResponse.status },
        );
      }
    }

    // Then update the subscription items
    const itemsUpdateResponse = await fetch(url, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${process.env.PADDLE_BEARER_TOKEN}`,
      },
      body: JSON.stringify({
        proration_billing_mode: prorationBillingMode,
        items: [
          {
            price_id: priceId,
            quantity: 1,
          },
        ],
      }),
    });

    const data = await itemsUpdateResponse.json();

    if (!itemsUpdateResponse.ok) {
      return NextResponse.json(
        {
          error: data.error || {
            detail: "Failed to update subscription items",
          },
        },
        { status: itemsUpdateResponse.status },
      );
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error("Server error modifying subscription:", error);
    return NextResponse.json(
      { error: { detail: "Internal server error" } },
      { status: 500 },
    );
  }
}
