import { NextRequest, NextResponse } from "next/server";
import { authenticateApiRequest } from "@/utils/auth/api-auth";

export async function POST(request: NextRequest) {
  const auth = await authenticateApiRequest();

  if (!auth.authenticated) {
    return auth.error;
  }

  try {
    const body = await request.json();
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_PADDLE_API_LINK}/pricing-preview`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${process.env.PADDLE_BEARER_TOKEN}`,
        },
        body: JSON.stringify(body),
      },
    );

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error("Error fetching pricing preview:", error);
    return NextResponse.json(
      { error: "Failed to fetch pricing preview" },
      { status: 500 },
    );
  }
}
