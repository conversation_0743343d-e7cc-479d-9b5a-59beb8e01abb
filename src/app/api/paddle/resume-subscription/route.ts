import { NextResponse } from "next/server";
import { authenticateApiRequest } from "@/utils/auth/api-auth";

export async function POST(request: Request) {
  const auth = await authenticateApiRequest();

  if (!auth.authenticated) {
    return auth.error;
  }

  const { user, supabase } = auth;

  const { data: profile } = await supabase
    .from("profiles")
    .select("subscription_ID")
    .eq("id", user?.id)
    .single();
  if (!profile?.subscription_ID) {
    return new NextResponse(
      JSON.stringify({
        error: { message: "Missing subscription id." },
      }),
      { status: 400 },
    );
  }
  const { subscriptionId, subDate } = (await request.json()) as {
    subscriptionId: string;
    subDate: string;
  };
  if (profile.subscription_ID !== subscriptionId) {
    return new NextResponse(
      JSON.stringify({
        error: { message: "Unauthorized." },
      }),
      { status: 401 },
    );
  }
  const res = await fetch(
    `${process.env.NEXT_PUBLIC_PADDLE_API_LINK}/subscriptions/${subscriptionId}`,
    {
      headers: { Authorization: `Bearer ${process.env.PADDLE_BEARER_TOKEN}` },
    },
  );
  const data = await res.json();
  const subStatus = data.data.status as "active" | "paused";

  if (subStatus === "active") {
    const {
      data: {
        scheduled_change: { action },
      },
    } = data as {
      data: {
        scheduled_change: { action: "pause" | "cancel"; effective_at: string };
      };
    };
    if (action && (action === "pause" || action === "cancel")) {
      if (action === "pause") {
        if (new Date(subDate) > new Date()) {
          const res = await fetch(
            `${process.env.NEXT_PUBLIC_PADDLE_API_LINK}/subscriptions/${subscriptionId}`,
            {
              headers: {
                Authorization: `Bearer ${process.env.PADDLE_BEARER_TOKEN}`,
                "Content-Type": "application/json",
              },
              method: "PATCH",
              body: JSON.stringify({
                scheduled_change: null,
              }),
            },
          );
          if (res.ok) {
            await supabase
              .from("profiles")
              .update({
                subscription_status: "ACTIVE",
                subscription_last_payment_date: null,
              })
              .eq("id", user?.id)
              .select();

            return new NextResponse(JSON.stringify("Successfully resumed"));
          } else {
            return new NextResponse(
              JSON.stringify({
                error: { message: "Something went wrong." },
              }),
              { status: 400 },
            );
          }
        }
      }
      if (action === "cancel") {
        const res = await fetch(
          `${process.env.NEXT_PUBLIC_PADDLE_API_LINK}/subscriptions/${subscriptionId}`,
          {
            headers: {
              Authorization: `Bearer ${process.env.PADDLE_BEARER_TOKEN}`,
              "Content-Type": "application/json",
            },
            method: "PATCH",
            body: JSON.stringify({
              scheduled_change: null,
            }),
          },
        );
        if (res.ok) {
          await supabase
            .from("profiles")
            .update({
              subscription_status: "ACTIVE",
              subscription_last_payment_date: null,
            })
            .eq("id", user?.id)
            .select();

          return new NextResponse(JSON.stringify("Successfully resumed"));
        } else {
          return new NextResponse(
            JSON.stringify({
              error: { message: "Something went wrong." },
            }),
            { status: 400 },
          );
        }
      }
    }
  } else if (subStatus === "paused") {
    const res = await fetch(
      `${process.env.NEXT_PUBLIC_PADDLE_API_LINK}/subscriptions/${subscriptionId}/resume`,
      {
        headers: {
          Authorization: `Bearer ${process.env.PADDLE_BEARER_TOKEN}`,
          "Content-Type": "application/json",
        },
        method: "POST",
        body: JSON.stringify({
          effective_from: "immediately",
        }),
      },
    );
    if (res.ok) {
      return new NextResponse(JSON.stringify("Successfully resumed"));
    } else {
      return new NextResponse(
        JSON.stringify({
          error: { message: "Something went wrong." },
        }),
        { status: 400 },
      );
    }
  }
}
