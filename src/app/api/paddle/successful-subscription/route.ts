import { NextResponse } from "next/server";
import { authenticateApiRequest } from "@/utils/auth/api-auth";

export async function POST(request: Request) {
  const auth = await authenticateApiRequest();

  if (!auth.authenticated) {
    return auth.error;
  }

  const { supabase } = auth;

  const { transactionId, userId, priceName } = (await request.json()) as {
    transactionId: string;
    userId: string;
    priceName: "Lifetime" | undefined;
  };
  if (!userId || !transactionId) {
    return new NextResponse(
      JSON.stringify({
        error: { message: "Something went wrong subscription" },
      }),
      { status: 400 },
    );
  }
  const { data, error } = await supabase
    .from("profiles")
    .select(`subscription_ID`)
    .eq("id", userId)
    .single();
  if (error) {
    return new NextResponse(
      JSON.stringify({
        error: { message: "Something went wrong subscription" },
      }),
      { status: 400 },
    );
  }
  const txn_res = await fetch(
    `${process.env.NEXT_PUBLIC_PADDLE_API_LINK}/transactions/${transactionId}`,
    { headers: { Authorization: `Bearer ${process.env.PADDLE_BEARER_TOKEN}` } },
  );
  if (!txn_res.ok) {
    return new NextResponse(
      JSON.stringify({
        error: { message: "Something went wrong subscription" },
      }),
      { status: 400 },
    );
  }

  if (priceName === "Lifetime") {
    if (transactionId && userId) {
      const lifetimePriceRes = await fetch(
        `${process.env.NEXT_PUBLIC_PADDLE_API_LINK}/prices/${process.env.NEXT_PUBLIC_PADDLE_PRODUCT_LIFETIME_PRICING_ID}`,
        {
          headers: {
            Authorization: `Bearer ${process.env.PADDLE_BEARER_TOKEN}`,
          },
        },
      );
      const lifetimePriceData = await lifetimePriceRes.json();
      await fetch(
        `${process.env.NEXT_PUBLIC_PADDLE_API_LINK}/prices/${process.env.NEXT_PUBLIC_PADDLE_PRODUCT_LIFETIME_PRICING_ID}`,
        {
          body: JSON.stringify({
            custom_data: {
              initialLimit: 100,
              remaining: lifetimePriceData?.data?.custom_data?.remaining - 1,
            },
          }),
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${process.env.PADDLE_BEARER_TOKEN}`,
          },
        },
      );

      await supabase
        .from("profiles")
        .update({
          subscription_status: "LIFETIME",
        })
        .eq("id", userId)
        .select();
      await supabase
        .from("profiles")
        .update({
          paddle_transaction_ID: transactionId,
        })
        .eq("id", userId)
        .select();

      if (data?.subscription_ID) {
        const res = await fetch(
          `${process.env.NEXT_PUBLIC_PADDLE_API_LINK}/subscriptions/${data.subscription_ID}/cancel`,
          {
            body: JSON.stringify({
              effective_from: "immediately",
            }),
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${process.env.PADDLE_BEARER_TOKEN}`,
            },
          },
        );
        if (!res.ok) {
          return new NextResponse(
            JSON.stringify({
              error: { message: "Something went wrong subscription" },
            }),
            { status: 400 },
          );
        }
      }
      return new NextResponse(JSON.stringify("Successfully subscription"));
    } else {
      return new NextResponse(
        JSON.stringify({
          error: { message: "Something went wrong subscription" },
        }),
        { status: 400 },
      );
    }
  } else {
    if (transactionId && userId) {
      await supabase
        .from("profiles")
        .update({
          paddle_transaction_ID: transactionId,
        })
        .eq("id", userId)
        .select();
      await supabase
        .from("profiles")
        .update({
          subscription_status: "ACTIVE",
        })
        .eq("id", userId)
        .select();

      return new NextResponse(JSON.stringify("Successfully subscription"));
    } else {
      return new NextResponse(
        JSON.stringify({
          error: { message: "Something went wrong subscription" },
        }),
        { status: 400 },
      );
    }
  }
}
