import { NextRequest, NextResponse } from "next/server";
import { PaddleTransaction } from "@/types";
import { headers } from "next/headers";
import { EventName, Paddle } from "@paddle/paddle-node-sdk";
import { createAdminClient } from "@/utils/supabase/adminClient";
import { getUserData } from "@/helpers/getUserData";
const paddle = new Paddle(process.env.PADDLE_BEARER_TOKEN || "");
export async function POST(request: NextRequest) {
  const header = await headers();
  const signature = header.get("paddle-signature");
  const rawRequestBody = await request?.text();
  const secretKey = process.env.PADDLE_WEBHOOK_SECRET_KEY || "";
  try {
    if (signature && rawRequestBody) {
      // The `unmarshal` function will validate the integrity of the webhook and return an entity

      const eventData = await paddle.webhooks.unmarshal(
        rawRequestBody,
        secretKey,
        signature,
      );
      const isValid = await paddle.webhooks.isSignatureValid(
        rawRequestBody,
        secretKey,
        signature,
      );
      if (!isValid) {
        return NextResponse.json({ received: false });
      }
      const supabase = createAdminClient();

      const reqData = await JSON.parse(rawRequestBody);
      let transactionID: undefined | string = undefined;
      let subscriptionID: undefined | string = undefined;
      let userData = null;
      const effectiveAt = reqData?.data?.scheduled_change?.effective_at;
      const scheduledChange = reqData?.data?.scheduled_change;
      const scheduledAction = reqData?.data?.scheduled_change?.action;
      const pausedAt = reqData?.data?.paused_at;
      const canceledAt = reqData?.data?.canceled_at;

      if (eventData.eventType === EventName.TransactionCompleted) {
        subscriptionID = reqData?.data?.subscription_id;
        transactionID = reqData?.data?.id;
      } else {
        subscriptionID = reqData?.data?.id;
        transactionID = reqData?.data?.transaction_id;
      }
      // TRANSACTION COMPLETED
      /*      if (eventData.eventType === EventName.TransactionCompleted) {
        userData = await getUserData({
          supabase,
          transactionID,
          eventType: "transaction",
        });
      }*/
      if (eventData.eventType === EventName.SubscriptionCreated) {
        userData = await getUserData({
          supabase,
          transactionID,
          eventType: EventName.SubscriptionCreated,
        });
      } else if (
        eventData.eventType === EventName.SubscriptionUpdated ||
        eventData.eventType === EventName.SubscriptionCanceled ||
        eventData.eventType === EventName.SubscriptionResumed
      ) {
        userData = await getUserData({
          supabase,
          subscriptionID,
          eventType: "subscription",
        });
      }
      switch (eventData.eventType) {
        case EventName.SubscriptionCreated:
          if (
            userData &&
            userData.eventType === "subscription.created" &&
            userData.subscriptionID
          ) {
            if (userData.subscriptionID !== subscriptionID) {
              await supabase
                .from("profiles")
                .update({
                  subscription_ID: subscriptionID,
                })
                .eq("paddle_transaction_ID", transactionID)
                .select();
            }
          } else {
            return new NextResponse("Transaction id not found", {
              status: 500,
            });
          }

          break;
        //   TRANSACTION COMPLETED
        /*        case EventName.TransactionCompleted:
          if (
            userData &&
            userData.eventType === "transaction" &&
            userData.subscriptionID
          ) {
            if (userData.subscriptionID !== subscriptionID) {
              await supabase
                .from("profiles")
                .update({
                  subscription_ID: subscriptionID,
                })
                .eq("paddle_transaction_ID", transactionID)
                .select();
            }
          }
          break;*/
        case EventName.AddressCreated:
          const action = reqData.data.action;
          const refundSubId = reqData.data.subscription_id;
          if (action === "refund" && refundSubId) {
            await fetch(
              `${process.env.NEXT_PUBLIC_PADDLE_API_LINK}/subscriptions/${refundSubId}/cancel`,
              {
                headers: {
                  Authorization: `Bearer ${process.env.PADDLE_BEARER_TOKEN}`,
                  "Content-Type": "application/json",
                },
                method: "POST",
                body: JSON.stringify({
                  effective_from: "immediately",
                  resume_at: null,
                }),
              },
            );
          }

          if (action === "refund" && transactionID) {
            const transaction = await fetchTransaction(transactionID);
            if (transaction.data.items[0].price.name === "Lifetime") {
              await supabase
                .from("profiles")
                .update({
                  subscription_status: null,
                  subscription_last_payment_date: null,
                  subscription_ID: null,
                  paddle_transaction_ID: null,
                })
                .eq("paddle_transaction_ID", transactionID)
                .select();
            }
          }
          // console.log("CHANGE:", reqData.data);
          break;
        case EventName.SubscriptionUpdated:
          if (userData) {
            const dbSubscriptionLastPaymentDate =
              userData.subscriptionLastPaymentDate;
            const dbSubscriptionStatus = userData.subscriptionStatus;

            if (scheduledChange) {
              if (!dbSubscriptionLastPaymentDate) {
                if (
                  scheduledAction === "pause" &&
                  dbSubscriptionStatus !== "PAUSED"
                ) {
                  await supabase
                    .from("profiles")
                    .update({
                      subscription_status: "PAUSED",
                      subscription_last_payment_date: effectiveAt,
                    })
                    .eq("subscription_ID", subscriptionID)
                    .select();
                }
                if (
                  scheduledAction === "cancel" &&
                  dbSubscriptionStatus !== "CANCELLED"
                ) {
                  if (dbSubscriptionStatus !== "LIFETIME") {
                    await supabase
                      .from("profiles")
                      .update({
                        subscription_status: "CANCELLED",
                        subscription_last_payment_date: effectiveAt,
                      })
                      .eq("subscription_ID", subscriptionID)
                      .select();
                  }
                }
              }
            } else {
              if (!scheduledAction && reqData?.data?.status === "active") {
                await supabase
                  .from("profiles")
                  .update({
                    subscription_status: "ACTIVE",
                    subscription_last_payment_date: null,
                  })
                  .eq("subscription_ID", subscriptionID)
                  .select();
              }
            }
          }
          break;
        case EventName.SubscriptionPaused:
          await supabase
            .from("profiles")
            .update({
              subscription_status: "PAUSED",
              subscription_last_payment_date: pausedAt,
            })
            .eq("subscription_ID", subscriptionID)
            .select();
          break;
        case EventName.SubscriptionResumed:
          if (userData) {
            const dbSubStatus = userData.subscriptionStatus;

            if (dbSubStatus !== "ACTIVE") {
              await supabase
                .from("profiles")
                .update({
                  subscription_status: "ACTIVE",
                  subscription_last_payment_date: null,
                })
                .eq("subscription_ID", subscriptionID)
                .select();
            }
          }

          break;
        case EventName.SubscriptionPastDue:
          // Instead of immediately canceling, you could:
          await supabase
            .from("profiles")
            .update({
              subscription_status: "PAST_DUE",
              subscription_last_payment_date: canceledAt,
            })
            .eq("subscription_ID", subscriptionID)
            .select();
          // 1. Log the past due event
          console.log(`Subscription ${subscriptionID} is past due`);

          // 2. You might want to notify the customer
          // await sendPastDueNotification(customer.email);
          break;

        case EventName.SubscriptionCanceled:
          if (userData) {
            if (userData.subscriptionStatus !== "LIFETIME") {
              await supabase
                .from("profiles")
                .update({
                  subscription_status: "CANCELLED",
                  subscription_last_payment_date: canceledAt,
                })
                .eq("subscription_ID", subscriptionID)
                .select();
            }
          }
      }

      // Return a 200 response to acknowledge receipt of the event
      return NextResponse.json({ received: true });
    } else {
      return new NextResponse("Signature missing in header", { status: 400 });
      console.log("Signature missing in header");
    }
  } catch (e) {
    // Handle signature mismatch or other runtime errors
    console.error(e);
  }
}

async function fetchTransaction(transactionID: string) {
  try {
    const res = await fetch(
      `${process.env.NEXT_PUBLIC_PADDLE_API_LINK}/transactions/${transactionID}`,
      {
        headers: {
          Authorization: `Bearer ${process.env.PADDLE_BEARER_TOKEN}`,
        },
      },
    );
    if (res.status === 404) {
      throw new Error("Subscription not found");
    }
    if (!res.ok) {
      throw new Error(`Failed to fetch transaction: ${res.statusText}`);
    }

    return (await res.json()) as PaddleTransaction;
  } catch (error) {
    console.error("Error fetching transaction:", error);
    throw error;
  }
}
