import { NextRequest, NextResponse } from "next/server";
import { authenticateApiRequest } from "@/utils/auth/api-auth";

const { NEXT_PUBLIC_PAYPAL_CLIENT_ID, PAYPAL_CLIENT_SECRET } = process.env;
const base = process.env.NEXT_PUBLIC_PAYPAL_API_BASE;

// You can move these functions to a separate file if you prefer
const generateAccessToken = async () => {
  try {
    if (!NEXT_PUBLIC_PAYPAL_CLIENT_ID || !PAYPAL_CLIENT_SECRET) {
      throw new Error("MISSING_API_CREDENTIALS");
    }
    const auth = Buffer.from(
      NEXT_PUBLIC_PAYPAL_CLIENT_ID + ":" + PAYPAL_CLIENT_SECRET,
    ).toString("base64");
    const response = await fetch(`${base}/v1/oauth2/token`, {
      method: "POST",
      body: "grant_type=client_credentials",
      headers: {
        Authorization: `Basic ${auth}`,
      },
    });

    const data = await response.json();
    // console.log("ACCess tok", data.access_token);
    return data.access_token;
  } catch (error) {
    console.error("Failed to generate Access Token:", error);
  }
};

// API Route Handler
export async function POST(request: NextRequest) {
  const auth = await authenticateApiRequest();

  if (!auth.authenticated) {
    return auth.error;
  }

  const { supabase } = auth;

  const data = await request.json();
  const accessToken = await generateAccessToken();

  if (data.subIDAndLastPayment[0]) {
    const res = await fetch(
      `${base}/v1/billing/subscriptions/${data.subIDAndLastPayment[0].subscription_ID}/cancel`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify({
          reason: "Canceling subscription",
        }),
      },
    );

    if (res.status === 204) {
      await supabase
        .from("profiles")
        .update({
          subscription_status: "CANCELLED",
        })
        .eq("subscription_ID", data.subIDAndLastPayment[0].subscription_ID)
        .select();

      const date = new Date(
        data.subIDAndLastPayment[0].subscription_last_payment_date,
      );

      await supabase
        .from("profiles")
        .update({
          subscription_last_payment_date: date.toISOString(),
        })
        .eq("subscription_ID", data.subIDAndLastPayment[0].subscription_ID)
        .select();
    }
    return new NextResponse(JSON.stringify(res));
  }
}
