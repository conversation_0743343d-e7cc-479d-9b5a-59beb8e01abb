import { NextRequest, NextResponse } from "next/server";
import { createAdminClient } from "@/utils/supabase/adminClient";

/*let data = {};
export async function GET(request: NextRequest) {
  return NextResponse.json({
    status: 200,
    data,
  });
}*/
export async function POST(request: NextRequest) {
  const req = await request.json();
  const supabase = createAdminClient();
  // sub activated
  /*req.resource.id;
  req.resource.status;
  req.resource.start_time;
  req.resource.billing_info.next_billing_time;*/

  if (req.event_type === "BILLING.SUBSCRIPTION.ACTIVATED" && req.resource.id) {
    await supabase
      .from("profiles")
      .update({
        subscription_status: req.resource.status,
      })
      .eq("subscription_ID", req.resource.id)
      .select();

    await supabase
      .from("profiles")
      .update({
        pro: true,
      })
      .eq("subscription_ID", req.resource.id)
      .select();

    const date = new Date(req.resource.start_time);

    await supabase
      .from("profiles")
      .update({
        subscription_last_payment_date: date.toISOString(),
      })
      .eq("subscription_ID", req.resource.id)
      .select();
  }
  if (req.event_type === "BILLING.SUBSCRIPTION.CANCELLED" && req.resource.id) {
    await supabase
      .from("profiles")
      .update({
        subscription_status: "CANCELLED",
      })
      .eq("subscription_ID", req.resource.id)
      .select();

    if (req.resource.start_time) {
      const date = new Date(req.resource.start_time);

      await supabase
        .from("profiles")
        .update({
          subscription_last_payment_date: date.toISOString(),
        })
        .eq("subscription_ID", req.resource.id)
        .select();
    }
  }
  if (
    req.event_type === "BILLING.SUBSCRIPTION.PAYMENT.FAILED" &&
    req.resource.id
  ) {
    await supabase
      .from("profiles")
      .update({
        subscription_status: req.resource.status,
      })
      .eq("subscription_ID", req.resource.id)
      .select();

    await supabase
      .from("profiles")
      .update({
        pro: false,
      })
      .eq("subscription_ID", req.resource.id)
      .select();
  }

  if (req.event_type === "BILLING.SUBSCRIPTION.EXPIRED" && req.resource.id) {
    await supabase
      .from("profiles")
      .update({
        subscription_status: req.resource.status,
      })
      .eq("subscription_ID", req.resource.id)
      .select();

    await supabase
      .from("profiles")
      .update({
        pro: false,
      })
      .eq("subscription_ID", req.resource.id)
      .select();
  }
  if (req.event_type === "BILLING.SUBSCRIPTION.SUSPENDED" && req.resource.id) {
    await supabase
      .from("profiles")
      .update({
        subscription_status: req.resource.status,
      })
      .eq("subscription_ID", req.resource.id)
      .select();

    await supabase
      .from("profiles")
      .update({
        pro: false,
      })
      .eq("subscription_ID", req.resource.id)
      .select();
  }

  if (
    req.event_type === "BILLING.SUBSCRIPTION.RE-ACTIVATED" &&
    req.resource.id
  ) {
    await supabase
      .from("profiles")
      .update({
        subscription_status: "ACTIVE",
      })
      .eq("subscription_ID", req.resource.id)
      .select();

    await supabase
      .from("profiles")
      .update({
        pro: true,
      })
      .eq("subscription_ID", req.resource.id)
      .select();

    await supabase
      .from("profiles")
      .update({
        subscription_last_payment_date: req.resource.start_time,
      })
      .eq("subscription_ID", req.resource.id)
      .select();
  }

  return NextResponse.json({
    status: 200,
    req,
    message: "Data has been updated",
  });
}
