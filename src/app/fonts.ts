// import {
//   Commissioner,
//   <PERSON><PERSON><PERSON>,
//   <PERSON><PERSON>,
//   <PERSON>o_Condensed,
//   <PERSON><PERSON>_Mono,
//   <PERSON><PERSON>_<PERSON>,
//   <PERSON><PERSON>_Franklin,
//   <PERSON><PERSON><PERSON>,
//   <PERSON><PERSON><PERSON>_<PERSON>ar,
//   Wix_<PERSON>for_Display,
//   <PERSON><PERSON>,
//   Alumni_Sans,
//   Geologica,
//   Open_Sans,
// } from "next/font/google";
// import localFont from "next/font/local";
//
// export const balboaPlus = localFont({
//   src: "./BalboaPlus-W00-Inline.woff2",
//   display: "swap",
// });
// export const balboaPlusFill = localFont({
//   src: "./balboaplus-fill-webfont.woff2",
//   display: "swap",
// });
//
// export const geistSans = localFont({
//   src: "./Geist/GeistVariableVF.ttf",
//   display: "swap",
// });
// export const One_Piece = localFont({
//   src: "./OnePieceTcg-Regular.ttf",
//   display: "swap",
// });
// export const One_Piece_Italic = localFont({
//   src: "./One-piece-italic.ttf",
//   display: "swap",
// });
// export const One_Piece_Italic_Bold = localFont({
//   src: "./One-piece-italic.ttf",
//   display: "swap",
// });
// export const One_Piece_Rarity = localFont({
//   src: "./Nimbus-Sans-TW01.ttf",
//   display: "swap",
// });
// export const Character_Power = localFont({
//   src: "./OnePieceTcg_power-Regular.ttf",
//   display: "swap",
// });
// export const Character_Power_10k = localFont({
//   src: "./Character_power_10k-Regular.ttf",
//   display: "swap",
// });
// export const Leader_Power = localFont({
//   src: "./Leader_power-Regular.ttf",
//   display: "swap",
// });
// export const roboto = Roboto({
//   weight: ["100", "300", "400", "500", "700", "900"],
//   subsets: ["latin"],
// });
// export const geologica = Geologica({
//   weight: ["600", "100", "300", "400", "500", "700", "900"],
//   subsets: ["latin"],
// });
// export const wix = Wix_Madefor_Display({
//   weight: ["400", "500", "700"],
//   subsets: ["latin"],
// });
// export const mohave = Alumni_Sans({
//   weight: ["300", "400", "500", "600", "700"],
//   subsets: ["latin"],
// });
// export const hindSiliguri = Hind_Siliguri({
//   weight: ["300", "400", "500", "700"],
//   subsets: ["latin"],
// });
//
// export const libreFranklin = Libre_Franklin({
//   weight: ["300", "400", "500", "600", "700"],
//   subsets: ["latin"],
// });
// export const PIXymbols = localFont({
//   src: "./PIXymbols-fixed.woff2",
//   display: "swap",
// });
// export const robotoMono = Roboto_Mono({
//   weight: ["300", "400", "500", "700"],
//   subsets: ["latin"],
// });
// export const robotoCondensed = Roboto_Condensed({
//   weight: ["300", "400", "700"],
//   subsets: ["latin"],
// });
//
// export const commissioner = Commissioner({
//   weight: ["100", "200", "300", "400", "500", "600", "700", "800", "900"],
//   subsets: ["latin"],
// });
//
// export const mukta = Mukta_Malar({
//   weight: ["400", "500", "600", "700", "800"],
//   subsets: ["latin"],
// });
// export const poppins = Poppins({
//   weight: ["400", "500", "600", "700", "800", "900"],
//   subsets: ["latin"],
// });
// export const openSans = Open_Sans({
//   weight: ["400", "500", "600", "700", "800"],
//   subsets: ["latin"],
// });
