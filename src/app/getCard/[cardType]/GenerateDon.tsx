"use client";
import CardBackground from "@/components/CardElement-components/card-elements/CardBackground";
import CardBorder from "@/components/CardElement-components/card-elements/CardBorder";

import CardArtistText from "@/components/CardElement-components/card-elements/CardArtistText";
import CardMadeWith from "@/components/CardElement-components/card-elements/CardMadeWith";
import Card from "@/components/CardElement-components/Card";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import CardKind from "@/components/CardElement-components/card-elements/CardKind";
import CardDonText from "@/components/CardElement-components/card-elements/CardDonText";
import CardAbilityDon from "@/components/CardElement-components/card-elements/CardAbilityDon";
import React from "react";

export default function GenerateDon({ CMW }: { CMW: boolean }) {
  const printReady = useGetStoreState("printReady");
  return (
    <Card maxWPx={printReady ? 3677 : 3357}>
      <CardBackground cardType={"don"} quality={100} png={true} />
      <img
        className={
          "card-background-image absolute top-0 z-[-1] h-full w-full object-cover"
        }
        alt={""}
      />
      <img
        className={
          "cardScreenshot absolute top-0 z-0 h-full w-full object-cover"
        }
        alt={""}
      />
      <CardBorder cardType={"don"} quality={100} png={true} />
      <CardKind cardType={"don"} />
      <CardDonText />
      <CardAbilityDon />
      <CardArtistText cardType={"don"} />
      {CMW && <CardMadeWith cardType={"don"} />}
    </Card>
  );
}
