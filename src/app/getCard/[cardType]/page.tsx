"use client";
// TODO: continue refactoring from getCard.
import { use, useEffect } from "react";

import { useDispatch } from "react-redux";

import { onSetDefaultStore } from "@/store/formSlice";

import GenerateCharacter from "@/app/getCard/[cardType]/GenerateCharacter";
import GenerateLeader from "@/app/getCard/[cardType]/GenerateLeader";
import GenerateEvent from "@/app/getCard/[cardType]/GenerateEvent";
import GenerateStage from "@/app/getCard/[cardType]/GenerateStage";
import GenerateDon from "@/app/getCard/[cardType]/GenerateDon";
import { GetCardState } from "@/types";
import { permanentRedirect } from "next/navigation";

export default function Page(props: {
  params: Promise<{
    cardType: "character" | "leader" | "event" | "stage" | "don";
  }>;
  searchParams: Promise<GetCardState>;
}) {
  const searchParams = use(props.searchParams);
  const params = use(props.params);
  if (
    !searchParams.uuid ||
    searchParams.uuid !== "9f84f1c0-1b57-4de7-942e-83e1ccf5c1b0"
  ) {
    permanentRedirect("/");
  }
  const dispatch = useDispatch();
  const subscriptionNotActive = searchParams?.cardSubscriptionStatus !== "true";
  const softwareAcceleration = searchParams?.softwareAcceleration === "true";
  const cfWorker = searchParams?.cfWorker === "true";

  useEffect(() => {
    dispatch(onSetDefaultStore(searchParams));
  }, [dispatch, searchParams]);
  return (
    <div className={"get-card-screenshot relative"}>
      {params.cardType === "character" && (
        <GenerateCharacter
          CMW={subscriptionNotActive}
          softwareAcceleration={softwareAcceleration}
          cfWorker={cfWorker}
        />
      )}
      {params.cardType === "leader" && (
        <GenerateLeader
          CMW={subscriptionNotActive}
          softwareAcceleration={softwareAcceleration}
          cfWorker={cfWorker}
        />
      )}
      {params.cardType === "event" && (
        <GenerateEvent
          CMW={subscriptionNotActive}
          softwareAcceleration={softwareAcceleration}
          cfWorker={cfWorker}
        />
      )}
      {params.cardType === "stage" && (
        <GenerateStage
          CMW={subscriptionNotActive}
          softwareAcceleration={softwareAcceleration}
          cfWorker={cfWorker}
        />
      )}
      {params.cardType === "don" && <GenerateDon CMW={subscriptionNotActive} />}
    </div>
  );
}
