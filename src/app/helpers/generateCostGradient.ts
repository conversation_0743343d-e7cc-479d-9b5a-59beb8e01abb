import { Color } from "@/types";
import { getCardColor } from "./getCardColor";

/**
 * Generates a CSS gradient for card costs based on the number of colors
 * @param colors Array of colors
 * @param isRainbow Whether to use the rainbow style
 * @returns CSS gradient string
 */
export function generateCostGradient(
  colors: Color[],
  isRainbow: boolean = false,
  eb02 = { border: false, other: false },
): string {
  if (!Array.isArray(colors) || colors.length === 0) {
    // Default to red if no colors
    const defaultColor = getCardColor("red");
    return `linear-gradient(135deg, ${defaultColor}, ${defaultColor})`;
  }

  // Convert colors to hex values
  const colorHexValues = colors.map((color) =>
    getCardColor(
      color,
      false,
      false,
      false,
      false,
      false,
      false,
      undefined,
      eb02,
    ),
  );

  // For a single color, return a solid gradient
  if (colorHexValues.length === 1) {
    return `linear-gradient(135deg, ${colorHexValues[0]}, ${colorHexValues[0]})`;
  }

  // For 2 colors, use the current 50/50 split
  if (colorHexValues.length === 2) {
    return `linear-gradient(135deg, ${colorHexValues[0]} 50%, ${colorHexValues[1]} 50%)`;
  }

  // For rainbow, always use the predefined rainbow gradient regardless of selected colors
  if (isRainbow) {
    // Use the predefined rainbow gradient
    return `
      conic-gradient(
        #B31E1F 0% 16.17%,
        black 16.17% 16.67%,
        #258E6A 16.67% 33.17%,
        black 33.17% 33.67%,
        #2082BB 33.67% 49.17%,
        black 49.17% 49.67%,
        #7F3C85 49.67% 66.17%,
        black 66.17% 66.67%,
        #272424 66.67% 83.17%,
        black 83.17% 83.67%,
        #FFEE33 83.67% 99.17%,
        black 99.17% 100%
      )
    `;
  }

  // For 3+ colors, create a conic gradient with the selected colors
  const segments = colorHexValues.length;
  const segmentSize = 100 / segments;
  const blackDividerSize = 0.5; // Size of black divider between colors

  const gradientStops: string[] = [];

  colorHexValues.forEach((color, index) => {
    const startPercent = index * segmentSize;
    const endPercent = (index + 1) * segmentSize - blackDividerSize;

    // Add the color segment
    gradientStops.push(`${color} ${startPercent}% ${endPercent}%`);

    // Add a black divider between colors (except after the last color)
    if (index < colorHexValues.length - 1) {
      gradientStops.push(`black ${endPercent}% ${(index + 1) * segmentSize}%`);
    } else {
      // For the last segment, add a black divider that wraps around to the beginning
      gradientStops.push(`black ${endPercent}% 100%`);
    }
  });

  return `conic-gradient(${gradientStops.join(", ")})`;
}
