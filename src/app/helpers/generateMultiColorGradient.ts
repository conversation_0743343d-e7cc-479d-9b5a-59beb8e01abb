import { Color } from "@/types";
import { getCardColor } from "./getCardColor";

/**
 * Generates a CSS gradient string based on the number of colors
 * @param colors Array of colors
 * @param useBackgroundColors Whether to convert colors to background colors
 * @returns CSS linear gradient string
 */
export function generateMultiColorGradient(
  colors: Color[],
  useBackgroundColors: boolean = false,
  eb02 = { border: false, other: false },
): string {
  if (!Array.isArray(colors) || colors.length === 0) {
    // Default to red if no colors
    const defaultColor = getCardColor(
      "red",
      false,
      false,
      false,
      false,
      useBackgroundColors,
    );
    return `linear-gradient(to right, ${defaultColor}, ${defaultColor})`;
  }

  // Convert colors to hex values
  const colorHexValues = colors.map((color) =>
    getCardColor(
      color,
      false,
      false,
      false,
      false,
      useBackgroundColors,
      false,
      undefined,
      eb02,
    ),
  );

  // For a single color, return a solid gradient
  if (colorHexValues.length === 1) {
    return `linear-gradient(to right, ${colorHexValues[0]}, ${colorHexValues[0]})`;
  }

  // For 2 colors, create a sharp 50/50 split without blending
  if (colorHexValues.length === 2) {
    return `linear-gradient(to right, ${colorHexValues[0]} 50%, ${colorHexValues[1]} 50%)`;
  }

  // For 3 colors, create a striped pattern with clear divisions (|||) for backgrounds
  // or a blended gradient for borders
  if (colorHexValues.length === 3) {
    // For backgrounds, use non-blended stripes
    if (useBackgroundColors) {
      return `linear-gradient(to right,
        ${colorHexValues[0]} 0%, ${colorHexValues[0]} 33.33%,
        ${colorHexValues[1]} 33.33%, ${colorHexValues[1]} 66.66%,
        ${colorHexValues[2]} 66.66%, ${colorHexValues[2]} 100%)`;
    }
    // For borders, use blended gradient similar to 2 colors
    else {
      return `linear-gradient(to right,
        ${colorHexValues[0]} 0%, ${colorHexValues[1]} 50%, ${colorHexValues[2]} 100%)`;
    }
  }

  // For more than 3 colors, use rainbow-style gradient (top-left to bottom-right)
  // Similar to the rainbow border
  return `linear-gradient(135deg, ${colorHexValues
    .map((color, index) => {
      const percentage = (index / (colorHexValues.length - 1)) * 100;
      return `${color} ${percentage}%`;
    })
    .join(", ")})`;
}
