import { Color } from "@/types";
import { getCardColor } from "./getCardColor";

/**
 * Generates a pattern gradient for card borders with multiple colors
 * @param colors Array of colors
 * @returns CSS gradient string for pattern
 */
export function generatePatternGradient(colors: Color[]): string {
  if (!Array.isArray(colors) || colors.length === 0) {
    // Default to red if no colors
    const defaultColor = getCardColor("red");
    return `linear-gradient(to right, ${defaultColor}, ${defaultColor})`;
  }

  // Convert colors to hex values
  const colorHexValues = colors.map((color) => getCardColor(color));

  // For 1 or 2 colors, use the existing pattern logic
  if (colorHexValues.length <= 2) {
    const cardColor = colorHexValues[0];
    const cardColor2 = colorHexValues[colorHexValues.length - 1];

    return `linear-gradient(to right, ${cardColor} 0% 45%,
      transparent 70% 100%), linear-gradient(to right, transparent 0% 35%, ${cardColor2} 55% 100%)`;
  }

  // For 3+ colors, create a smooth overlapping gradient
  // Create a gradient with overlapping colors for smoother transitions
  if (colorHexValues.length >= 3) {
    // For 3 colors, create a specific pattern with overlapping sections
    if (colorHexValues.length === 3) {
      // Create a gradient with more overlap between first and second colors
      return `linear-gradient(to right,
        ${colorHexValues[0]} 0%, ${colorHexValues[0]} 40%,
        ${colorHexValues[1]} 45%, ${colorHexValues[1]} 75%,
        ${colorHexValues[2]} 60%, ${colorHexValues[2]} 100%)`;
    }

    // For more than 3 colors, distribute them evenly with overlap
    const stops = [];
    for (let i = 0; i < colorHexValues.length; i++) {
      const color = colorHexValues[i];
      const position = (i / (colorHexValues.length - 1)) * 100;

      // Calculate position and overlap for each color
      const totalColors = colorHexValues.length;
      const segmentSize = 100 / (totalColors - 1);
      const overlap = segmentSize * 0.4; // 40% overlap for smoother transitions

      // Calculate start and end positions with overlap
      const start = Math.max(0, position - overlap);
      const end = Math.min(100, position + overlap);

      if (i === 0) {
        // First color - start at 0% but extend further to overlap with second color
        stops.push(`${color} 0%`);
        stops.push(`${color} ${end}%`);
      } else if (i === totalColors - 1) {
        // Last color - end at 100% but start earlier to overlap with second-to-last color
        stops.push(`${color} ${start}%`);
        stops.push(`${color} 100%`);
      } else {
        // Middle colors - ensure overlap with adjacent colors
        stops.push(`${color} ${start}%`);
        stops.push(`${color} ${end}%`);
      }
    }

    return `linear-gradient(to right, ${stops.join(", ")})`;
  }

  // Fallback to a simple gradient
  return `linear-gradient(to right, ${colorHexValues.join(", ")})`;
}
