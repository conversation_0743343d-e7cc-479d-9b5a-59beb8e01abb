import {
  CHARACTER_COLORS,
  CHARACTER_COLORS_SP,
  EVENT_COLORS_EB02,
  EventColorKey,
} from "@/components/CardElement-components/card-elements/characterBorderTypes";
import { CharacterColor } from "@/types";

export function getCardColor(
  color: string,
  sp = false,
  sec = false,
  sp2 = false,
  power = false,
  bg = false,
  counter = false,
  colorArray?: string[],
  eb02 = { border: false, other: false },
) {
  if (eb02.border) {
    return EVENT_COLORS_EB02[color as CharacterColor];
  }
  if (eb02.other) {
    const key = (color + "Dim") as EventColorKey;
    return EVENT_COLORS_EB02[key];
  }
  if (colorArray && colorArray?.length > 1 && !sp) {
    return "#000";
  }
  if (counter && colorArray && colorArray?.length > 1) {
    return "#000";
  }
  if (bg && color === "black") {
    return "#595959";
  }
  if (sp || (sp2 && counter)) {
    const shadowColorStandard = CHARACTER_COLORS_SP[color as CharacterColor];
    const shadowColorYellow = CHARACTER_COLORS_SP["yellowText"];
    const shadowColor =
      color !== "yellow" ? shadowColorStandard : shadowColorYellow;
    return shadowColor;
  }
  if (sec && color === "yellow") {
    return "#000";
  }
  if (sp2 && power) {
    return "#fff";
  }
  if (sp2) {
    return "#000";
  }
  return CHARACTER_COLORS[color as CharacterColor];
}
/*
if (cardType === true) {
  if (sp) {
    return CHARACTER_COLORS_SP[color as CharacterColor];
  }
  return CHARACTER_COLORS[color as CharacterColor];
} else {
  if (sp) {
    const shadowColorStandard = CHARACTER_COLORS_SP[color as CharacterColor];
    const shadowColorYellow = CHARACTER_COLORS_SP["yellowText"];
    const shadowColor =
      color !== "yellow" ? shadowColorStandard : shadowColorYellow;
    return shadowColor;
  }
}
return CHARACTER_COLORS[color as CharacterColor];*/
