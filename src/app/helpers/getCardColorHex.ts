import {
  CHARACTER_COLORS,
  CHARACTER_COLORS_SP,
  EVENT_COLORS_EB02,
  EventColorKey,
  HexColor,
} from "@/components/CardElement-components/card-elements/characterBorderTypes";
import { CharacterColor } from "@/types";

interface GetCardColorSVGParams {
  color: string;
  sp?: boolean;
  sec?: boolean;
  sp2?: boolean;
  power?: boolean;
  bg?: boolean;
  counter?: boolean;
  colorArray?: string[];
  eb02?: { border?: boolean; other?: boolean };
  op10?: { cost?: boolean };
}

export function getCardColorHex({
  color,
  sp = false,
  sec = false,
  sp2 = false,
  power = false,
  bg = false,
  counter = false,
  colorArray,
  eb02 = { border: false, other: false },
  op10 = { cost: false },
}: GetCardColorSVGParams): HexColor {
  if (eb02.border) {
    return EVENT_COLORS_EB02[color as CharacterColor];
  }
  if (eb02.other) {
    const key = (color + "Dim") as EventColorKey;
    return EVENT_COLORS_EB02[key];
  }
  if (op10.cost) {
    const key = (color +
      (color === "yellow" ? "CostBackground" : "Dim")) as EventColorKey;
    return EVENT_COLORS_EB02[key];
  }
  if (colorArray && colorArray?.length > 1 && !sp) {
    return "#000";
  }
  if (counter && colorArray && colorArray?.length > 1) {
    return "#000";
  }
  if (bg && color === "black") {
    return "#595959";
  }
  if (sp || (sp2 && counter)) {
    const shadowColorStandard = CHARACTER_COLORS_SP[color as CharacterColor];
    const shadowColorYellow = CHARACTER_COLORS_SP["yellowText"];

    return color !== "yellow" ? shadowColorStandard : shadowColorYellow;
  }
  if (sec && color === "yellow") {
    return "#000";
  }
  if (sp2 && power) {
    return "#fff";
  }
  if (sp2) {
    return "#000";
  }
  return CHARACTER_COLORS[color as CharacterColor];
}
