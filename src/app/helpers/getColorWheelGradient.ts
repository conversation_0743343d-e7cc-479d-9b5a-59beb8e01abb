import { Color } from "@/types";
import { getCardColor } from "./getCardColor";

/**
 * Generates a custom gradient for the color wheel based on the number of colors
 * @param colors Array of colors
 * @returns CSS gradient string
 */
export function getColorWheelGradient(
  colors: Color[],
  eb02 = {
    border: false,
    other: false,
  },
): string {
  if (!Array.isArray(colors) || colors.length === 0) {
    // Default to red if no colors
    const defaultColor = getCardColor("red");
    return `linear-gradient(135deg, ${defaultColor}, ${defaultColor})`;
  }

  // For 1-2 colors, use the first color
  if (colors.length <= 3) {
    const firstColor = getCardColor(
      colors[0],
      false,
      false,
      false,
      false,
      false,
      false,
      undefined,
      eb02,
    );
    return `linear-gradient(135deg, ${firstColor}, ${firstColor})`;
  }

  // For 3 colors: mix first and second color
  // First color from 0% to 85%, second color from 90% to 100%
  // if (colors.length === 3) {
  //   const color1 = getCardColor(colors[0]);
  //
  //   const color2 = getCardColor(colors[1]);
  //   return `linear-gradient(90deg, ${color1} 0%, ${color1} 30%, ${color1} 70%, ${color2} 100%)`;
  // }

  // For 4 colors: mix second and third colors
  // Second color from 0% to 10%, third color from 15% to 100%
  if (colors.length === 4) {
    const color2 = getCardColor(
      colors[1],
      false,
      false,
      false,
      false,
      false,
      false,
      undefined,
      eb02,
    );
    const color3 = getCardColor(
      colors[2],
      false,
      false,
      false,
      false,
      false,
      false,
      undefined,
      eb02,
    );
    return `linear-gradient(135deg, ${color2} 0%, ${color2} 10%, ${color3} 15%, ${color3} 100%)`;
  }

  // For 5 colors: mix third and fourth colors
  // Third color from 0% to 40%, fourth color from 45% to 100%
  if (colors.length === 5) {
    const color3 = getCardColor(
      colors[2],
      false,
      false,
      false,
      false,
      false,
      false,
      undefined,
      eb02,
    );
    const color4 = getCardColor(
      colors[3],
      false,
      false,
      false,
      false,
      false,
      false,
      undefined,
      eb02,
    );
    return `linear-gradient(135deg, ${color3} 0%, ${color3} 30%, ${color4} 80%, ${color4} 100%)`;
  }

  // For 6+ colors: mix third, fourth, and fifth colors
  // Third color from 0% to 35%, fourth color from 40% to 90%, fifth color from 93% to 100%
  const color3 = getCardColor(
    colors[2],
    false,
    false,
    false,
    false,
    false,
    false,
    undefined,
    eb02,
  );
  const color4 = getCardColor(
    colors[3],
    false,
    false,
    false,
    false,
    false,
    false,
    undefined,
    eb02,
  );
  const color5 = getCardColor(
    colors[4],
    false,
    false,
    false,
    false,
    false,
    false,
    undefined,
    eb02,
  );
  return `linear-gradient(135deg, ${color3} 0%, ${color3} 5%, ${color4} 60%, ${color4} 90%, ${color5} 93%, ${color5} 100%)`;
}
