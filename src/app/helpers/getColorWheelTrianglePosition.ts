import { CharacterColor } from "@/types";

export function getColorWheelTrianglePosition(color: CharacterColor) {
  if (color.includes("red")) {
    return "top-right";
  }
  if (color.includes("green")) {
    return "right";
  }
  if (color.includes("blue")) {
    return "bottom-right";
  }
  if (color.includes("purple")) {
    return "bottom-left";
  }
  if (color.includes("black")) {
    return "left";
  }
  if (color.includes("yellow")) {
    return "top-left";
  }
}
