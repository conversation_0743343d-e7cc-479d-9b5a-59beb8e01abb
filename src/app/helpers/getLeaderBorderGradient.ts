import { BlendMode, Color, LeaderBorder } from "@/types";
import { getCardColor } from "@/app/helpers/getCardColor";

export default function getLeaderBorderGradient(
  color: Color,
  color2: Color,
  leaderBorder: LeaderBorder,
  cardType: "character" | "leader" | "event" | "stage" | "don",
) {
  const colors = [color, color2];
  let backgroundGradient = "";
  let backgroundBlendMode: BlendMode = color === color2 ? "" : "screen";
  const cardColor = getCardColor(color);
  const cardColor2 = getCardColor(color2);
  let patternGradient = `linear-gradient(to right ,${colors.includes("blue") && colors.includes("green") && leaderBorder === "AA-white" ? cardColor2 : cardColor}  0% 45%,
   transparent 70% 100%), linear-gradient(to right,transparent 0% 35% ,${colors.includes("blue") && colors.includes("green") && leaderBorder === "AA-white" ? cardColor : cardColor2}  55% 100%)`;
  if (cardType === "leader") {
    if (leaderBorder === "standard" || leaderBorder === "AA-white") {
      if (color === color2) {
        backgroundGradient = `linear-gradient(to right ,${cardColor}  ,  ${cardColor2})`;
      }
      // backgroundGradient = `linear-gradient(to right ,${cardColor}  0% 45%, transparent 75% 100%), linear-gradient(to right,transparent 0% 15% ,${cardColor2}  55% 100%)`;
      if (colors.includes("red") && colors.includes("green")) {
        backgroundGradient = `linear-gradient(to right ,${cardColor}  0% 45%, transparent 75% 100%), linear-gradient(to right,transparent 0% 20% ,${cardColor2}  55% 100%)`;
      }
      if (colors.includes("black") && !colors.includes("yellow")) {
        if (colors[0] === "black") {
          backgroundGradient = `linear-gradient(to right ,${cardColor}  0% 70%,  transparent 80% 100%),
         linear-gradient(to right,transparent 0% 45%  ,${cardColor2}  70% 100%),
          linear-gradient(to right,transparent 0% 30%  ,rgba(255,255,255, 0.25)  55% 50%,transparent 69% 100%  )`;
        } else {
          backgroundGradient = `linear-gradient(to right ,${cardColor}  0% 37%,  transparent 55% 100%),
         linear-gradient(to right,transparent 0% 20%  ,${cardColor2}  37% 100%),
          linear-gradient(to right,transparent 0% 20%  ,rgba(255,255,255, 0.25)  50% 50%,transparent 74% 100%  )`;
        }
      }
      if (colors.includes("red") && colors.includes("blue")) {
        backgroundGradient = `linear-gradient(to right ,${cardColor}  0% 42%,  transparent 60% 100%),
           linear-gradient(to right,transparent 0% 30%  ,${cardColor2}  42% 100%),
           linear-gradient(to right,transparent 0% 20%  ,rgba(255,255,255, 0.30)  35% 50%,transparent 83% 100%  )`;
        backgroundBlendMode = "hue";
      }

      if (colors.includes("red") && colors.includes("purple")) {
        if (colors[0] === "purple") {
          backgroundGradient = `linear-gradient(to right ,${cardColor}  0% 52%,  transparent 58% 62%),
           linear-gradient(to right,transparent 0% 40%  ,${cardColor2}  52% 100%),
           linear-gradient(to right,transparent 0% 20%  ,rgba(255,255,255, 0.2)  35% 65%,transparent 83% 100%  )`;
          backgroundBlendMode = "lighten";
        } else {
          backgroundGradient = `linear-gradient(to right ,${cardColor}  0% 55%,  transparent 70% 100%),
           linear-gradient(to right,transparent 0% 40%  ,${cardColor2}  55% 100%),
           linear-gradient(to right,transparent 0% 20%  ,rgba(255,255,255, 0.2)  35% 65%,transparent 83% 100%  )`;
          backgroundBlendMode = "lighten";
        }
      }
      if (colors.includes("yellow") && !colors.includes("black")) {
        backgroundGradient = `linear-gradient(to right ,${cardColor}  0% 60%,  transparent 70% 100%),
           linear-gradient(to right,transparent 0% 37%  ,${cardColor2}  60% 100%),
           linear-gradient(to right,transparent 0% 20% ,rgba(255,255,255, 0.1) 28% ,rgba(255,255,255, 0.3)  40% 50%,transparent 83% 100%  )`;
      }
      if (
        color === "yellow" &&
        (colors.includes("green") || colors.includes("blue"))
      ) {
        backgroundGradient = `linear-gradient(to right ,${cardColor}  0% 45%,  transparent 70% 100%),
           linear-gradient(to right,transparent 0% 37%  ,${cardColor2}  45% 100%),
           linear-gradient(to right,transparent 0% 20% ,rgba(255,255,255, 0.1) 28% ,rgba(255,255,255, 0.3)  40% 50%,transparent 83% 100%  )`;
      }
      if (
        color === "yellow" &&
        (colors.includes("purple") || colors.includes("red"))
      ) {
        backgroundGradient = `linear-gradient(to right ,${cardColor}  0% 45%,  transparent 65% 100%),
           linear-gradient(to right,transparent 0% 37%  ,${cardColor2}  47% 100%),
           linear-gradient(to right,transparent 0% 20% ,rgba(255,255,255, 0.1) 28% ,rgba(255,255,255, 0.3)  40% 50%,transparent 83% 100%  )`;
      }

      if (colors.includes("green") && colors.includes("blue")) {
        if (colors[0] === "green") {
          backgroundGradient = `linear-gradient(to right ,${cardColor}  0% 65%,  transparent 60% 100%),
        linear-gradient(to right,transparent 0% 30%  ,${cardColor2}  65% 100%),
        linear-gradient(to right,transparent 0% 20%  ,rgba(255,255,255, 0.25)  40% 50%,transparent 83% 100%  )`;
          backgroundBlendMode = "lighten";
        } else {
          backgroundGradient = `linear-gradient(to right ,${cardColor}  0% 40%,  transparent 60% 100%),
           linear-gradient(to right,transparent 0% 30%  ,${cardColor2}  40% 100%),
           linear-gradient(to right,transparent 0% 20%  ,rgba(255,255,255, 0.25)  40% 50%,transparent 83% 100%  )`;
          backgroundBlendMode = "lighten";
        }
      }
      if (colors.includes("green") && colors.includes("purple")) {
        backgroundGradient = `linear-gradient(to right ,${cardColor}  0% 50%,  transparent 60% 100%),
         linear-gradient(to right,transparent 0% 20%  ,${cardColor2}  50% 100%),
          linear-gradient(to right,transparent 0% 35%  ,rgba(255,255,255, 0.15)  55% 65%,transparent 83% 100%  )`;
      }
      if (colors.includes("blue") && colors.includes("purple")) {
        backgroundGradient = `linear-gradient(to right ,${cardColor}  0% 45%,  transparent 60% 100%),
         linear-gradient(to right,transparent 0% 30%  ,${cardColor2}  45% 100%),
          linear-gradient(to right,transparent 0% 40%  ,rgba(255,255,255, 0.15)  55% 60%,transparent 83% 100%  )`;
        backgroundBlendMode = "lighten";
      }
      if (colors.includes("black") && colors.includes("yellow")) {
        if (colors[0] === "black") {
          backgroundGradient = `linear-gradient(to right ,${cardColor}  0% 70%,  transparent 80% 100%),
         linear-gradient(to right,transparent 0% 50%  ,${cardColor2}  65% 100%),
          linear-gradient(to right,transparent 0% 30%  ,rgba(255,255,255, 0.25)  55% 50%,transparent 69% 100%  )`;
        } else {
          backgroundGradient = `linear-gradient(to right ,${cardColor}  0% 37%,  transparent 50% 100%),
         linear-gradient(to right,transparent 0% 20%  ,${cardColor2}  37% 100%),
          linear-gradient(to right,transparent 0% 20%  ,rgba(255,255,255, 0.25)  55% 50%,transparent 70% 100%  )`;
        }
      }
    }

    if (leaderBorder === "AA-black-and-white" || leaderBorder === "AA-black") {
      patternGradient = `linear-gradient(to right ,${cardColor}  0% 45%,
   transparent 70% 100%), linear-gradient(to right,transparent 0% 35% ,${cardColor2}  55% 100%)`;
      backgroundBlendMode = "darken";
    }
    if (
      leaderBorder === "AA-black-and-white" ||
      leaderBorder === "standard-white"
    ) {
      backgroundGradient = "linear-gradient(to right,#fff ,#fff)";
    }
    if (leaderBorder === "rainbow") {
      backgroundGradient =
        "linear-gradient(135.8deg,#7E1B23 5% ,#9B7E50 13%, #6E8D6A 18%, #2B81AE 35% 35%, #62456F 55% 67%, #383838 72% 76%, #1F1C19 78% 78%, #383532 80%, #9D9051 90%)";
    }
  }
  return [backgroundGradient, backgroundBlendMode, patternGradient];
}
