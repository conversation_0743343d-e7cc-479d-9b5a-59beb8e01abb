import { BlendMode, Color, LeaderBorder, ColorObj } from "@/types";
import { getCardColorHex } from "@/app/helpers/getCardColorHex";

export default function getLeaderBorderGradientSVG(
  color: Color,
  color2: Color,
  leaderBorder: LeaderBorder,
  cardType: "character" | "leader" | "event" | "stage" | "don",
): [ColorObj[][], BlendMode, ColorObj[][]] {
  const colors = [color, color2];
  let backgroundGradient: ColorObj[][] = [];
  let backgroundBlendMode: BlendMode = color === color2 ? "" : "screen";
  const cardColor = getCardColorHex({ color });
  const cardColor2 = getCardColorHex({ color: color2 });
  let patternGradient: ColorObj[][] = [
    [
      {
        color:
          colors.includes("blue") &&
          colors.includes("green") &&
          leaderBorder === "AA-white"
            ? cardColor2
            : cardColor,
        stops: ["0%", "45%"],
      },
      {
        color: "transparent",
        stops: ["70%", "100%"],
      },
    ],
    [
      {
        color: "transparent",
        stops: ["0%", "35%"],
      },
      {
        color:
          colors.includes("blue") &&
          colors.includes("green") &&
          leaderBorder === "AA-white"
            ? cardColor
            : cardColor2,
        stops: [colors[0] === colors[1] ? "0%" : "55%", "100%"],
      },
    ],
  ];
  if (cardType === "leader") {
    if (leaderBorder === "standard" || leaderBorder === "AA-white") {
      if (color === color2) {
        backgroundGradient = [[{ color: cardColor, stops: ["0%", "100%"] }]];
      }
      if (colors.includes("red") && colors.includes("green")) {
        backgroundGradient = [
          [
            { color: cardColor, stops: ["0%", "45%"] },
            { color: "transparent", stops: ["75%", "100%"] },
          ],
          [
            { color: "transparent", stops: ["0%", "15%"] },
            { color: cardColor2, stops: ["50%", "100%"] },
          ],
        ];
        // mix-blend-mode: exclusion;

        // mix-blend-mode: luminosity;

        // mix-blend-mode: plus-darker;

        // mix-blend-mode: plus-lighter;
        backgroundBlendMode = "screen";
      }
      if (colors.includes("black") && !colors.includes("yellow")) {
        if (colors[0] !== colors[1]) {
          backgroundGradient = [
            [
              { color: cardColor, stops: ["0%", "60%"] },
              { color: "transparent", stops: ["70%", "100%"] },
            ],
            [
              { color: "transparent", stops: ["0%", "37%"] },
              { color: cardColor2, stops: ["60%", "100%"] },
            ],
            [
              { color: "transparent", stops: ["0%", "0%"] },
              { color: "rgba(255,255,255, 0.25)", stops: ["43%"] },
              { color: "rgba(255,255,255, 0.15)", stops: ["65%"] },
              { color: "transparent", stops: ["100%"] },
            ],
          ];
        }
        backgroundBlendMode = "screen";
      }
      if (colors.includes("red") && colors.includes("blue")) {
        backgroundGradient = [
          [
            { color: cardColor, stops: ["0%", "45%"] },
            { color: "transparent", stops: ["90%", "100%"] },
          ],
          [
            { color: "transparent", stops: ["0%", "30%"] },
            { color: cardColor2, stops: ["70%", "100%"] },
          ],
          [
            { color: cardColor, stops: ["0%", "25%"] },
            { color: "rgba(220,220,220, 0.5)", stops: ["50%", "50%"] },
            { color: cardColor2, stops: ["75%", "100%"] },
          ],
        ];
        backgroundBlendMode = "screen";
      }

      if (colors.includes("red") && colors.includes("purple")) {
        backgroundGradient = [
          [
            { color: cardColor, stops: ["0%", "60%"] },
            { color: "transparent", stops: ["70%", "100%"] },
          ],
          [
            { color: "transparent", stops: ["0%", "37%"] },
            { color: cardColor2, stops: ["60%", "100%"] },
          ],
          [
            { color: "transparent", stops: ["0%", "0%"] },
            { color: "rgba(255,255,255, 0.3)", stops: ["50%"] },
            { color: "rgba(255,255,255, 0.2)", stops: ["65%"] },
            { color: "transparent", stops: ["100%"] },
          ],
        ];
        backgroundBlendMode = "screen";
      }
      if (colors.includes("yellow") && !colors.includes("black")) {
        if (colors[0] === "blue") {
          backgroundGradient = [
            [
              { color: cardColor, stops: ["0%", "60%"] },
              { color: "transparent", stops: ["70%", "100%"] },
            ],
            [
              { color: "transparent", stops: ["0%", "37%"] },
              { color: cardColor2, stops: ["60%", "100%"] },
            ],
            [
              { color: "transparent", stops: ["0%", "20%"] },
              { color: "rgba(255,255,255, 0.3)", stops: ["40%"] },
              { color: "rgba(255,255,255, 0.5)", stops: ["50%", "50%"] },
              { color: cardColor2, stops: ["70%", "100%"] },
            ],
          ];
        } else {
          if (colors[0] !== colors[1]) {
            backgroundGradient = [
              [
                { color: cardColor, stops: ["0%", "60%"] },
                { color: "transparent", stops: ["70%", "100%"] },
              ],
              [
                { color: "transparent", stops: ["0%", "37%"] },
                { color: cardColor2, stops: ["60%", "100%"] },
              ],
              [
                { color: "transparent", stops: ["0%", "20%"] },
                { color: "rgba(255,255,255, 0.1)", stops: ["28%"] },
                { color: "rgba(255,255,255, 0.25)", stops: ["40%", "50%"] },
                { color: "transparent", stops: ["78%", "100%"] },
              ],
            ];
          }
        }
      }
      if (
        color === "yellow" &&
        (colors.includes("green") || colors.includes("blue"))
      ) {
        backgroundGradient = [
          [
            { color: cardColor, stops: ["0%", "60%"] },
            { color: "transparent", stops: ["70%", "100%"] },
          ],
          [
            { color: "transparent", stops: ["0%", "37%"] },
            { color: cardColor2, stops: ["60%", "100%"] },
          ],
          [
            { color: "transparent", stops: ["0%", "20%"] },
            // { color: "rgba(255,255,255, 0.3)", stops: ["40%"] },
            { color: "rgba(255,255,255, 0.4)", stops: ["50%", "50%"] },
            { color: cardColor2, stops: ["70%"] },
          ],
        ];
      }
      if (
        color === "yellow" &&
        (colors.includes("purple") || colors.includes("red"))
      ) {
        backgroundGradient = [
          [
            { color: cardColor, stops: ["0%", "60%"] },
            { color: "transparent", stops: ["70%", "100%"] },
          ],
          [
            { color: "transparent", stops: ["0%", "37%"] },
            { color: cardColor2, stops: ["60%", "100%"] },
          ],
          [
            { color: "transparent", stops: ["0%", "20%"] },
            // { color: "rgba(255,255,255, 0.3)", stops: ["40%"] },
            { color: "rgba(255,255,255, 0.4)", stops: ["50%", "50%"] },
            { color: cardColor2, stops: ["80%"] },
          ],
        ];
      }

      if (colors.includes("green") && colors.includes("blue")) {
        backgroundGradient = [
          [
            { color: cardColor, stops: ["0%", "65%"] },
            { color: "transparent", stops: ["60%", "100%"] },
          ],
          [
            { color: "transparent", stops: ["0%", "40%"] },
            { color: cardColor2, stops: ["55%", "100%"] },
          ],
          [
            { color: "transparent", stops: ["0%", "0%"] },
            { color: "rgba(255,255,255, 0.3)", stops: ["40%", "50%"] },
            { color: "rgba(255,255,255, 0.3)", stops: ["50%", "60%"] },
            { color: "transparent", stops: ["83%", "100%"] },
          ],
        ];
        backgroundBlendMode = "screen";
      }
      if (colors.includes("green") && colors.includes("purple")) {
        backgroundGradient = [
          [
            { color: cardColor, stops: ["0%", "65%"] },
            { color: "transparent", stops: ["60%", "100%"] },
          ],
          [
            { color: "transparent", stops: ["0%", "40%"] },
            { color: cardColor2, stops: ["55%", "100%"] },
          ],
          [
            { color: "transparent", stops: ["0%", "0%"] },
            { color: "rgba(255,255,255, 0.3)", stops: ["45%", "50%"] },
            { color: "rgba(255,255,255, 0.3)", stops: ["50%", "0%"] },
            { color: "transparent", stops: ["100%", "100%"] },
          ],
        ];
        backgroundBlendMode = "screen";
      }
      if (colors.includes("blue") && colors.includes("purple")) {
        backgroundGradient = [
          [
            { color: cardColor, stops: ["0%", "60%"] },
            { color: "transparent", stops: ["70%", "100%"] },
          ],
          [
            { color: "transparent", stops: ["0%", "37%"] },
            { color: cardColor2, stops: ["60%", "100%"] },
          ],
          [
            { color: "transparent", stops: ["0%", "20%"] },
            { color: "rgba(255,255,255, 0.1)", stops: ["40%"] },
            { color: "rgba(255,255,255, 0.2)", stops: ["50%", "50%"] },
            { color: "rgba(255,255,255, 0.1)", stops: ["60%", "77%"] },
            { color: "transparent", stops: ["100%"] },
          ],
        ];
        backgroundBlendMode = "screen";
      }
      if (colors.includes("black") && colors.includes("yellow")) {
        if (colors[0] === "black") {
          backgroundGradient = [
            [
              { color: cardColor, stops: ["0%", "60%"] },
              { color: "transparent", stops: ["70%", "100%"] },
            ],
            [
              { color: "transparent", stops: ["0%", "37%"] },
              { color: cardColor2, stops: ["60%", "100%"] },
            ],
            [
              { color: "transparent", stops: ["0%", "15%"] },
              // { color: "rgba(255,255,255, 0.3)", stops: ["40%"] },
              { color: "rgba(255,255,255, 0.4)", stops: ["50%", "50%"] },
              { color: cardColor2, stops: ["90%"] },
            ],
          ];
        } else {
          backgroundGradient = [
            [
              { color: cardColor, stops: ["0%", "60%"] },
              { color: "transparent", stops: ["70%", "100%"] },
            ],
            [
              { color: "transparent", stops: ["0%", "37%"] },
              { color: cardColor2, stops: ["60%", "100%"] },
            ],
            [
              { color: "transparent", stops: ["0%", "15%"] },
              // { color: "rgba(255,255,255, 0.3)", stops: ["40%"] },
              { color: "rgba(255,255,255, 0.1)", stops: ["50%", "50%"] },
              { color: cardColor2, stops: ["70%"] },
            ],
          ];
        }
      }
    }

    if (leaderBorder === "AA-black-and-white" || leaderBorder === "AA-black") {
      patternGradient = [
        [
          { color: cardColor, stops: ["0%", "45%"] },
          { color: "transparent", stops: ["70%", "100%"] },
        ],
        [
          { color: "transparent", stops: ["0%", "35%"] },
          { color: cardColor2, stops: ["55%", "100%"] },
        ],
      ];
      backgroundBlendMode = "darken";
    }
    if (
      leaderBorder === "AA-black-and-white" ||
      leaderBorder === "standard-white"
    ) {
      backgroundGradient = [
        [
          { color: "#fff", stops: ["0%", "100%"] },
          { color: "#fff", stops: ["0%", "100%"] },
        ],
      ];
    }
    if (leaderBorder === "rainbow") {
      backgroundGradient = [
        [
          { color: "#7E1B23", stops: ["5%"] },
          { color: "#9B7E50", stops: ["13%"] },
          { color: "#6E8D6A", stops: ["18%"] },
          { color: "#2B81AE", stops: ["35%", "35%"] },
          { color: "#62456F", stops: ["55%", "67%"] },
          { color: "#383838", stops: ["72%", "76%"] },
          { color: "#1F1C19", stops: ["78%", "78%"] },
          { color: "#383532", stops: ["80%"] },
          { color: "#9D9051", stops: ["90%"] },
        ],
      ];
    }
  }
  return [backgroundGradient, backgroundBlendMode, patternGradient];
}
