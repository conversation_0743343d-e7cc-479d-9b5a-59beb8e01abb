import { BlendMode, Color, LeaderBorder } from "@/types";
import { getMultiColorGradientHex } from "@/app/helpers/getMultiColorGradientHex";
import { HexColor } from "@/components/CardElement-components/card-elements/characterBorderTypes";

/**
 * Generates border gradients for cards with multiple colors
 * @param colors Array of colors
 * @param leaderBorder Leader border type
 * @param cardType Card type
 * @returns [backgroundGradient, backgroundBlendMode]
 */
export default function getMultiColorBorderGradientHex(
  colors: Color[],
  leaderBorder: LeaderBorder,
  cardType: "character" | "leader" | "event" | "stage" | "don",
): [HexColor[], BlendMode] {
  // For backward compatibility, handle the case when colors is empty
  if (!Array.isArray(colors) || colors.length === 0) {
    colors = ["red"];
  }

  const color = colors[0];
  const color2 = colors.length > 1 ? colors[colors.length - 1] : colors[0];

  let backgroundGradient: HexColor[] = [];
  // Set blend mode based on number of colors
  let backgroundBlendMode: BlendMode = "";
  if (colors.length === 2 && color !== color2) {
    backgroundBlendMode = "screen";
  } else if (colors.length === 3) {
    backgroundBlendMode = "screen"; // Use screen blend mode for 3 colors too
  }

  // let patternGradient = `linear-gradient(to right ,${colors.includes("blue") && colors.includes("green") && leaderBorder === "AA-white" ? cardColor2 : cardColor}  0% 45%,
  //  transparent 70% 100%), linear-gradient(to right,transparent 0% 35% ,${colors.includes("blue") && colors.includes("green") && leaderBorder === "AA-white" ? cardColor : cardColor2}  55% 100%)`;

  if (cardType === "leader") {
    // Handle special border types first
    if (
      leaderBorder === "standard-white" ||
      leaderBorder === "AA-black-and-white"
    ) {
      backgroundGradient = ["#fff", "#fff"];
      return [backgroundGradient, ""];
    }

    // For standard borders, use our multi-color gradient logic
    if (leaderBorder === "standard" || leaderBorder === "AA-white") {
      // Use our multi-color gradient function
      backgroundGradient = getMultiColorGradientHex(colors);
    }
    // For AA-black borders
    if (leaderBorder === "AA-black") {
      backgroundBlendMode = "darken";
    }
  }

  // If no specific gradient was set, generate a default gradient
  if (backgroundGradient.length === 0) {
    backgroundGradient = getMultiColorGradientHex(colors);
  }
  // console.log("Pattern Gradient: ", patternGradient);
  return [backgroundGradient, backgroundBlendMode];
}
