import { Color } from "@/types";
import { getCardColorHex } from "@/app/helpers/getCardColorHex";
import { HexColor } from "@/components/CardElement-components/card-elements/characterBorderTypes";

/**
 * Generates a CSS gradient string based on the number of colors
 * @param colors Array of colors
 * @param useBackgroundColors Whether to convert colors to background colors
 * @param eb02 Whether to use the eb02 colors
 * @returns CSS linear gradient string
 */
export function getMultiColorGradientHex(
  colors: Color[],
  useBackgroundColors: boolean = false,
  eb02 = { border: false, other: false },
): HexColor[] {
  if (!Array.isArray(colors) || colors.length === 0) {
    // Default to red if no colors

    return [getCardColorHex({ color: "red", bg: useBackgroundColors })];
  }

  // Convert colors to hex values

  return colors.map((color) =>
    getCardColorHex({ color, bg: useBackgroundColors, eb02 }),
  );
}
