import { Color, ColorObj } from "@/types";
import { getCardColor } from "./getCardColor";
import { getCardColorHex } from "@/app/helpers/getCardColorHex";

/**
 * Generates a pattern gradient for card borders with multiple colors
 * @param colors Array of colors
 * @returns CSS gradient string for pattern
 */
export function getPatternGradientSVG(colors: Color[]): ColorObj[][] {
  if (!Array.isArray(colors) || colors.length === 0) {
    // Default to red if no colors
    const defaultColor = getCardColor("red");

    return [[{ color: defaultColor }, { color: defaultColor }]];
  }

  // Convert colors to hex values
  const colorHexValues = colors.map((color) => getCardColorHex({ color }));

  // For 1 or 2 colors, use the existing pattern logic
  if (colorHexValues.length <= 2) {
    const cardColor = colorHexValues[0];
    const cardColor2 = colorHexValues[colorHexValues.length - 1];

    return [
      [
        { color: cardColor, stops: ["0%", "45%"] },
        { color: "transparent", stops: ["70%", "100%"] },
      ],
      [
        { color: "transparent", stops: ["0%", "35%"] },
        { color: cardColor2, stops: ["55%", "100%"] },
      ],
    ];
  }

  // For 3+ colors, create a smooth overlapping gradient
  // Create a gradient with overlapping colors for smoother transitions
  if (colorHexValues.length >= 3) {
    // For 3 colors, create a specific pattern with overlapping sections
    if (colorHexValues.length === 3) {
      // Create a gradient with more overlap between first and second colors
      return [
        [
          { color: colorHexValues[0], stops: ["0%", "40%"] },
          { color: colorHexValues[1], stops: ["45%", "75%"] },
          { color: colorHexValues[2], stops: ["60%", "100%"] },
        ],
      ];
    }

    // For more than 3 colors, distribute them evenly with overlap
    const gradientStops: ColorObj[] = [];
    for (let i = 0; i < colorHexValues.length; i++) {
      const color = colorHexValues[i];
      const position = (i / (colorHexValues.length - 1)) * 100;

      // Calculate position and overlap for each color
      const totalColors = colorHexValues.length;
      const segmentSize = 100 / (totalColors - 1);
      const overlap = segmentSize * 0.4; // 40% overlap for smoother transitions

      // Calculate start and end positions with overlap
      const start = Math.max(0, position - overlap);
      const end = Math.min(100, position + overlap);

      if (i === 0) {
        // First color - start at 0% but extend further to overlap with second color
        gradientStops.push({ color, stops: ["0%", `${end}%`] });
      } else if (i === totalColors - 1) {
        // Last color - end at 100% but start earlier to overlap with second-to-last color
        gradientStops.push({ color, stops: [`${start}%`, "100%"] });
      } else {
        // Middle colors - ensure overlap with adjacent colors
        gradientStops.push({ color, stops: [`${start}%`, `${end}%`] });
      }
    }

    return [gradientStops];
  }

  // Fallback to a simple gradient
  return colorHexValues.map((color) => [{ color }]);
}
