export function getPrintReadyDistanceString(
  initialDistance: string | undefined,
  distanceFrom: "left-or-right" | "top-or-bottom",
  printReady: boolean,
  offsetByPixel?: 1 | 4 | -1,
) {
  if (!printReady) return initialDistance;
  if (typeof initialDistance === "string" && distanceFrom === "left-or-right") {
    return `${(((parseFloat(initialDistance) / 100) * 3357 + (3677 - 3357) / 2) / 3677) * 100}%`;
  }
  if (typeof initialDistance === "string" && distanceFrom === "top-or-bottom") {
    const result =
      (((parseFloat(initialDistance) / 100) * 4692 + (5011 - 4692) / 2) /
        5011) *
      100;
    if (offsetByPixel === 1)
      return `${offsetByPixel ? result * 1.001 : result}%`;
    if (offsetByPixel === -1)
      return `${offsetByPixel ? result * 0.9998 : result}%`;
    if (offsetByPixel === 4)
      return `${offsetByPixel ? result * 1.008 : result}%`;
    return `${result}%`;
  }
}

export function getPrintReadyDistanceNumber(
  initialDistance: number | undefined,
  distanceFrom: "left-or-right" | "top-or-bottom",
  printReady: boolean,
) {
  if (!printReady) return initialDistance;
  if (typeof initialDistance === "number" && distanceFrom === "left-or-right") {
    return (((initialDistance / 100) * 3357 + (3677 - 3357) / 2) / 3677) * 100;
  }
  if (typeof initialDistance === "number" && distanceFrom === "top-or-bottom") {
    return (((initialDistance / 100) * 4692 + (5011 - 4692) / 2) / 5011) * 100;
  }
}
