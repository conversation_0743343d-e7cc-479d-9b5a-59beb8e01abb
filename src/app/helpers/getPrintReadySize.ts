export function getPrintReadySizeString(
  initialValue: string | undefined,
  printReady: boolean,
  offsetByPixels?: 25,
) {
  if (!printReady) return initialValue;
  if (typeof initialValue === "string") {
    const result = (parseFloat(initialValue) / 100) * (3357 / 3677) * 100;
    if (offsetByPixels === 25) return result * 1.0255 + "%";
    return `${result}%`;
  }
}

export function getPrintReadySizeNumber(
  initialValue: number | undefined,
  printReady: boolean,
) {
  if (!printReady) return initialValue;
  if (typeof initialValue === "number") {
    return (initialValue / 100) * (3357 / 3677) * 100;
  }
}
