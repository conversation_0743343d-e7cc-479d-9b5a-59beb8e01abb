import {
  CHARACTER_COLORS_SEC,
  CHARACTER_COLORS_SP,
} from "@/components/CardElement-components/card-elements/characterBorderTypes";
import {
  CharacterBorder,
  CharacterColor,
  EventBorder,
  LeaderBorder,
} from "@/types";

export function getTextColor(
  color: string,
  characterBorderType: CharacterBorder = "none",
  cardType: "character" | "leader" | "event" | "stage" | "don" = "character",
  leaderBorder: LeaderBorder | false = false,
  specifyTextOnCard:
    | "counter"
    | "card_number_and_set"
    | "card_kind"
    | "power"
    | "artist"
    | "made_with"
    | "card_type"
    | false = false,
  colorArray: string[],
  eventBorder?: EventBorder,
) {
  if (cardType === "event" && eventBorder === "eb-02") {
    return "#000";
  }
  if (
    cardType === "event" &&
    eventBorder === "op-10" &&
    specifyTextOnCard === "card_number_and_set"
  ) {
    return "#fff";
  }
  if (cardType === "event" && eventBorder === "op-10") {
    return CHARACTER_COLORS_SEC["goldEvent"];
  }
  if (
    cardType === "character" &&
    characterBorderType === "none" &&
    color === "yellow" &&
    (specifyTextOnCard === "card_number_and_set" ||
      specifyTextOnCard === "artist" ||
      specifyTextOnCard === "made_with")
  ) {
    return CHARACTER_COLORS_SP["yellowText"];
  }
  if (
    cardType === "character" &&
    characterBorderType === "none" &&
    specifyTextOnCard === "counter" &&
    color === "yellow"
  ) {
    return CHARACTER_COLORS_SP["yellowText"];
  }
  if (
    cardType === "character" &&
    characterBorderType === "none" &&
    specifyTextOnCard === "counter"
  ) {
    return CHARACTER_COLORS_SP[color as CharacterColor];
  }

  if (cardType === "character" && specifyTextOnCard === "power") {
    if (characterBorderType === "sr" || characterBorderType === "sp-v2") {
      return "#000";
    }
    if (
      characterBorderType === "sec" ||
      characterBorderType === "sec-aa" ||
      characterBorderType === "mr"
    ) {
      return CHARACTER_COLORS_SEC["gold"];
    }

    if (characterBorderType !== "standard") {
      return "";
    } else {
      return "brightness-0";
    }
  }
  if (
    cardType === "character" &&
    (characterBorderType === "sec" || characterBorderType === "sec-aa")
  ) {
    return CHARACTER_COLORS_SEC["gold"];
  }
  if (
    cardType === "character" &&
    characterBorderType === "mr" &&
    specifyTextOnCard !== "counter"
  ) {
    return CHARACTER_COLORS_SEC["gold"];
  }
  if (
    cardType === "character" ||
    cardType === "event" ||
    cardType === "stage"
  ) {
    if (
      cardType === "character" &&
      (characterBorderType === "none" || characterBorderType === "sp-v2")
    ) {
      return "";
    } else {
      return color === "yellow" && colorArray?.length === 1
        ? "brightness-0"
        : color === "yellow" && colorArray?.length > 1
          ? "#fff"
          : "";
    }
  }
  if (cardType === "leader" && specifyTextOnCard === "power") {
    if (leaderBorder === "rainbow" || leaderBorder === "full-art") {
      return "#fff";
    } else {
      if (leaderBorder === "standard-white" || leaderBorder === "AA-black") {
        return "#000";
      }
      if (
        leaderBorder === "25th" ||
        leaderBorder === "AA-white" ||
        leaderBorder === "AA-black-and-white"
      ) {
        return "#fff";
      }
      return color?.toLocaleLowerCase().includes("yellow") ? "#000" : "#fff";
    }
  }
  if (cardType === "leader") {
    if (leaderBorder === "AA-white") {
      if (
        specifyTextOnCard === "card_kind" ||
        specifyTextOnCard === "card_type"
      ) {
        return "#171717";
      }
      return "";
    }
    if (leaderBorder === "full-art") {
      return "#171717";
    }
    return "";
  }
  return "";
}
