// "use client";
// import { useState } from "react";
// import <PERSON><PERSON><PERSON> from "react-easy-crop";
// import { useGetStoreState } from "@/helpers/useGetStoreState";
// import { useDispatch } from "react-redux";
// import { onImageUrl } from "@/store/formSlice";
// import { Button } from "@mantine/core";
// import CardBorder from "@/components/CardElement-components/card-elements/CardBorder";
//
// export function ImageCropper({
//   cardType,
// }: {
//   cardType: "character" | "leader" | "event" | "stage" | "don";
// }) {
//   const dispatch = useDispatch();
//   const [crop, setCrop] = useState({ x: 0, y: 0 });
//   const [zoom, setZoom] = useState(1);
//
//   const [fileUrl, setFileUrl] = useState<string | null>(null); // State to hold the Data URL
//
//   const [croppedArea, setCroppedArea] = useState<any | null>(null); // State to hold cropped File
//   const [croppedAreaPixels, setCroppedAreaPixels] = useState<any | null>(null); // State to hold cropped File
//   const [isCropping, setIsCropping] = useState(false); // State to track cropping state
//   const imageFile = useGetStoreState("imageFile") as File | null;
//
//   const onCropComplete = () => {
//     if (croppedArea && croppedAreaPixels && fileUrl) {
//       // Perform cropping and set cropped data in the state
//       const canvas = document.createElement("canvas");
//       const image = new Image();
//       image.src = fileUrl;
//       image.onload = () => {
//         canvas.width = croppedAreaPixels.width;
//         canvas.height = croppedAreaPixels.height;
//         const ctx = canvas.getContext("2d");
//         ctx?.drawImage(
//           image,
//           croppedAreaPixels.x,
//           croppedAreaPixels.y,
//           croppedAreaPixels.width,
//           croppedAreaPixels.height,
//           0,
//           0,
//           croppedAreaPixels.width,
//           croppedAreaPixels.height,
//         );
//         const croppedDataURL = canvas.toDataURL("image/jpeg");
//         dispatch(onImageUrl(croppedDataURL));
//       };
//       setIsCropping(false);
//     }
//   };
//   function onCrop(croppedArea: any, croppedAreaPixels: any) {
//     setCroppedArea(croppedArea);
//     setCroppedAreaPixels(croppedAreaPixels);
//   }
//
//   const startCrop = () => {
//     loadImage(imageFile!);
//     setIsCropping(true);
//   };
//
//   const completeCrop = () => {
//     setIsCropping(false);
//     // You can use croppedDataUrl and croppedFile as needed
//   };
//
//   const loadImage = (file: File) => {
//     const reader = new FileReader();
//     reader.onload = (e) => {
//       setFileUrl(e?.target?.result as string);
//     };
//     reader.readAsDataURL(file);
//   };
//
//   return (
//     <>
//       {isCropping && fileUrl ? (
//         <>
//           <Button
//             variant={"filled"}
//             onClick={onCropComplete}
//             className={
//               "font-normal tracking-wide dark:bg-[rgb(55,58,64)] dark:text-[rgb(212,212,212)]"
//             }
//             fullWidth
//           >
//             Complete Cropping
//           </Button>
//           <div className={"relative xl:max-w-[19rem]"}>
//             <Cropper
//               image={fileUrl}
//               crop={crop}
//               zoom={zoom}
//               aspect={3357 / 4692}
//               onCropChange={setCrop}
//               onCropComplete={onCrop}
//               onZoomChange={setZoom}
//               setCropSize={(size) => {}}
//               initialCroppedAreaPixels={{
//                 height: 300,
//                 width: 200,
//                 y: -25,
//                 x: -50,
//               }}
//               style={{
//                 cropAreaStyle: {
//                   position: "absolute",
//
//                   zIndex: "100",
//                 },
//                 containerStyle: {
//                   position: "absolute",
//                 },
//                 mediaStyle: {
//                   position: "absolute",
//                 },
//               }}
//             />
//
//             <CardBorder cardType={cardType} quality={1} />
//             <div className={""}></div>
//           </div>
//         </>
//       ) : (
//         <div>
//           {imageFile && (
//             <>
//               <Button
//                 variant={"filled"}
//                 onClick={startCrop}
//                 className={
//                   "font-normal tracking-wide dark:bg-[rgb(55,58,64)] dark:text-[rgb(212,212,212)]"
//                 }
//                 fullWidth
//               >
//                 Crop the image
//               </Button>
//             </>
//           )}
//         </div>
//       )}
//     </>
//   );
// }
