"use client";
import {
  onAttribute,
  onCharacterBorderType,
  onColorArray,
  onEventBorderType,
  onInputField,
  onLeaderBorderType,
  onSPTop,
} from "@/store/formSlice";
import { Dispatch } from "@reduxjs/toolkit";
import { ChangeEvent } from "react";
import {
  CardAttribute,
  CardCost,
  CardLife,
  CharacterBorder,
  Color,
  EventBorder,
  LeaderBorder,
} from "@/types";

export function handleMultiColorChange(dispatch: Dispatch, colors: Color[]) {
  if (colors && colors.length > 0) {
    dispatch(onColorArray(colors));
  }
}
/*export function handleAbilityTextSize(
  dispatch: Dispatch,
  size: number,
) {
  dispatch(onAbilityScreenshotTextSize(size));
}*/

export function handleAttributeChange(
  dispatch: Dispatch,
  attribute: CardAttribute,
) {
  if (attribute) {
    dispatch(onAttribute(attribute));
  }
}

export function handleNameChange(
  dispatch: Dispatch,
  event: ChangeEvent<HTMLInputElement>,
) {
  const value = event.target.value;

  dispatch(onInputField("name", value));
}
export function handleDonTextChange(
  dispatch: Dispatch,
  event: ChangeEvent<HTMLInputElement>,
) {
  const value = event.target.value;

  dispatch(onInputField("donText", value));
}
export function handleCardTypeChange(
  dispatch: Dispatch,
  event: ChangeEvent<HTMLInputElement>,
) {
  const value = event.target.value;

  dispatch(onInputField("cardType", value));
}
export function handleCardCostChange(dispatch: Dispatch, cost: CardCost) {
  dispatch(onInputField("cost", cost));
}
export function handleCardLifeChange(dispatch: Dispatch, life: CardLife) {
  dispatch(onInputField("life", life));
}
export function handleCardPowerChange(dispatch: Dispatch, power: string) {
  dispatch(onInputField("power", power));
}
export function handleDonPowerChange(dispatch: Dispatch, power: string) {
  dispatch(onInputField("donPower", power));
}
export function handleCardTriggerChange(
  dispatch: Dispatch,
  event: ChangeEvent<HTMLInputElement>,
) {
  const value = event.target.value;
  const trigger = !!value;
  dispatch(onInputField("triggerText", value));

  dispatch(onInputField("trigger", trigger));
}
export function handleCardCounterChange(
  dispatch: Dispatch,
  event: ChangeEvent<HTMLInputElement>,
) {
  const value = event.target.value;

  dispatch(onInputField("counterText", value));
}
export function handleCardSetChange(
  dispatch: Dispatch,
  event: ChangeEvent<HTMLInputElement>,
) {
  const value = event.target.value;

  dispatch(onInputField("set", value));
}
export function handleCardRarityChange(
  dispatch: Dispatch,
  event: ChangeEvent<HTMLInputElement>,
) {
  const value = event.target.value;

  dispatch(onInputField("rarity", value));
}

export function handleCardRarity2Change(
  dispatch: Dispatch,
  event: ChangeEvent<HTMLInputElement>,
) {
  const value = event.target.value;

  dispatch(onInputField("rarity2", value));
}
export function handleCardNumberChange(
  dispatch: Dispatch,
  event: ChangeEvent<HTMLInputElement>,
) {
  const value = event.target.value;

  dispatch(onInputField("cardNum", value));
}

export function handleCardPrintWaveChange(
  dispatch: Dispatch,
  event: ChangeEvent<HTMLInputElement>,
) {
  const value = event.target.value;

  dispatch(onInputField("printWave", value));
}
export function handleCardArtistChange(
  dispatch: Dispatch,
  event: ChangeEvent<HTMLInputElement>,
) {
  const value = event.target.value;

  dispatch(onInputField("artist", value));
}
export function handleLeaderBorderTypeChange(
  dispatch: Dispatch,
  borderType: LeaderBorder,
) {
  if (borderType) {
    dispatch(onLeaderBorderType(borderType));
  }
}
export function handleCharacterBorderTypeChange(
  dispatch: Dispatch,
  borderType: CharacterBorder,
) {
  if (borderType) {
    dispatch(onCharacterBorderType(borderType));
  }
}

export function handleEventBorderTypeChange(
  dispatch: Dispatch,
  borderType: EventBorder,
) {
  if (borderType) {
    dispatch(onEventBorderType(borderType));
  }
}
export function handlePowerBlackChange(
  dispatch: Dispatch,
  event: ChangeEvent<HTMLInputElement>,
) {
  const value = event.target.checked;

  dispatch(onInputField("powerBlack", value));
}

export function handleFoilBorderChange(
  dispatch: Dispatch,
  event: ChangeEvent<HTMLInputElement>,
) {
  const value = event.target.checked;

  dispatch(onInputField("foilBorder", value));
}

export function handleLeaderBorderEnabledChange(
  dispatch: Dispatch,
  event: ChangeEvent<HTMLInputElement>,
) {
  const value = event.target.checked;

  dispatch(onInputField("leaderBorderEnabled", value));
}
export function handleAAStarChange(
  dispatch: Dispatch,
  event: ChangeEvent<HTMLInputElement>,
) {
  const value = event.target.checked;

  dispatch(onInputField("aaStar", value));
}

export function handlePrintReadyChange(
  dispatch: Dispatch,
  event: ChangeEvent<HTMLInputElement>,
) {
  const value = event.target.checked;

  dispatch(onInputField("printReady", value));
}
export function handleMultiColorModeChange(dispatch: Dispatch, event: boolean) {
  dispatch(onInputField("multiColorMode", event));
}

export function handleDonAbilityChange(
  dispatch: Dispatch,
  event: ChangeEvent<HTMLInputElement>,
) {
  const value = event.target.checked;

  dispatch(onInputField("donAbility", value));
}
export function handeSPTopChange(dispatch: Dispatch, SPTop: boolean) {
  dispatch(onSPTop(SPTop));
}
