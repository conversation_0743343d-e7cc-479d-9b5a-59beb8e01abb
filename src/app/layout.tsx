import "./globals.css";

import { ColorSchemeScript } from "@mantine/core";

import { PostHogProvider } from "@/components/PostHogProvider";

import { ReactNode } from "react";
import { Metadata } from "next";
import UmamiAnalytics from "@/components/UmamiAnalytics";
import { ThemeProvider } from "@/components/theme-provider";
import { Toaster } from "sonner";
import StoreProvider from "@/components/StoreProvider";
import UpdateNotification from "@/components/UpdateNotification";

export const metadata: Metadata = {
  metadataBase: new URL("https://ultimatetcgcm.com/"),
};

export default function RootLayout({ children }: { children: ReactNode }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className="font-geist-sans">
        <ColorSchemeScript defaultColorScheme="auto" />
        <PostHogProvider>
          <ThemeProvider
            attribute="class"
            defaultTheme="system"
            enableSystem
            disableTransitionOnChange
          >
            <StoreProvider>{children}</StoreProvider>
          </ThemeProvider>
          <UmamiAnalytics />
          <UpdateNotification />
          <Toaster position="bottom-right" richColors closeButton />
        </PostHogProvider>
      </body>
    </html>
  );
}
