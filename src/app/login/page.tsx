import { Metada<PERSON> } from "next";
import { createClient } from "@/utils/supabase/server";
import { permanentRedirect, RedirectType } from "next/navigation";
import { LoginForm } from "@/components/login-form";

export const metadata: Metadata = {
  title: "Sign In - Ultimate TCG Card Maker",
  description: "Sign In with google account or create one",
  alternates: {
    canonical: "https://ultimatetcgcm.com/login",
  },
};
export default async function Page() {
  const supabase = await createClient();
  const session = await supabase.auth.getSession();
  if (session.data.session) {
    permanentRedirect("/one-piece/character", RedirectType.push);
    return null;
  }
  // Test vercel preview
  return (
    <div className="flex min-h-svh w-full items-center justify-center p-6 md:p-10">
      <div className="w-full max-w-sm">
        <LoginForm />
      </div>
    </div>
  );
}
