import "@mantine/core/styles/Button.css";
import React from "react";
import ProfileMenu from "@/app/one-piece/ProfileMenu";
import OnePieceLayoutShell from "@/app/one-piece/OnePieceLayoutShell";
import { createClient } from "@/utils/supabase/server";

export default async function OnePieceLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const supabase = await createClient();
  let username = "";
  let avatarUrl = "";

  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (user) {
    const { data } = await supabase
      .from("profiles")
      .select("username,avatar_url")
      .eq("id", user?.id);
    if (data && data[0]) {
      username = data[0]?.username ? data[0].username : "";
      if (data[0]?.avatar_url) {
        if (data[0]?.avatar_url.includes("googleusercontent")) {
          avatarUrl = data[0]?.avatar_url;
        } else {
          const {
            data: { publicUrl },
          } = supabase.storage
            .from("avatars")
            .getPublicUrl(data[0]?.avatar_url);
          avatarUrl = publicUrl;
        }
      }
    }

    if (data && !data[0].avatar_url) {
      avatarUrl = user.user_metadata.avatar_url;
    }
  }

  return (
    <>
      <OnePieceLayoutShell
        ProfileMenu={
          <ProfileMenu user={user} username={username} avatarUrl={avatarUrl} />
        }
      >
        {children}
      </OnePieceLayoutShell>
    </>
  );
}
