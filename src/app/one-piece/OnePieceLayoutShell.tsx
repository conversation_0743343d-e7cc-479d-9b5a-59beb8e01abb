"use client";
import { AppShell, Burger } from "@mantine/core";
import Link from "next/link";

import React from "react";
import { useDisclosure } from "@/hooks/useDisclosure";
import { usePathname } from "next/navigation";

export default function OnePieceLayoutShell({
  children,
  ProfileMenu,
}: {
  children: React.ReactNode;
  ProfileMenu: React.JSX.Element;
}) {
  const [opened, { toggle }] = useDisclosure();
  const pathName = usePathname();
  return (
    <AppShell
      header={{ height: 60 }}
      navbar={{
        width: 300,
        breakpoint: "md",
        collapsed: { mobile: !opened },
      }}
      padding="md"
    >
      <AppShell.Header
        className={
          "flex grid-cols-3 grid-rows-1 items-center gap-4 px-4! lg:grid lg:grid-cols-2 dark:border-[#424242] dark:bg-[#242424]"
        }
      >
        <Burger opened={opened} onClick={toggle} hiddenFrom="md" size="lg" />
        <Link
          href={"/"}
          className={
            "grow rounded-sm px-2! py-1! text-center text-neutral-900 transition-colors duration-200 active:bg-neutral-200 lg:justify-self-start lg:text-start lg:hover:bg-neutral-200 lg:active:bg-neutral-300 dark:text-neutral-100 dark:active:bg-[#2E2E2E] dark:lg:hover:bg-[#2E2E2E] dark:lg:active:bg-[#3B3B3B]"
          }
          onClick={() => {}}
        >
          Ultimate TCG Card Maker
        </Link>
        {/*<ColorCycle />*/}
        {ProfileMenu}
      </AppShell.Header>
      <AppShell.Navbar
        p={"md"}
        className={
          "bg-[#ffffff]/80 backdrop-blur-md md:bg-[#fff] md:backdrop-blur-none dark:border-[#424242] dark:bg-[#242424]/70 dark:backdrop-blur-md md:dark:bg-[#242424]"
        }
      >
        <div className={"flex flex-col"}>
          <Link
            href={"/one-piece/character"}
            onClick={() => {
              toggle();
            }}
            className={`${pathName === "/one-piece/character" && "bg-neutral-300 dark:bg-[#3B3B3B]"} rounded-sm px-2! py-2! text-neutral-900 transition-colors duration-200 active:bg-neutral-200 lg:hover:bg-neutral-200 lg:active:bg-neutral-300 dark:text-neutral-100 dark:active:bg-[#2E2E2E] dark:lg:hover:bg-[#2E2E2E] dark:lg:active:bg-[#3B3B3B]`}
          >
            Character
          </Link>
          <Link
            href={"/one-piece/leader"}
            onClick={toggle}
            className={`${pathName === "/one-piece/leader" && "bg-neutral-300 dark:bg-[#3B3B3B]"} rounded-sm px-2! py-2! text-neutral-900 transition-colors duration-200 active:bg-neutral-200 lg:hover:bg-neutral-200 lg:active:bg-neutral-300 dark:text-neutral-100 dark:active:bg-[#2E2E2E] dark:lg:hover:bg-[#2E2E2E] dark:lg:active:bg-[#3B3B3B]`}
          >
            Leader
          </Link>
          <Link
            href={"/one-piece/event"}
            onClick={() => {
              toggle();
            }}
            className={` ${pathName === "/one-piece/event" && "bg-neutral-300 dark:bg-[#3B3B3B]"} rounded-sm px-2! py-2! text-neutral-900 transition-colors duration-200 active:bg-neutral-200 lg:hover:bg-neutral-200 lg:active:bg-neutral-300 dark:text-neutral-100 dark:active:bg-[#2E2E2E] dark:lg:hover:bg-[#2E2E2E] dark:lg:active:bg-[#3B3B3B]`}
          >
            Event
          </Link>
          <Link
            href={"/one-piece/stage"}
            onClick={() => {
              toggle();
            }}
            className={` ${pathName === "/one-piece/stage" && "bg-neutral-300 dark:bg-[#3B3B3B]"} rounded-sm px-2! py-2! text-neutral-900 transition-colors duration-200 active:bg-neutral-200 lg:hover:bg-neutral-200 lg:active:bg-neutral-300 dark:text-neutral-100 dark:active:bg-[#2E2E2E] dark:lg:hover:bg-[#2E2E2E] dark:lg:active:bg-[#3B3B3B]`}
          >
            Stage
          </Link>
          <Link
            href={"/one-piece/don"}
            onClick={() => {
              toggle();
            }}
            className={` ${pathName === "/one-piece/don" && "bg-neutral-300 dark:bg-[#3B3B3B]"} rounded-sm px-2! py-2! text-neutral-900 transition-colors duration-200 active:bg-neutral-200 lg:hover:bg-neutral-200 lg:active:bg-neutral-300 dark:text-neutral-100 dark:active:bg-[#2E2E2E] dark:lg:hover:bg-[#2E2E2E] dark:lg:active:bg-[#3B3B3B]`}
          >
            Don
          </Link>
        </div>
      </AppShell.Navbar>
      <AppShell.Main className={"bg-neutral-100 dark:bg-[#1F1F1F]"}>
        {children}
      </AppShell.Main>
    </AppShell>
  );
}
