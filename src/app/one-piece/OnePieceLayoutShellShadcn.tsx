"use client";
import Link from "next/link";
import React, { useState } from "react";
import { usePathname } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Menu, X } from "lucide-react";
import { cn } from "@/lib/utils";

export default function OnePieceLayoutShellShadcn({
  children,
  ProfileMenu,
}: {
  children: React.ReactNode;
  ProfileMenu: React.ReactNode;
}) {
  const [opened, setOpened] = useState(false);
  const pathName = usePathname();

  const toggleMenu = () => {
    setOpened(!opened);
  };

  return (
    <div className="flex h-screen flex-col overflow-hidden">
      {/* Header - Fixed height at the top */}
      <header className="bg-background flex h-[60px] shrink-0 grid-cols-3 grid-rows-1 items-center gap-4 border-b px-4 lg:grid lg:grid-cols-2 dark:border-[#424242] dark:bg-[#242424]">
        <Button
          variant="ghost"
          size="icon"
          onClick={toggleMenu}
          className="dark:active:bg-input0 relative active:bg-[#3B3B3B] md:hidden"
          aria-label={opened ? "Close menu" : "Open menu"}
        >
          <Menu
            className={cn(
              "h-9! w-9! transition-all duration-300",
              opened
                ? "scale-0 rotate-90 opacity-0"
                : "scale-100 rotate-0 opacity-100",
            )}
          />
          <X
            className={cn(
              "absolute inset-0 m-auto h-9! w-9! transition-all duration-300",
              opened
                ? "scale-100 rotate-0 opacity-100"
                : "scale-0 rotate-90 opacity-0",
            )}
          />
        </Button>
        <Link
          href={"/"}
          className="grow rounded-sm px-2 py-1 text-center text-neutral-900 transition-colors duration-200 active:bg-neutral-200 lg:justify-self-start lg:text-start lg:hover:bg-neutral-200 lg:active:bg-neutral-300 dark:text-neutral-100 dark:active:bg-[#2E2E2E] dark:lg:hover:bg-[#2E2E2E] dark:lg:active:bg-[#3B3B3B]"
        >
          Ultimate TCG Card Maker
        </Link>
        {ProfileMenu}
      </header>

      {/* Content area - Takes remaining height */}
      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar/Navbar - Fixed on the left for mobile, static for desktop */}
        <aside
          className={cn(
            "bg-background fixed top-[60px] left-0 z-30 h-[calc(100vh-60px)] w-[300px] overflow-y-auto border-r p-4 transition-transform duration-300 md:static md:top-0",
            opened ? "translate-x-0" : "-translate-x-full",
            "bg-[#ffffff]/80 backdrop-blur-md md:translate-x-0 md:bg-[#fff] md:backdrop-blur-none dark:border-[#424242] dark:bg-[#242424]/70 dark:backdrop-blur-md md:dark:bg-[#242424]",
          )}
        >
          <div className="flex flex-col">
            <Link
              href={"/one-piece/character"}
              onClick={toggleMenu}
              className={`${pathName === "/one-piece/character" && "bg-neutral-300 dark:bg-[#3B3B3B]"} rounded-sm px-2 py-2 text-neutral-900 transition-colors duration-200 active:bg-neutral-200 lg:hover:bg-neutral-200 lg:active:bg-neutral-300 dark:text-neutral-100 dark:active:bg-[#2E2E2E] dark:lg:hover:bg-[#2E2E2E] dark:lg:active:bg-[#3B3B3B]`}
            >
              Character
            </Link>
            <Link
              href={"/one-piece/leader"}
              onClick={toggleMenu}
              className={`${pathName === "/one-piece/leader" && "bg-neutral-300 dark:bg-[#3B3B3B]"} rounded-sm px-2 py-2 text-neutral-900 transition-colors duration-200 active:bg-neutral-200 lg:hover:bg-neutral-200 lg:active:bg-neutral-300 dark:text-neutral-100 dark:active:bg-[#2E2E2E] dark:lg:hover:bg-[#2E2E2E] dark:lg:active:bg-[#3B3B3B]`}
            >
              Leader
            </Link>
            <Link
              href={"/one-piece/event"}
              onClick={toggleMenu}
              className={`${pathName === "/one-piece/event" && "bg-neutral-300 dark:bg-[#3B3B3B]"} rounded-sm px-2 py-2 text-neutral-900 transition-colors duration-200 active:bg-neutral-200 lg:hover:bg-neutral-200 lg:active:bg-neutral-300 dark:text-neutral-100 dark:active:bg-[#2E2E2E] dark:lg:hover:bg-[#2E2E2E] dark:lg:active:bg-[#3B3B3B]`}
            >
              Event
            </Link>
            <Link
              href={"/one-piece/stage"}
              onClick={toggleMenu}
              className={`${pathName === "/one-piece/stage" && "bg-neutral-300 dark:bg-[#3B3B3B]"} rounded-sm px-2 py-2 text-neutral-900 transition-colors duration-200 active:bg-neutral-200 lg:hover:bg-neutral-200 lg:active:bg-neutral-300 dark:text-neutral-100 dark:active:bg-[#2E2E2E] dark:lg:hover:bg-[#2E2E2E] dark:lg:active:bg-[#3B3B3B]`}
            >
              Stage
            </Link>
            <Link
              href={"/one-piece/don"}
              onClick={toggleMenu}
              className={`${pathName === "/one-piece/don" && "bg-neutral-300 dark:bg-[#3B3B3B]"} rounded-sm px-2 py-2 text-neutral-900 transition-colors duration-200 active:bg-neutral-200 lg:hover:bg-neutral-200 lg:active:bg-neutral-300 dark:text-neutral-100 dark:active:bg-[#2E2E2E] dark:lg:hover:bg-[#2E2E2E] dark:lg:active:bg-[#3B3B3B]`}
            >
              Don
            </Link>
          </div>
        </aside>

        {/* Main content - Scrollable area with left margin for the sidebar */}
        <main className="ml-0 flex-1 overflow-y-auto bg-neutral-100 p-4 md:ml-0 dark:bg-[#1F1F1F]">
          {children}
        </main>
      </div>
    </div>
  );
}
