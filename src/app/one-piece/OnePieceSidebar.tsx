"use client";

import React, { useEffect } from "react";
import <PERSON> from "next/link";
import { usePathname } from "next/navigation";

import {
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarTrigger,
  useSidebar,
} from "@/components/ui/sidebar";

interface OnePieceSidebarProps {
  children: React.ReactNode;
  ProfileMenu: React.ReactNode;
}

export function OnePieceSidebar({
  children,
  ProfileMenu,
}: OnePieceSidebarProps) {
  const pathname = usePathname();
  const { isMobile, setOpenMobile } = useSidebar();

  // Function to close the sidebar on mobile when a link is clicked
  const handleLinkClick = () => {
    if (isMobile) {
      setOpenMobile(false);
    }
  };

  useEffect(() => {
    // Set up any body styles or event listeners here

    return () => {
      // Safely clean up on unmount
      if (document.body) {
        document.body.style.pointerEvents = "auto";
      }
    };
  }, []);

  const menuItems = [
    {
      title: "Character",
      url: "/one-piece/character",
    },
    {
      title: "Leader",
      url: "/one-piece/leader",
    },
    {
      title: "Event",
      url: "/one-piece/event",
    },
    {
      title: "Stage",
      url: "/one-piece/stage",
    },
    {
      title: "Don",
      url: "/one-piece/don",
    },
  ];

  return (
    <div className="flex min-h-screen min-w-screen">
      <Sidebar variant="sidebar" collapsible="offcanvas">
        <SidebarHeader className="dark:bg-sidebar hidden h-[60px] bg-white py-2 md:block">
          <Link
            href="/"
            className="dark:data-[active=true]:bg-full-button-active dark:hover:bg-full-button-hover! dark:active:bg-full-button-active! data-[active=true]:bg-full-button-active mt-1 flex items-center gap-2 rounded-sm px-2 py-1 transition-colors duration-100 hover:bg-neutral-200! active:bg-neutral-300!"
            onClick={handleLinkClick}
          >
            <span className="font-semibold">Ultimate TCG Card Maker</span>
          </Link>
        </SidebarHeader>
        <p className={"text-xxl mb-4 px-2 md:hidden"} onClick={handleLinkClick}>
          Menu
        </p>
        <SidebarContent className={"dark:bg-sidebar bg-white px-2"}>
          <SidebarMenu>
            {menuItems.map((item) => (
              <SidebarMenuItem key={item.title}>
                <SidebarMenuButton
                  asChild
                  isActive={pathname === item.url}
                  className={"text-lg md:text-base"}
                >
                  <Link
                    className={
                      "dark:data-[active=true]:bg-full-button-active dark:hover:bg-full-button-hover! dark:active:bg-full-button-active! data-[active=true]:bg-full-button-active transition-colors duration-100 hover:bg-neutral-200! active:bg-neutral-300!"
                    }
                    href={item.url}
                    onClick={handleLinkClick}
                  >
                    <span>{item.title}</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            ))}
          </SidebarMenu>
        </SidebarContent>
        {/*<SidebarFooter className="items-end px-4 py-2 md:items-start">*/}
        {/*  {ProfileMenu}*/}
        {/*</SidebarFooter>*/}
      </Sidebar>
      <div className="relative flex max-w-[100vw] flex-1 flex-col">
        <header className="dark:bg-sidebar fixed z-20 flex h-[60px] w-full items-center justify-between border-b bg-white px-4 md:w-[calc(100vw-265px)]">
          {/*TODO: Check why sidebar trigger and link are re-rending when they are outside the sidebar component*/}
          <div className="md:hidden">
            <SidebarTrigger />
          </div>
          <div className="flex-1 text-center md:hidden">
            <Link
              href="/"
              className="dark:data-[active=true]:bg-full-button-active dark:hover:bg-full-button-hover! dark:active:bg-full-button-active! mt-2 gap-2 rounded-sm px-2 py-2 font-semibold transition-colors duration-100"
              onClick={handleLinkClick}
            >
              Ultimate TCG Card Maker
            </Link>
          </div>
          <div className={"ml-auto"}> {ProfileMenu}</div>

          <div>{/* Add any header content here */}</div>
        </header>
        <main className="mt-[60px] flex-1 bg-neutral-100 p-4 dark:bg-[#1F1F1F]">
          {children}
        </main>
      </div>
    </div>
  );
}
