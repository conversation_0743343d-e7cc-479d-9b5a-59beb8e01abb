"use client";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

import React, { useEffect } from "react";
import { useRouter } from "next/navigation";
import { createClient } from "@/utils/supabase/client";
import { User } from "@supabase/supabase-js";
import { Subscription } from "@supabase/supabase-js";

export default function ProfileMenu({
  username,
  user,
  avatarUrl,
}: {
  username: string;
  user: User | null;
  avatarUrl: string;
}) {
  const router = useRouter();
  const supabase = createClient();
  useEffect(() => {
    let subscription: { data: { subscription: Subscription } };
    async function run() {
      subscription = supabase.auth.onAuthStateChange(
        async (event, newSession) => {
          if (newSession?.access_token) {
            router.refresh();
          }
        },
      );
    }
    run();
    return () => {
      subscription.data.subscription.unsubscribe();
    };
  }, [router, supabase.auth]);
  return (
    <>
      {!user && (
        <Button
          onClick={() => {
            router.push("/login");
          }}
          className={"justify-self-end"}
        >
          Sign in
        </Button>
      )}
      {user && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button
              className={`flex items-center justify-self-end ${
                user ? "rounded-sm" : "rounded-3xl"
              } ${
                user ? "px-2" : ""
              } text-neutral-900 transition-colors duration-200 active:bg-neutral-200 lg:hover:bg-neutral-200 lg:active:bg-neutral-300 dark:text-neutral-100 dark:active:bg-[#3B3B3B] dark:lg:hover:bg-[#2E2E2E] dark:lg:active:bg-[#3B3B3B]`}
            >
              <div className="flex items-center gap-2">
                <span className={"hidden lg:block"}>
                  {username ? username : user?.email}
                </span>

                <Avatar>
                  <AvatarImage src={avatarUrl} />
                  <AvatarFallback>U</AvatarFallback>
                </Avatar>
              </div>
            </button>
          </DropdownMenuTrigger>

          <DropdownMenuContent align="end" className="w-[150px]">
            {user && (
              <>
                <DropdownMenuItem
                  className={
                    "rounded-sm text-neutral-900 transition-colors duration-200 active:bg-neutral-200 lg:hover:bg-neutral-200 lg:active:bg-neutral-300 dark:text-neutral-100 dark:active:bg-[#424242] dark:lg:hover:bg-[#3B3B3B] dark:lg:active:bg-[#424242]"
                  }
                  onClick={() => {
                    router.push("/account");
                  }}
                >
                  Account
                </DropdownMenuItem>
                <DropdownMenuItem
                  className={
                    "rounded-sm text-neutral-900 transition-colors duration-200 active:bg-neutral-200 lg:hover:bg-neutral-200 lg:active:bg-neutral-300 dark:text-neutral-100 dark:active:bg-[#424242] dark:lg:hover:bg-[#3B3B3B] dark:lg:active:bg-[#424242]"
                  }
                  onClick={() => {
                    supabase.auth.signOut().then(() => {
                      router.refresh();
                    });
                  }}
                >
                  Sign out
                </DropdownMenuItem>
              </>
            )}
            {!user && (
              <DropdownMenuItem
                onClick={() => {
                  router.push("/login");
                }}
              >
                Sign in
              </DropdownMenuItem>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      )}
    </>
  );
}
