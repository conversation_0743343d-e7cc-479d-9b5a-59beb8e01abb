import { Avatar, Text } from "@mantine/core";
import { createClient } from "@/utils/supabase/server";

export default async function ProfileMenuAvatarAndEmail() {
  const supabase = await createClient();
  let username = "";
  let avatarUrl = "";

  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (user) {
    const { data } = await supabase
      .from("profiles")
      .select("username,avatar_url")
      .eq("id", user?.id);

    if (data && data[0]) {
      username = data[0]?.username ? data[0].username : "";
    }

    if (data) {
      avatarUrl = user.user_metadata.avatar_url;
    }
  }
  return (
    <>
      <Text className={"hidden lg:block"}>
        {username ? username : user?.email}
      </Text>

      <Avatar alt={"Profile picture"} src={avatarUrl} />
    </>
  );
}
