import CardForm from "@/components/CardForm-components/CardForm";
import FormInput from "@/components/CardForm-components/FormInput";
import FormRow from "@/components/CardForm-components/FormRow";
import AbilityInput from "@/components/AbilityInput";
import Card from "@/components/CardElement-components/Card";
import <PERSON><PERSON><PERSON><PERSON> from "@/app/one-piece/character/Card<PERSON>haracter";
import ExpandableCard from "@/components/ExpandableCard";
import React, { Suspense } from "react";
import FormUploadAndDownloadSection from "@/components/CardForm-components/FormUploadAndDownloadSection";
import TriggerInput from "@/components/CardForm-components/TriggerInput";
import ForegroundBackgroundImgUpload from "@/components/CardForm-components/ForegroundBackgroundImgUpload";
import PaddleRetain from "@/components/PaddleRetain";
import PrintReadyInput from "@/components/CardForm-components/PrintReadyInput";
import AsyncDownloadButton from "@/components/CardElement-components/AsyncDownloadButton";
import { createClient } from "@/utils/supabase/server";
import getSubscriptionNameAndStatus from "@/helpers/getSubscriptionNameAndStatus";
import CustomLoader from "@/components/CustomLoader";
import NavigationTransition from "@/components/NavigationTransition";
import { Metadata } from "next";
import PageLoader from "@/components/PageLoader";

export const metadata: Metadata = {
  title: "Create One Piece Character Card - Ultimate TCG Card Maker",
  description:
    "Create Character cards, customize their abilities and power levels. Whether it's Luffy, Zoro, or Nami, your imagination sets the limit.",
  alternates: {
    canonical: "https://ultimatetcgcm.com/one-piece/character",
  },
  openGraph: {
    title: "Create One Piece Character Card - Ultimate TCG Card Maker",
    type: "website",
    url: "https://ultimatetcgcm.com/one-piece/character",
    description:
      "Create Character cards, customize their abilities and power levels. Whether it's Luffy, Zoro, or Nami, your imagination sets the limit.",
  },
};
export default async function Page() {
  const supabase = await createClient();
  const { data: user } = await supabase.auth.getUser();

  const subscription = await getSubscriptionNameAndStatus();
  return (
    <>
      <div
        className={`relative mb-1.5 flex flex-col justify-between xl:mb-40 xl:flex-row`}
      >
        <NavigationTransition fallback={<PageLoader />}>
          <CardForm formFor={"character"}>
            <div className={`font-geist-sans flex flex-col gap-7`}>
              <h1 className={"text-xl"}>One Piece Character Card</h1>
              <FormUploadAndDownloadSection
                uploadFor={"character"}
                subscription={subscription}
              />
              <div className={"flex flex-col gap-7 sm:flex-row lg:gap-36"}>
                <FormRow>
                  <FormInput type={"character-border-type"} />
                  <FormInput type={"color"} />
                  <FormInput type={"attribute"} />
                  <FormInput type={"name"} />
                  <FormInput type={"card-type"} />
                  <FormInput type={"cost"} />
                  <FormInput type={"power"} />
                  <TriggerInput />
                </FormRow>
                <FormRow>
                  <FormInput type={"counter"} />
                  <FormInput type={"set"} />
                  <FormInput type={"rarity"} />
                  <FormInput type={"rarity2"} />
                  <FormInput type={"card-num"} />
                  <FormInput type={"print-wave"} />
                  <FormInput type={"artist"} />
                  <FormInput
                    type={"leaderBorderEnabled"}
                    cardKind={"character"}
                  />
                  <FormInput type={"aaStar"} cardKind={"character"} />
                  <PrintReadyInput subscription={subscription} />
                  <ForegroundBackgroundImgUpload cardType={"character"} />
                  <AsyncDownloadButton
                    cardType={"character"}
                    user={user}
                    subscription={subscription}
                  />
                </FormRow>
              </div>
              <AbilityInput backgroundToggle={true} />
            </div>
          </CardForm>

          <ExpandableCard>
            <Card
              maxWRem={28}
              className={"mr:auto w-full md:mt-7 md:mr-7 lg:ml-auto"}
            >
              <Suspense
                fallback={
                  <div className={"flex justify-center"}>
                    <CustomLoader className={"mx-full! my-full! block!"} />
                  </div>
                }
              >
                <CardCharacter subscription={subscription} />
              </Suspense>
            </Card>
          </ExpandableCard>
        </NavigationTransition>
      </div>
      {process.env.NEXT_PUBLIC_PADDLE_ENVIRONMENT === "production" && (
        <PaddleRetain />
      )}
    </>
  );
}
