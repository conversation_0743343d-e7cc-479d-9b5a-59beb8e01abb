import CardBackground from "@/components/CardElement-components/card-elements/CardBackground";
import CardBorder from "@/components/CardElement-components/card-elements/CardBorder";

import CardArtistText from "@/components/CardElement-components/card-elements/CardArtistText";
import CardMadeWith from "@/components/CardElement-components/card-elements/CardMadeWith";

import CardWaterMark from "@/components/CardElement-components/CardWaterMark";
import React from "react";

import Card<PERSON>oreground from "@/components/CardElement-components/card-elements/CardForeground";
import { Subscription } from "@/types";
import CardKind from "@/components/CardElement-components/card-elements/CardKind";
import CardDonText from "@/components/CardElement-components/card-elements/CardDonText";
import CardAbilityDon from "@/components/CardElement-components/card-elements/CardAbilityDon";

export default function CardDon({
  subscription,
}: {
  subscription: Subscription;
}) {
  return (
    <>
      <CardBackground cardType={"don"} quality={25} />

      <CardForeground />
      <CardWaterMark subscription={subscription} />
      <CardBorder cardType={"don"} quality={25} />
      <CardKind cardType={"don"} />
      <CardDonText />
      <CardAbilityDon />
      <CardArtistText cardType={"don"} />
      <CardMadeWith cardType={"don"} />
    </>
  );
}
