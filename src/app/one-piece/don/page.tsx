import CardForm from "@/components/CardForm-components/CardForm";
import FormInput from "@/components/CardForm-components/FormInput";
import FormRow from "@/components/CardForm-components/FormRow";

import Card from "@/components/CardElement-components/Card";

import React, { Suspense } from "react";

import CardDon from "@/app/one-piece/don/CardDon";
import FormUploadAndDownloadSection from "@/components/CardForm-components/FormUploadAndDownloadSection";
import { Metadata } from "next";
import ForegroundBackgroundImgUpload from "@/components/CardForm-components/ForegroundBackgroundImgUpload";

import PrintReadyInput from "@/components/CardForm-components/PrintReadyInput";
import AsyncDownloadButton from "@/components/CardElement-components/AsyncDownloadButton";
import { createClient } from "@/utils/supabase/server";
import getSubscriptionNameAndStatus from "@/helpers/getSubscriptionNameAndStatus";
import ExpandableCard from "@/components/ExpandableCard";
import DonAbilityInput from "@/components/DonAbilityInput";
import CustomLoader from "@/components/CustomLoader";
import PageLoader from "@/components/PageLoader";
import NavigationTransition from "@/components/NavigationTransition";
export const metadata: Metadata = {
  title: "Create One Piece Don Card - Ultimate TCG Card Maker",
  description:
    "Create Don cards and unleash your inner artist by crating amazing full art that suits your play style.",
  alternates: {
    canonical: "https://ultimatetcgcm.com/one-piece/don",
  },
  openGraph: {
    title: "Create One Piece Don Card - Ultimate TCG Card Maker",
    type: "website",
    url: "https://ultimatetcgcm.com/one-piece/don",
    description:
      "Create Don cards and unleash your inner artist by crating amazing full art that suits your play style.",
  },
};
export default async function Page() {
  const supabase = await createClient();
  const { data: user } = await supabase.auth.getUser();
  const subscription = await getSubscriptionNameAndStatus();
  return (
    <div
      className={`relative mb-1.5 flex flex-col justify-between md:flex-row`}
    >
      <NavigationTransition fallback={<PageLoader />}>
        <CardForm formFor={"don"}>
          <div className={"flex flex-col justify-start gap-7"}>
            <h1 className={"text-xl"}>One Piece Don Card</h1>
            <FormUploadAndDownloadSection
              uploadFor={"don"}
              subscription={subscription}
            />

            <div className={"flex flex-col gap-7 sm:flex-row sm:gap-36"}>
              <FormRow>
                <PrintReadyInput subscription={subscription} />
                <FormInput type={"donAbility"} />
                <FormInput type={"don-text"} />
                <FormInput type={"don-power"} />
                <FormInput type={"artist"} />
                <DonAbilityInput />
                <ForegroundBackgroundImgUpload cardType={"don"} />
                <AsyncDownloadButton
                  cardType={"don"}
                  subscription={subscription}
                  user={user}
                />
              </FormRow>
            </div>
          </div>
        </CardForm>
        <ExpandableCard>
          <Card
            maxWRem={28}
            className={"mr:auto w-full md:mt-7 md:mr-7 lg:ml-auto"}
          >
            <Suspense
              fallback={
                <div className={"flex justify-center"}>
                  <CustomLoader className={"mx-full! my-full! block!"} />
                </div>
              }
            >
              <CardDon subscription={subscription} />
            </Suspense>
          </Card>
        </ExpandableCard>
      </NavigationTransition>
    </div>
  );
}
