"use client";

import * as Sen<PERSON> from "@sentry/nextjs";
import Error from "next/error";
import { useEffect } from "react";

export default function ErrorPage({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    Sentry.captureException(error);
  }, [error]);

  return (
    <div className="flex h-[80vh] flex-col items-center justify-center">
      <h2 className="text-xl font-bold">Something went wrong!</h2>
      <p className="mt-2 text-gray-600 dark:!text-gray-400">
        We&apos;ve logged the error and will fix it soon.
      </p>
      <div className="mt-4 flex gap-4">
        <button
          onClick={() => {
            reset();
          }}
          className="rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
        >
          Try again
        </button>
      </div>
    </div>
  );
}
