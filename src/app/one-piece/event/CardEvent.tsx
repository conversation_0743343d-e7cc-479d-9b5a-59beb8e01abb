import CardBackground from "@/components/CardElement-components/card-elements/CardBackground";
import CardBorder from "@/components/CardElement-components/card-elements/CardBorder";

import CardCost from "@/components/CardElement-components/card-elements/CardCost";

import CardPrintWave from "@/components/CardElement-components/card-elements/CardPrintWave";
import CardRarity from "@/components/CardElement-components/card-elements/CardRarity";

import CardName from "@/components/CardElement-components/card-elements/CardName";
import CardKind from "@/components/CardElement-components/card-elements/CardKind";
import CardType from "@/components/CardElement-components/card-elements/CardType";

import CardSetAndNum from "@/components/CardElement-components/card-elements/CardSetAndNum";
import CardRarityText from "@/components/CardElement-components/card-elements/CardRarityText";
import CardPrintWaveText from "@/components/CardElement-components/card-elements/CardPrintWaveText";
import CardArtistText from "@/components/CardElement-components/card-elements/CardArtistText";
import CardMadeWith from "@/components/CardElement-components/card-elements/CardMadeWith";

import CardAbilityAndTrigger from "@/components/CardElement-components/card-elements/CardAbilityAndTrigger";

import React from "react";
import CardWaterMark from "@/components/CardElement-components/CardWaterMark";

import CardColorWheel from "@/components/CardElement-components/card-elements/CardColorWheel";

import CardForeground from "@/components/CardElement-components/card-elements/CardForeground";
import { Subscription } from "@/types";

export default function CardEvent({
  subscription,
}: {
  subscription: Subscription;
}) {
  return (
    <>
      <CardBackground cardType={"event"} quality={25} />

      <CardForeground />
      <CardWaterMark subscription={subscription} />
      <CardBorder cardType={"event"} quality={25} />
      <CardCost eventOrStage={true} event={true} />

      <CardPrintWave quality={1} cardKindRoute={"event"} />
      <CardRarity quality={1} cardKindRoute={"event"} />
      <CardAbilityAndTrigger
        abilityBackGround={false}
        triggerQuality={1}
        client={true}
        cardType={"event"}
      />
      <CardName cardType={"event"} />
      <CardKind cardType={"event"} />
      <CardType cardType={"event"} />

      <CardSetAndNum cardType={"event"} />
      <CardRarityText cardType={"event"} client={true} />
      <CardPrintWaveText cardType={"event"} client={true} />
      <CardArtistText cardType={"event"} />
      <CardMadeWith cardType={"event"} />
      <CardColorWheel cardKind={"event"} />
    </>
  );
}
