import CardForm from "@/components/CardForm-components/CardForm";
import FormInput from "@/components/CardForm-components/FormInput";
import FormRow from "@/components/CardForm-components/FormRow";
import AbilityInput from "@/components/AbilityInput";
import Card from "@/components/CardElement-components/Card";

import React, { Suspense } from "react";
import CardEvent from "@/app/one-piece/event/CardEvent";
import FormUploadAndDownloadSection from "@/components/CardForm-components/FormUploadAndDownloadSection";
import TriggerInput from "@/components/CardForm-components/TriggerInput";
import { Metadata } from "next";
import ForegroundBackgroundImgUpload from "@/components/CardForm-components/ForegroundBackgroundImgUpload";

import PrintReadyInput from "@/components/CardForm-components/PrintReadyInput";
import AsyncDownloadButton from "@/components/CardElement-components/AsyncDownloadButton";
import { createClient } from "@/utils/supabase/server";
import getSubscriptionNameAndStatus from "@/helpers/getSubscriptionNameAndStatus";
import ExpandableCard from "@/components/ExpandableCard";
import CustomLoader from "@/components/CustomLoader";
import PageLoader from "@/components/PageLoader";
import NavigationTransition from "@/components/NavigationTransition";
export const metadata: Metadata = {
  title: "Create One Piece Event Card - Ultimate TCG Card Maker",
  description:
    "Create Event cards, recreate iconic One Piece events or invent new scenarios.",
  alternates: {
    canonical: "https://ultimatetcgcm.com/one-piece/event",
  },
  openGraph: {
    title: "Create One Piece Event Card - Ultimate TCG Card Maker",
    type: "website",
    url: "https://ultimatetcgcm.com/one-piece/event",
    description:
      "Create Event cards, recreate iconic One Piece events or invent new scenarios.",
  },
};
export default async function Page() {
  const supabase = await createClient();
  const { data: user } = await supabase.auth.getUser();
  const subscription = await getSubscriptionNameAndStatus();
  return (
    <div
      className={`relative mb-1.5 flex flex-col justify-between xl:mb-40 xl:flex-row`}
    >
      <NavigationTransition fallback={<PageLoader />}>
        <CardForm formFor={"event"}>
          <div className={"flex flex-col justify-start gap-7"}>
            <h1 className={"text-xl"}>One Piece Event Card</h1>
            <FormUploadAndDownloadSection
              uploadFor={"event"}
              subscription={subscription}
            />

            <div className={"flex flex-col gap-7 sm:flex-row sm:gap-36"}>
              <FormRow>
                <FormInput type={"event-border-type"} />
                <FormInput type={"color"} />
                <FormInput type={"name"} />
                <FormInput type={"card-type"} />
                <FormInput type={"cost"} />
                <FormInput type={"set"} />
                <FormInput type={"foilBorder"} cardKind={"event"} />
                <FormInput type={"aaStar"} />
                <PrintReadyInput subscription={subscription} />
                <TriggerInput />
              </FormRow>
              <FormRow>
                <FormInput type={"rarity"} />
                <FormInput type={"card-num"} />
                <FormInput type={"print-wave"} />
                <FormInput type={"artist"} />

                <ForegroundBackgroundImgUpload cardType={"event"} />
                <AsyncDownloadButton
                  cardType={"event"}
                  subscription={subscription}
                  user={user}
                />
              </FormRow>
            </div>

            <AbilityInput backgroundToggle={true} />
          </div>
        </CardForm>

        <ExpandableCard>
          <Card
            maxWRem={28}
            className={"mr:auto w-full md:mt-7 md:mr-7 lg:ml-auto"}
          >
            <Suspense
              fallback={
                <div className={"flex justify-center"}>
                  <CustomLoader className={"mx-full! my-full! block!"} />
                </div>
              }
            >
              <CardEvent subscription={subscription} />
            </Suspense>
          </Card>
        </ExpandableCard>
      </NavigationTransition>
    </div>
  );
}
