import React from "react";
import OnePieceLayoutShadcn from "@/app/one-piece/OnePieceLayoutShadcn";
import ServerStatus from "@/components/ServerStatus";
import { ServerStatusProvider } from "@/context/ServerStatusContext";
import { checkServerHealth } from "@/lib/serverHealth";
import "../onepiece-styles.css";

export default async function Layout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Check server health on the server-side
  const isServerOnline = await checkServerHealth();

  return (
    <OnePieceLayoutShadcn>
      <ServerStatusProvider initialServerStatus={isServerOnline}>
        <div className={"flex flex-col"}>
          <ServerStatus />
          {children}
        </div>
      </ServerStatusProvider>
    </OnePieceLayoutShadcn>
  );
}
