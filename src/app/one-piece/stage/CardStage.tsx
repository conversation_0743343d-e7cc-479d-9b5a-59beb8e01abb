import CardBackground from "@/components/CardElement-components/card-elements/CardBackground";
import CardBorder from "@/components/CardElement-components/card-elements/CardBorder";

import CardCost from "@/components/CardElement-components/card-elements/CardCost";

import CardPrintWave from "@/components/CardElement-components/card-elements/CardPrintWave";
import CardRarity from "@/components/CardElement-components/card-elements/CardRarity";

import CardName from "@/components/CardElement-components/card-elements/CardName";
import CardKind from "@/components/CardElement-components/card-elements/CardKind";
import CardType from "@/components/CardElement-components/card-elements/CardType";

import CardSetAndNum from "@/components/CardElement-components/card-elements/CardSetAndNum";
import CardRarityText from "@/components/CardElement-components/card-elements/CardRarityText";
import CardPrintWaveText from "@/components/CardElement-components/card-elements/CardPrintWaveText";
import CardArtistText from "@/components/CardElement-components/card-elements/CardArtistText";
import CardMadeWith from "@/components/CardElement-components/card-elements/CardMadeWith";

import CardAbilityAndTrigger from "@/components/CardElement-components/card-elements/CardAbilityAndTrigger";

import React from "react";
import CardWaterMark from "@/components/CardElement-components/CardWaterMark";

import CardColorWheel from "@/components/CardElement-components/card-elements/CardColorWheel";

import CardForeground from "@/components/CardElement-components/card-elements/CardForeground";
import { Subscription } from "@/types";

export default function CardStage({
  subscription,
}: {
  subscription: Subscription;
}) {
  return (
    <>
      <CardBackground cardType={"stage"} quality={25} />

      <CardForeground />
      <CardWaterMark subscription={subscription} />
      <CardBorder cardType={"stage"} quality={25} />
      <CardCost eventOrStage={true} />

      <CardPrintWave quality={1} cardKindRoute={"stage"} />
      <CardRarity quality={1} cardKindRoute={"stage"} />
      <CardAbilityAndTrigger
        abilityBackGround={false}
        triggerQuality={1}
        client={true}
        cardType={"stage"}
      />
      <CardName cardType={"stage"} />
      <CardKind cardType={"stage"} />
      <CardType cardType={"stage"} />

      <CardSetAndNum cardType={"stage"} />
      <CardRarityText cardType={"stage"} client={true} />
      <CardPrintWaveText cardType={"stage"} client={true} />
      <CardArtistText cardType={"stage"} />
      <CardMadeWith cardType={"stage"} />
      <CardColorWheel cardKind={"stage"} />
    </>
  );
}
