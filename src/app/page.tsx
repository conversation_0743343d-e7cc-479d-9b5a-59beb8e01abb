import MainPageShell from "@/app/MainPageShell";
import ScrollToTop from "@/components/ScrollToTop";
import { Metadata } from "next";
import PaddleRetain from "@/components/PaddleRetain";
import MainPageHeroSection from "@/components/Main-page-components/MainPageHeroSection";
import MainPageContent from "@/components/Main-page-components/MainPageContent";
import MainPageFooter from "@/components/Main-page-components/MainPageFooter";

export const metadata: Metadata = {
  title: "One Piece TCG Card Maker | Ultimate TCG Card Maker",
  description:
    "One Piece TCG Card Maker - create custom One Piece TCG cards such as <PERSON>, Leader, Even, Stage, Don and download them with our One Piece TCG Card Maker.",

  alternates: {
    canonical: "https://ultimatetcgcm.com/",
  },
  metadataBase: new URL("https://ultimatetcgcm.com/"),
  openGraph: {
    title: "One Piece TCG Card Maker - Ultimate TCG Card Maker",
    type: "website",
    url: "https://ultimatetcgcm.com/",
    description:
      "One Piece TCG Card Maker - create custom One Piece TCG cards such as <PERSON>, Leader, Even, Stage, Don and download them with our One Piece TCG Card Maker.",
  },
};
export default function Home() {
  return (
    <>
      <MainPageShell>
        <ScrollToTop />
        <div className="font-geist-sans relative flex flex-col items-center font-light">
          <MainPageHeroSection />
          <MainPageContent />
        </div>
        <MainPageFooter />
      </MainPageShell>
      {process.env.NEXT_PUBLIC_PADDLE_ENVIRONMENT === "production" && (
        <PaddleRetain />
      )}
    </>
  );
}
