"use client";
import Link from "next/link";
import React from "react";

export default function PolicyLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="flex min-h-screen flex-col">
      {/* Header - Fixed height at the top */}
      <header className="bg-background flex h-[60px] shrink-0 flex-row items-center justify-center border-b px-4 md:justify-start dark:border-[#424242] dark:bg-[#242424]">
        <Link
          href={"/"}
          className="rounded-sm px-2 py-1 text-center text-neutral-900 transition-colors duration-200 active:bg-neutral-200 lg:justify-self-start lg:text-start lg:hover:bg-neutral-200 lg:active:bg-neutral-300 dark:text-neutral-100 dark:active:bg-[#2E2E2E] dark:lg:hover:bg-[#2E2E2E] dark:lg:active:bg-[#3B3B3B]"
        >
          Ultimate TCG Card Maker
        </Link>
      </header>

      {/* Main content */}
      <main className="flex flex-1 flex-col items-center bg-neutral-100 p-4 dark:bg-neutral-900">
        {children}
      </main>
    </div>
  );
}
