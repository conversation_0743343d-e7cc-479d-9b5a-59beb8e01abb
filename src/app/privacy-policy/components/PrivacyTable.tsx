"use client";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import React from "react";

export default function PrivacyTable() {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead className={"whitespace-normal"}>Category</TableHead>
          <TableHead className={"whitespace-normal"}>Examples</TableHead>
          <TableHead className={"whitespace-normal"}>Collected</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        <TableRow>
          <TableCell className={"whitespace-normal"}>A. Identifiers</TableCell>
          <TableCell className={"whitespace-normal"}>
            Contact details, such as real name, alias, postal address, telephone
            or mobile contact number, unique personal identifier, online
            identifier, Internet Protocol address, email address, and account
            name
          </TableCell>
          <TableCell className={"whitespace-normal"}>NO</TableCell>
        </TableRow>
        <TableRow>
          <TableCell className={"whitespace-normal"}>
            B. Protected classification characteristics under state or federal
            law
          </TableCell>
          <TableCell className={"whitespace-normal"}>
            Gender and date of birth
          </TableCell>
          <TableCell className={"whitespace-normal"}>NO</TableCell>
        </TableRow>
        <TableRow>
          <TableCell className={"whitespace-normal"}>
            C. Commercial information
          </TableCell>
          <TableCell className={"whitespace-normal"}>
            Transaction information, purchase history, financial details, and
            payment information
          </TableCell>
          <TableCell className={"whitespace-normal"}>NO</TableCell>
        </TableRow>
        <TableRow>
          <TableCell className={"whitespace-normal"}>
            D. Biometric information
          </TableCell>
          <TableCell className={"whitespace-normal"}>
            Fingerprints and voiceprints
          </TableCell>
          <TableCell className={"whitespace-normal"}>NO</TableCell>
        </TableRow>
        <TableRow>
          <TableCell className={"whitespace-normal"}>
            E. Internet or other similar network activity
          </TableCell>
          <TableCell className={"whitespace-normal"}>
            Browsing history, search history, online behavior, interest data,
            and interactions with our and other websites, applications, systems,
            and advertisements
          </TableCell>
          <TableCell className={"whitespace-normal"}>NO</TableCell>
        </TableRow>
        <TableRow>
          <TableCell className={"whitespace-normal"}>
            F. Geolocation data
          </TableCell>
          <TableCell className={"whitespace-normal"}>Device location</TableCell>
          <TableCell className={"whitespace-normal"}>NO</TableCell>
        </TableRow>
        <TableRow>
          <TableCell className={"whitespace-normal"}>
            G. Audio, electronic, visual, thermal, olfactory, or similar
            information
          </TableCell>
          <TableCell className={"whitespace-normal"}>
            Images and audio, video or call recordings created in connection
            with our business activities
          </TableCell>
          <TableCell className={"whitespace-normal"}>NO</TableCell>
        </TableRow>
        <TableRow>
          <TableCell className={"whitespace-normal"}>
            H. Professional or employment-related information
          </TableCell>
          <TableCell className={"whitespace-normal"}>
            Business contact details in order to provide you our Services at a
            business level or job title, work history, and professional
            qualifications if you apply for a job with us
          </TableCell>
          <TableCell className={"whitespace-normal"}>NO</TableCell>
        </TableRow>
        <TableRow>
          <TableCell className={"whitespace-normal"}>
            I. Education Information
          </TableCell>
          <TableCell className={"whitespace-normal"}>
            Student records and directory information
          </TableCell>
          <TableCell className={"whitespace-normal"}>NO</TableCell>
        </TableRow>
        <TableRow>
          <TableCell className={"whitespace-normal"}>
            J. Inferences drawn from collected personal information
          </TableCell>
          <TableCell className={"whitespace-normal"}>
            Inferences drawn from any of the collected personal information
            listed above to create a profile or summary about, for example, an
            individual&apos;s preferences and characteristics
          </TableCell>
          <TableCell className={"whitespace-normal"}>NO</TableCell>
        </TableRow>
        <TableRow>
          <TableCell className={"whitespace-normal"}>
            K. Sensitive personal Information{" "}
          </TableCell>
          <TableCell className={"whitespace-normal"}></TableCell>
          <TableCell className={"whitespace-normal"}>NO</TableCell>
        </TableRow>
      </TableBody>
    </Table>
  );
}
