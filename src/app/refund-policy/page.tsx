import { List, ListItem } from "@/components/ui/list";
import { Title } from "@/components/ui/title";
import React from "react";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Refund Policy - Ultimate TCG Card Maker",
  description: "Refund Policy",
  alternates: {
    canonical: "https://ultimatetcgcm.com/refund-policy",
  },
  openGraph: {
    title: "Refund Policy - Ultimate TCG Card Maker",
    type: "website",
    url: "https://ultimatetcgcm.com/refund-policy",
    description: "Refund Policy",
  },
};
export default function Page() {
  return (
    <div className={"flex max-w-4xl flex-col items-start gap-4"}>
      <Title order={1}>Refund Policy</Title>
      <p>
        <span className={"font-bold"}>
          Thank you for choosing Ultimate TCG Card Maker!
        </span>
        <br />
        We strive to empower your creativity and hope you enjoy designing your
        custom TCG cards. This refund policy outlines the conditions under which
        a refund may be issued.
      </p>
      <p>
        <span className={"font-bold"}>
          Refunds are generally not available due to the nature of our digital
          service.
        </span>
        <br />
        Once you activate your subscription, you are granted access to the full
        functionality of Ultimate TCG Card Maker. SaaS products differ from
        physical goods in that they cannot be easily returned.
      </p>
      <p>
        However, we understand that unforeseen circumstances may arise. We will
        consider issuing a full or partial refund in the following situations:
      </p>
      <List listStyleType={"disc"}>
        <ListItem>
          Technical Issues: If you encounter a demonstrably critical and
          unresolved technical issue that prevents the core functionality of
          Ultimate TCG Card Maker from working as advertised, and our support
          team is unable to resolve the issue within a reasonable timeframe. To
          request a refund in this case, you will need to provide detailed
          information about the problem, including screenshots, error messages,
          and any communication with our support team regarding the issue.
        </ListItem>
      </List>
      <p className={"font-bold"}>Please note:</p>
      <List listStyleType={"disc"}>
        <ListItem>
          Refunds will only be considered for requests submitted within 7 days
          of your initial subscription purchase.
        </ListItem>
        <ListItem>
          Refunds will not be granted for:
          <List withPadding listStyleType={"circle"}>
            <ListItem>Change of mind</ListItem>
            <ListItem>
              Cancellation of service after the initial grace period (if
              applicable)
            </ListItem>
            <ListItem>
              Issues arising from unsuitable device specifications or
              compatibility
            </ListItem>
            <ListItem>
              Limited functionality due to exceeding usage limits
            </ListItem>
          </List>
        </ListItem>
      </List>
      <Title order={4}>Requesting a Refund:</Title>
      <p>
        If you believe you qualify for a refund under the conditions outlined
        above, please contact our support team at{" "}
        <span className={"font-bold"}><EMAIL></span> with a
        detailed explanation of the issue and any supporting documentation. We
        will review your request and respond within 7 business days.
      </p>
      <Title order={4}>Issuing Refunds:</Title>
      <p>
        If your refund request is approved, we will credit the original payment
        method used for your purchase. Please allow 7 business days for the
        refund to appear in your account.
      </p>
      <p className={"font-bold"}>
        We value your business and hope this policy provides clarity on our
        Ultimate TCG Card Maker refund process. If you have any questions,
        please don&apos;t hesitate to contact us.
      </p>
    </div>
  );
}
