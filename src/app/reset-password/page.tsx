import ResetForm from "@/app/reset-password/ResetForm";
import { createClient } from "@/utils/supabase/server";
import { permanentRedirect } from "next/navigation";

export default async function Page() {
  const supabase = await createClient();
  const session = await supabase.auth.getSession();
  if (!session.data.session) {
    permanentRedirect("/login");
    return null;
  }

  // Get user to check authentication provider
  const {
    data: { user },
  } = await supabase.auth.getUser();

  // Redirect Google OAuth users away from password reset

  if (
    !(
      user?.identities &&
      user?.identities?.length === 1 &&
      user?.identities?.some((identity) => identity.provider === "email")
    )
  ) {
    permanentRedirect("/one-piece/character");
    return null;
  }

  return <ResetForm />;
}
