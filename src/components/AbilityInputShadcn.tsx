// "use client";
//
// import { TextStyle } from "@tiptap/extension-text-style";
// import { Bold } from "@tiptap/extension-bold";
// import { useDispatch, useSelector } from "react-redux";
// import React, { useEffect, useRef, useState } from "react";
// import {
//   onAbility,
//   onAbilityDropShadow,
//   onAbilityTextSize,
//   onDropShadow,
//   onEditorState,
//   onInputField,
// } from "@/store/formSlice";
// import { storeState } from "@/store/store";
// import { StarterKit } from "@tiptap/starter-kit";
// import { Underline } from "@tiptap/extension-underline";
// import Highlight from "@tiptap/extension-highlight";
// import { TextAlign } from "@tiptap/extension-text-align";
// import { Color } from "@tiptap/extension-color";
// import {
//   BubbleMenu,
//   Editor,
//   EditorContent,
//   SingleCommands,
//   useEditor,
// } from "@tiptap/react";
// import { useGetStoreState } from "@/helpers/useGetStoreState";
// import { Italic } from "@tiptap/extension-italic";
// import { cn } from "@/lib/utils";
// import {
//   Dialog,
//   DialogContent,
//   DialogDescription,
//   DialogHeader,
//   DialogTitle,
//   DialogTrigger,
//   DialogClose,
// } from "@/components/ui/dialog";
// import {
//   Tooltip,
//   TooltipContent,
//   TooltipProvider,
//   TooltipTrigger,
// } from "@/components/ui/tooltip";
// import {
//   DropdownMenu,
//   DropdownMenuContent,
//   DropdownMenuGroup,
//   DropdownMenuItem,
//   DropdownMenuLabel,
//   DropdownMenuSeparator,
//   DropdownMenuTrigger,
// } from "@/components/ui/dropdown-menu";
// import { Button } from "@/components/ui/button";
// import { Paintbrush, Check, Palette } from "lucide-react";
// import { ColorPicker } from "antd";
//
// const CustomParagraph = TextStyle.extend({
//   renderHTML({ HTMLAttributes }) {
//     if (HTMLAttributes.class === "orange-ability") {
//       return [
//         "span",
//         { class: "orange-ability-container" },
//         [
//           "span",
//           { class: "orange-ability-shadow" },
//           ["span", { ...HTMLAttributes }, 0],
//         ],
//       ];
//     } else if (HTMLAttributes.class === "black-ability") {
//       return [
//         "span",
//         { class: "black-ability-container" },
//         [
//           "span",
//           { class: "black-ability-shadow" },
//           ["span", { ...HTMLAttributes }, 0],
//         ],
//       ];
//     } else if (HTMLAttributes.class === "trigger-ability") {
//       return [
//         "span",
//         { class: "trigger-ability-container" },
//         [
//           "span",
//           { class: "trigger-ability-shadow" },
//           ["span", { ...HTMLAttributes }, 0],
//         ],
//       ];
//     } else {
//       return ["span", { ...HTMLAttributes }, 0];
//     }
//   },
//   addAttributes() {
//     return {
//       color: {
//         // … and customize the HTML rendering.
//         renderHTML: (attributes) => {
//           const color = attributes.color;
//           const classColor =
//             color === "#2F77B3"
//               ? "blue-ability"
//               : color === "#d94880"
//                 ? "pink-ability"
//                 : color === "#DC8535"
//                   ? "orange-ability"
//                   : color === "#ba212f"
//                     ? "red-ability"
//                     : color === "#f8ed70"
//                       ? "trigger-ability"
//                       : color === "#FFFFFF"
//                         ? "white-ability"
//                         : color === "#000000"
//                           ? "black-ability"
//                           : "";
//           return {
//             class: classColor,
//           };
//         },
//       },
//     };
//   },
// });
//
// const CustomBold = Bold.extend({
//   name: "customBold",
//   renderHTML({ HTMLAttributes }) {
//     // Original:
//     // return ['strong', HTMLAttributes, 0]
//     return ["b", HTMLAttributes, 0];
//   },
// });
// const CustomItalic = Italic.extend({
//   name: "customItalic",
//   renderHTML({ HTMLAttributes }) {
//     const attributes = HTMLAttributes;
//     attributes.class = `font-one-piece-italic-bold text-[0.94em]`;
//     // Original:
//     // return ['strong', HTMLAttributes, 0]
//     return ["em", attributes, 0];
//   },
// });
//
// // Custom RichTextEditor components
// const RichTextEditor = {
//   Root: ({
//     children,
//     editor,
//     className,
//     id,
//   }: {
//     children: React.ReactNode;
//     editor: Editor | null;
//     className?: string;
//     id?: string;
//   }) => {
//     return (
//       <div
//         className={cn(
//           "border-input flex flex-col overflow-hidden rounded-md border",
//           className,
//         )}
//         id={id}
//       >
//         {children}
//       </div>
//     );
//   },
//   Toolbar: ({
//     children,
//     className,
//   }: {
//     children: React.ReactNode;
//     className?: string;
//   }) => {
//     return (
//       <div
//         className={cn(
//           "border-input bg-muted/50 flex flex-wrap gap-1 border-b p-1",
//           className,
//         )}
//       >
//         {children}
//       </div>
//     );
//   },
//   ControlsGroup: ({
//     children,
//     className,
//   }: {
//     children: React.ReactNode;
//     className?: string;
//   }) => {
//     return <div className={cn("flex items-center", className)}>{children}</div>;
//   },
//   Control: ({
//     children,
//     onClick,
//     className,
//     disabled,
//     title,
//     "aria-label": ariaLabel,
//   }: {
//     children: React.ReactNode;
//     onClick?: () => void;
//     className?: string;
//     disabled?: boolean;
//     title?: string;
//     "aria-label"?: string;
//   }) => {
//     return (
//       <Button
//         variant="ghost"
//         size="sm"
//         onClick={onClick}
//         className={cn("h-8 px-2 text-xs", className)}
//         disabled={disabled}
//         title={title}
//         aria-label={ariaLabel}
//       >
//         {children}
//       </Button>
//     );
//   },
//   Bold: ({ editor }: { editor: Editor | null }) => {
//     return (
//       <TooltipProvider>
//         <Tooltip>
//           <TooltipTrigger asChild>
//             <Button
//               variant="ghost"
//               size="sm"
//               onClick={() => editor?.chain().focus().toggleBold().run()}
//               className={cn(
//                 "h-8 px-2 text-xs",
//                 editor?.isActive("bold")
//                   ? "bg-accent text-accent-foreground"
//                   : "",
//               )}
//               aria-label="Bold"
//             >
//               B
//             </Button>
//           </TooltipTrigger>
//           <TooltipContent>Bold</TooltipContent>
//         </Tooltip>
//       </TooltipProvider>
//     );
//   },
//   Italic: ({ editor }: { editor: Editor | null }) => {
//     return (
//       <TooltipProvider>
//         <Tooltip>
//           <TooltipTrigger asChild>
//             <Button
//               variant="ghost"
//               size="sm"
//               onClick={() => editor?.chain().focus().toggleItalic().run()}
//               className={cn(
//                 "h-8 px-2 text-xs",
//                 editor?.isActive("italic")
//                   ? "bg-accent text-accent-foreground"
//                   : "",
//               )}
//               aria-label="Italic"
//             >
//               I
//             </Button>
//           </TooltipTrigger>
//           <TooltipContent>Italic</TooltipContent>
//         </Tooltip>
//       </TooltipProvider>
//     );
//   },
//   Underline: ({ editor }: { editor: Editor | null }) => {
//     return (
//       <TooltipProvider>
//         <Tooltip>
//           <TooltipTrigger asChild>
//             <Button
//               variant="ghost"
//               size="sm"
//               onClick={() => editor?.chain().focus().toggleUnderline().run()}
//               className={cn(
//                 "h-8 px-2 text-xs",
//                 editor?.isActive("underline")
//                   ? "bg-accent text-accent-foreground"
//                   : "",
//               )}
//               aria-label="Underline"
//             >
//               U
//             </Button>
//           </TooltipTrigger>
//           <TooltipContent>Underline</TooltipContent>
//         </Tooltip>
//       </TooltipProvider>
//     );
//   },
//   Strikethrough: ({ editor }: { editor: Editor | null }) => {
//     return (
//       <TooltipProvider>
//         <Tooltip>
//           <TooltipTrigger asChild>
//             <Button
//               variant="ghost"
//               size="sm"
//               onClick={() => editor?.chain().focus().toggleStrike().run()}
//               className={cn(
//                 "h-8 px-2 text-xs",
//                 editor?.isActive("strike")
//                   ? "bg-accent text-accent-foreground"
//                   : "",
//               )}
//               aria-label="Strikethrough"
//             >
//               S
//             </Button>
//           </TooltipTrigger>
//           <TooltipContent>Strikethrough</TooltipContent>
//         </Tooltip>
//       </TooltipProvider>
//     );
//   },
//   ClearFormatting: ({ editor }: { editor: Editor | null }) => {
//     return (
//       <TooltipProvider>
//         <Tooltip>
//           <TooltipTrigger asChild>
//             <Button
//               variant="ghost"
//               size="sm"
//               onClick={() =>
//                 editor?.chain().focus().clearNodes().unsetAllMarks().run()
//               }
//               className={cn("h-8 px-2 text-xs")}
//               aria-label="Clear formatting"
//             >
//               Clear
//             </Button>
//           </TooltipTrigger>
//           <TooltipContent>Clear formatting</TooltipContent>
//         </Tooltip>
//       </TooltipProvider>
//     );
//   },
//   Color: ({
//     color,
//     editor,
//     onClick,
//   }: {
//     color: string;
//     editor: Editor | null;
//     onClick?: () => void;
//   }) => {
//     return (
//       <Button
//         variant="ghost"
//         size="sm"
//         onClick={() => editor?.chain().focus().setColor(color).run()}
//         className={cn("h-8 w-8 rounded-sm p-0")}
//         style={{ backgroundColor: color }}
//         aria-label={`Color: ${color}`}
//       />
//     );
//   },
//   ColorPicker: ({
//     colors,
//     editor,
//   }: {
//     colors: string[];
//     editor: Editor | null;
//   }) => {
//     const [selectedColor, setSelectedColor] = useState<string | null>(null);
//     const [showColorPicker, setShowColorPicker] = useState(false);
//     const [open, setOpen] = useState(false);
//
//     const handleColorSelect = (color: string) => {
//       setSelectedColor(color);
//       if (editor) {
//         editor.chain().focus().setColor(color).run();
//       }
//     };
//
//     const clearColor = () => {
//       setSelectedColor(null);
//       if (editor) {
//         editor.chain().focus().unsetColor().run();
//       }
//     };
//
//     const toggleColorPicker = () => {
//       setShowColorPicker(!showColorPicker);
//     };
//
//     // Group colors into rows of 7
//     const colorRows: string[][] = [];
//     for (let i = 0; i < colors.length; i += 7) {
//       colorRows.push(colors.slice(i, i + 7));
//     }
//
//     return (
//       <DropdownMenu open={open} onOpenChange={setOpen}>
//         <DropdownMenuTrigger asChild>
//           <Button
//             variant="ghost"
//             size="sm"
//             className="relative h-8 w-8 p-1"
//             aria-label="Color picker"
//           >
//             <div
//               className="border-input absolute inset-1 rounded-full border"
//               style={{ backgroundColor: selectedColor || "transparent" }}
//             />
//             {!selectedColor && <Paintbrush className="absolute h-4 w-4" />}
//           </Button>
//         </DropdownMenuTrigger>
//         <DropdownMenuContent className="w-auto p-3" align="start">
//           <div className="space-y-2">
//             {!showColorPicker ? (
//               <>
//                 <DropdownMenuLabel>Color Palette</DropdownMenuLabel>
//                 {colorRows.map((row, rowIndex) => (
//                   <div key={rowIndex} className="flex gap-1">
//                     {row.map((color) => (
//                       <Button
//                         key={color}
//                         variant="ghost"
//                         size="sm"
//                         onClick={() => {
//                           handleColorSelect(color);
//                           setOpen(false);
//                         }}
//                         className="h-8 w-8 rounded-sm p-0"
//                         style={{ backgroundColor: color }}
//                         aria-label={`Color: ${color}`}
//                       />
//                     ))}
//                   </div>
//                 ))}
//               </>
//             ) : (
//               <>
//                 <DropdownMenuLabel>Color Picker</DropdownMenuLabel>
//                 <div className="flex justify-center p-2">
//                   <ColorPicker
//                     showText
//                     value={selectedColor || undefined}
//                     onChange={(color) => {
//                       const hexColor = color.toHexString();
//                       handleColorSelect(hexColor);
//                     }}
//                   />
//                 </div>
//               </>
//             )}
//
//             <DropdownMenuSeparator />
//             <div className="flex justify-between">
//               <DropdownMenuItem onClick={clearColor} className="cursor-pointer">
//                 <Check className="mr-1 h-4 w-4" /> Clear
//               </DropdownMenuItem>
//               <DropdownMenuItem
//                 onClick={toggleColorPicker}
//                 className="cursor-pointer"
//               >
//                 <Palette className="mr-1 h-4 w-4" />
//                 {showColorPicker ? "Palette" : "Picker"}
//               </DropdownMenuItem>
//             </div>
//           </div>
//         </DropdownMenuContent>
//       </DropdownMenu>
//     );
//   },
//   UnsetColor: ({ editor }: { editor: Editor | null }) => {
//     return (
//       <TooltipProvider>
//         <Tooltip>
//           <TooltipTrigger asChild>
//             <Button
//               variant="ghost"
//               size="sm"
//               onClick={() => editor?.chain().focus().unsetColor().run()}
//               className={cn("h-8 px-2 text-xs")}
//               aria-label="Unset color"
//             >
//               Unset
//             </Button>
//           </TooltipTrigger>
//           <TooltipContent>Unset color</TooltipContent>
//         </Tooltip>
//       </TooltipProvider>
//     );
//   },
//   Content: ({
//     className,
//     onChange,
//   }: {
//     className?: string;
//     onChange?: () => void;
//   }) => {
//     return (
//       <div
//         className={cn(
//           "prose prose-sm max-w-none p-3 focus:outline-none",
//           className,
//         )}
//         onChange={onChange}
//       />
//     );
//   },
// };
//
// function DropShadowButton() {
//   const dispatch = useDispatch();
//   const dropShadow = useGetStoreState("dropShadow");
//
//   return (
//     <TooltipProvider>
//       <Tooltip>
//         <TooltipTrigger asChild>
//           <RichTextEditor.Control
//             onClick={() => {
//               dispatch(onDropShadow());
//             }}
//             className={cn(
//               "px-2",
//               dropShadow
//                 ? "bg-neutral-800 text-neutral-50 transition-all duration-100 dark:bg-neutral-200 dark:text-neutral-950"
//                 : "",
//             )}
//           >
//             Text Outline
//           </RichTextEditor.Control>
//         </TooltipTrigger>
//         <TooltipContent>Toggle text outline</TooltipContent>
//       </Tooltip>
//     </TooltipProvider>
//   );
// }
//
// function AbilityDropShadowButton() {
//   const dispatch = useDispatch();
//   const abilityDropShadow = useGetStoreState("abilityDropShadow");
//   return (
//     <TooltipProvider>
//       <Tooltip>
//         <TooltipTrigger asChild>
//           <RichTextEditor.Control
//             onClick={() => {
//               dispatch(onAbilityDropShadow());
//             }}
//             className={cn(
//               "px-2",
//               abilityDropShadow
//                 ? "bg-neutral-800 text-neutral-50 transition-all duration-100 dark:bg-neutral-200 dark:text-neutral-950"
//                 : "",
//             )}
//           >
//             Ability Outline
//           </RichTextEditor.Control>
//         </TooltipTrigger>
//         <TooltipContent>Toggle ability outline</TooltipContent>
//       </Tooltip>
//     </TooltipProvider>
//   );
// }
//
// function Background() {
//   const dispatch = useDispatch();
//   const abilityBackground = useGetStoreState("abilityBackground");
//   return (
//     <TooltipProvider>
//       <Tooltip>
//         <TooltipTrigger asChild>
//           <RichTextEditor.Control
//             onClick={() => {
//               dispatch(onInputField("abilityBackground"));
//             }}
//             className={cn(
//               "px-2",
//               abilityBackground
//                 ? "bg-neutral-800 text-neutral-50 transition-all duration-100 dark:bg-neutral-200 dark:text-neutral-950"
//                 : "",
//             )}
//           >
//             Background
//           </RichTextEditor.Control>
//         </TooltipTrigger>
//         <TooltipContent>Toggle ability background</TooltipContent>
//       </Tooltip>
//     </TooltipProvider>
//   );
// }
//
// function TextIncreaseDecrease({
//   children,
//   by,
//   label,
// }: {
//   children: React.ReactNode;
//   by: number;
//   label: string;
// }) {
//   const dispatch = useDispatch();
//   const abilityTextSize = useGetStoreState("abilityTextSize");
//   return (
//     <TooltipProvider>
//       <Tooltip>
//         <TooltipTrigger asChild>
//           <RichTextEditor.Control
//             onClick={() => {
//               dispatch(onAbilityTextSize(by));
//             }}
//             aria-label={label}
//             title={label}
//             className={"px-2"}
//             disabled={abilityTextSize === 1 && label === "Decrease font size"}
//           >
//             {children}
//           </RichTextEditor.Control>
//         </TooltipTrigger>
//         <TooltipContent>{label}</TooltipContent>
//       </Tooltip>
//     </TooltipProvider>
//   );
// }
//
// function TextSize() {
//   const size = useSelector(
//     (state: storeState) => state.mainFormSlice.abilityTextSize,
//   );
//   const dispatch = useDispatch();
//   return (
//     <TooltipProvider>
//       <Tooltip>
//         <TooltipTrigger asChild>
//           <RichTextEditor.Control
//             onClick={() => {
//               dispatch(onAbilityTextSize("default"));
//             }}
//             aria-label="Reset font size"
//             title="Reset font size"
//             className={""}
//           >
//             <div
//               className={
//                 "flex min-w-[4rem] content-center justify-center text-center"
//               }
//             >
//               <p>{size + "px"}</p>
//             </div>
//           </RichTextEditor.Control>
//         </TooltipTrigger>
//         <TooltipContent>Reset font size</TooltipContent>
//       </Tooltip>
//     </TooltipProvider>
//   );
// }
//
// export default function AbilityInput({
//   backgroundToggle = false,
// }: {
//   backgroundToggle?: boolean;
// }) {
//   const editorState = useGetStoreState("editorState");
//   const dispatch = useDispatch();
//
//   const [openedHelp, setOpenedHelp] = useState(false);
//   const [openedAbilities, setOpenedAbilities] = useState(false);
//
//   const openHelp = () => setOpenedHelp(true);
//   const closeHelp = () => setOpenedHelp(false);
//   const openAbilities = () => setOpenedAbilities(true);
//   const closeAbilities = () => setOpenedAbilities(false);
//
//   const editor = useEditor({
//     content: editorState,
//     immediatelyRender: false,
//     extensions: [
//       StarterKit,
//       Underline,
//       Highlight,
//       TextAlign.configure({ types: ["heading", "paragraph"] }),
//       Color,
//       CustomParagraph,
//       CustomBold,
//       CustomItalic,
//     ],
//     editorProps: {
//       attributes: {
//         class: "ability-input",
//       },
//     },
//     onUpdate({ editor }) {
//       dispatch(onAbility(editor.getHTML()));
//     },
//     onDestroy() {
//       dispatch(onEditorState(editor?.getJSON()));
//     },
//     onBlur() {
//       dispatch(onEditorState(editor?.getJSON()));
//     },
//     onCreate({ editor }) {
//       editor.commands.setContent(editorState);
//     },
//   });
//
//   return (
//     <>
//       <div className={"flex flex-col"}>
//         <label className={"text-sm"}>Ability</label>
//         <p className={"text-muted-foreground text-xs"}>Card ability</p>
//         <RichTextEditor.Root
//           editor={editor}
//           className={
//             "relative mt-1 max-w-full bg-stone-800 lg:max-w-[697.42px]"
//           }
//           id={"ability"}
//         >
//           <RichTextEditor.Toolbar className={"flex flex-row"}>
//             <RichTextEditor.ControlsGroup>
//               <RichTextEditor.Bold editor={editor} />
//               <RichTextEditor.Italic editor={editor} />
//               <RichTextEditor.Underline editor={editor} />
//               <RichTextEditor.Strikethrough editor={editor} />
//               <RichTextEditor.ClearFormatting editor={editor} />
//             </RichTextEditor.ControlsGroup>
//             <RichTextEditor.ColorPicker
//               colors={[
//                 "#25262b",
//                 "#868e96",
//                 "#fa5252",
//                 "#e64980",
//                 "#be4bdb",
//                 "#7950f2",
//                 "#4c6ef5",
//                 "#228be6",
//                 "#15aabf",
//                 "#12b886",
//                 "#40c057",
//                 "#82c91e",
//                 "#fab005",
//                 "#fd7e14",
//               ]}
//               editor={editor}
//             />
//
//             {editor && (
//               <BubbleMenu
//                 editor={editor}
//                 tippyOptions={{ offset: [80, -60], zIndex: 100 }}
//               >
//                 <RichTextEditor.ControlsGroup className={"flex"}>
//                   <TooltipProvider>
//                     <Tooltip>
//                       <TooltipTrigger asChild>
//                         <RichTextEditor.Color color="#2F77B3" editor={editor} />
//                       </TooltipTrigger>
//                       <TooltipContent className="blue-ability-tooltip text-neutral-900 dark:text-neutral-100">
//                         OnPlay
//                       </TooltipContent>
//                     </Tooltip>
//                   </TooltipProvider>
//
//                   <TooltipProvider>
//                     <Tooltip>
//                       <TooltipTrigger asChild>
//                         <RichTextEditor.Color color="#d94880" editor={editor} />
//                       </TooltipTrigger>
//                       <TooltipContent className="pink-ability-tooltip text-neutral-900 dark:text-neutral-100">
//                         Once Per Turn
//                       </TooltipContent>
//                     </Tooltip>
//                   </TooltipProvider>
//
//                   <TooltipProvider>
//                     <Tooltip>
//                       <TooltipTrigger asChild>
//                         <RichTextEditor.Color color="#DC8535" editor={editor} />
//                       </TooltipTrigger>
//                       <TooltipContent className="orange-ability-tooltip text-neutral-900 dark:text-neutral-100">
//                         Blocker
//                       </TooltipContent>
//                     </Tooltip>
//                   </TooltipProvider>
//
//                   <TooltipProvider>
//                     <Tooltip>
//                       <TooltipTrigger asChild>
//                         <RichTextEditor.Color color="#ba212f" editor={editor} />
//                       </TooltipTrigger>
//                       <TooltipContent className="counter-ability-tooltip text-neutral-900 dark:text-neutral-100">
//                         Counter
//                       </TooltipContent>
//                     </Tooltip>
//                   </TooltipProvider>
//
//                   <TooltipProvider>
//                     <Tooltip>
//                       <TooltipTrigger asChild>
//                         <RichTextEditor.Color color="#f8ed70" editor={editor} />
//                       </TooltipTrigger>
//                       <TooltipContent className="trigger-ability-tooltip text-neutral-900 dark:text-neutral-100">
//                         Trigger
//                       </TooltipContent>
//                     </Tooltip>
//                   </TooltipProvider>
//
//                   <TooltipProvider>
//                     <Tooltip>
//                       <TooltipTrigger asChild>
//                         <RichTextEditor.Color color="#FFFFFF" editor={editor} />
//                       </TooltipTrigger>
//                       <TooltipContent className="number-ability-tooltip text-neutral-900 dark:text-neutral-100">
//                         1
//                       </TooltipContent>
//                     </Tooltip>
//                   </TooltipProvider>
//
//                   <TooltipProvider>
//                     <Tooltip>
//                       <TooltipTrigger asChild>
//                         <RichTextEditor.Color color="#000000" editor={editor} />
//                       </TooltipTrigger>
//                       <TooltipContent className="don-ability-tooltip text-neutral-900 dark:text-neutral-100">
//                         DON!!×1
//                       </TooltipContent>
//                     </Tooltip>
//                   </TooltipProvider>
//
//                   <RichTextEditor.Italic editor={editor} />
//                   <RichTextEditor.UnsetColor editor={editor} />
//                 </RichTextEditor.ControlsGroup>
//               </BubbleMenu>
//             )}
//
//             <RichTextEditor.ControlsGroup>
//               <TextIncreaseDecrease by={-0.5} label={"Decrease font size"}>
//                 {"<"}
//               </TextIncreaseDecrease>
//               <TextSize />
//               <TextIncreaseDecrease by={0.5} label={"Increase font size"}>
//                 {">"}
//               </TextIncreaseDecrease>
//             </RichTextEditor.ControlsGroup>
//             <DropShadowButton />
//             <AbilityDropShadowButton />
//             {backgroundToggle && <Background />}
//
//             {/* Help Dialog */}
//             <Dialog open={openedHelp} onOpenChange={setOpenedHelp}>
//               <DialogContent className="max-w-3xl">
//                 <DialogHeader>
//                   <DialogTitle>Help</DialogTitle>
//                 </DialogHeader>
//                 <div className="space-y-4">
//                   <p>
//                     Just select the text to style it, you can style both Trigger
//                     and Ability text.
//                   </p>
//                   <p>
//                     A bubble menu that includes all ability styles represented
//                     by their respective colors will appear, you can also remove
//                     the style by selecting the last option in the menu.
//                   </p>
//                   <p>
//                     For trigger text, press "Enter" to create a new line in case
//                     you want the text to start below{" "}
//                     <span className="trigger-text">
//                       <span className="trigger-ability-container">
//                         <span className="trigger-ability-shadow">
//                           <span className="trigger-ability mt-1">Trigger</span>{" "}
//                         </span>
//                       </span>
//                     </span>
//                   </p>
//                   <p>
//                     You can use the <strong>"Background"</strong> button to
//                     toggle the background of the ability text.
//                   </p>
//                   <p>
//                     You can use the <strong>"Text Outline"</strong> and{" "}
//                     <strong>"Ability Outline"</strong> buttons to toggle the
//                     white outline of the text and the ability text respectively.
//                   </p>
//                   <p>
//                     You can change the font size by pressing{" "}
//                     <strong>{`"<"`}</strong> or <strong>{`">"`}</strong>, and
//                     you can reset it by pressing the current font size button.
//                   </p>
//                   <p>
//                     Pro Tip: you can use the "×" sign instead of "x" to make the
//                     DON!! ability more accurate e.g.{" "}
//                     <span className="black-ability-container">
//                       <span className="black-ability-shadow">
//                         <span className="black-ability">DON!!×1</span>{" "}
//                       </span>
//                     </span>
//                   </p>
//                 </div>
//               </DialogContent>
//             </Dialog>
//
//             {/* Abilities Dialog */}
//             <Dialog open={openedAbilities} onOpenChange={setOpenedAbilities}>
//               <DialogContent className="max-w-3xl">
//                 <DialogHeader>
//                   <DialogTitle>Ability List</DialogTitle>
//                 </DialogHeader>
//                 <div className="space-y-4">
//                   <p>
//                     <span className={"blue-ability"}>On Play</span>{" "}
//                     <span className={"blue-ability"}>On Block</span>{" "}
//                     <span className={"blue-ability"}>On K.O.</span>{" "}
//                     <span className={"blue-ability"}>Activate:Main</span>{" "}
//                     <span className={"blue-ability"}>When Attacking</span>{" "}
//                     <span className={"blue-ability"}>Your Turn</span>{" "}
//                     <span className={"blue-ability"}>Opponent&apos;s turn</span>{" "}
//                     <span className={"blue-ability"}>Main</span>{" "}
//                     <span className={"blue-ability"}>When Attacked</span>{" "}
//                     <span className={"blue-ability"}>Start of Your Turn</span>{" "}
//                     <span className={"blue-ability"}>End of Your Turn</span>{" "}
//                     <span className={"blue-ability"}>
//                       When Opponent Attacks
//                     </span>{" "}
//                   </p>
//                   <p>
//                     <span className={"pink-ability"}>Once Per Turn</span>{" "}
//                   </p>
//                   <p>
//                     <span className={"orange-ability-container"}>
//                       <span className={"orange-ability-shadow"}>
//                         <span className={"orange-ability"}>Blocker</span>{" "}
//                       </span>
//                     </span>
//                     <span className={"orange-ability-container"}>
//                       <span className={"orange-ability-shadow"}>
//                         <span className={"orange-ability"}>Rush</span>{" "}
//                       </span>
//                     </span>
//                     <span className={"orange-ability-container"}>
//                       <span className={"orange-ability-shadow"}>
//                         <span className={"orange-ability"}>Double Attack</span>{" "}
//                       </span>
//                     </span>
//                     <span className={"orange-ability-container"}>
//                       <span className={"orange-ability-shadow"}>
//                         <span className={"orange-ability"}>Banish</span>{" "}
//                       </span>
//                     </span>
//                   </p>
//                   <p>
//                     <span className={"red-ability"}>Counter</span>{" "}
//                   </p>
//                   <p className={"trigger-text"}>
//                     <span className={"trigger-ability-container"}>
//                       <span className={"trigger-ability-shadow"}>
//                         <span className={"trigger-ability mt-1"}>Trigger</span>{" "}
//                       </span>
//                     </span>
//                   </p>
//                   <p>
//                     <span className={"white-ability"}>1</span>{" "}
//                   </p>
//                   <p>
//                     <span className={"black-ability-container"}>
//                       <span className={"black-ability-shadow"}>
//                         <span className={"black-ability"}>DON!!×1</span>{" "}
//                       </span>
//                     </span>
//                   </p>
//                 </div>
//               </DialogContent>
//             </Dialog>
//
//             <RichTextEditor.Control
//               className={"px-2"}
//               onClick={openAbilities}
//               title="Ability List"
//             >
//               Ability List
//             </RichTextEditor.Control>
//             <RichTextEditor.Control
//               className={"px-2"}
//               onClick={openHelp}
//               title="Help"
//             >
//               Help
//             </RichTextEditor.Control>
//           </RichTextEditor.Toolbar>
//
//           <div className="prose prose-sm font-roboto max-w-none overflow-clip p-3 focus:outline-none">
//             {editor && <EditorContent editor={editor} />}
//           </div>
//           <DefaultStateSetter />
//         </RichTextEditor.Root>
//       </div>
//     </>
//   );
// }
//
// function DefaultStateSetter() {
//   // Get the editor instance from the context
//   const editor = useRef<Editor | null>(null);
//
//   // Get the default state for the editor from some store
//   const editorDefaultState = useGetStoreState("editorDefaultState");
//
//   const commandsRef = useRef<SingleCommands | undefined>(undefined);
//
//   // Update the ref if the editor commands change
//   useEffect(() => {
//     commandsRef.current = editor.current?.commands;
//   }, [editor.current?.commands]);
//
//   useEffect(() => {
//     // Only set content if the commands have not changed
//     if (commandsRef.current) {
//       commandsRef.current.setContent(editorDefaultState);
//     }
//   }, [editorDefaultState]);
//
//   // This component does not render anything
//   return null;
// }
