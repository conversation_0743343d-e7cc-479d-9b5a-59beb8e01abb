// "use client";
// import { onAttribute } from "@/store/formSlice";
// import { useDispatch, useSelector } from "react-redux";
// import { storeState } from "@/store/store";
//
// export function AttributeButton({ type }: { type: string }) {
//   const active =
//     useSelector((state: storeState) => state.mainFormSlice.attribute) === type;
//   const dispatch = useDispatch();
//   return (
//     <button
//       onClick={() => {
//         dispatch(onAttribute(type as CardAttribute));
//       }}
//     >
//       <img
//         className={`${
//           active ? "drop-shadow-[0_0px_3px_rgba(255,255,255,1)]" : ""
//         } max-h-[2.7rem]`}
//         src={`/assetss/${type}.png`}
//         alt="slash ability"
//       />
//     </button>
//   );
// }
