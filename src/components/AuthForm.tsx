/*
"use client";
import { ThemeSupa } from "@supabase/auth-ui-shared";
import { Auth } from "@supabase/auth-ui-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { Subscription } from "@supabase/supabase-js";
import { createClient } from "@/utils/supabase/client";
import { showErrorToast, showSuccessToast } from "@/lib/toast";
import { Button } from "@/components/ui/button";
import { useTheme } from "next-themes";

export default function AuthForm() {
  const router = useRouter();
  const supabase = createClient();
  const [mounted, setMounted] = useState(false);
  const { theme, systemTheme } = useTheme();
  useEffect(() => {
    setMounted(true);
  }, []);
  const currentTheme = mounted
    ? theme === "system"
      ? systemTheme
      : theme
    : undefined;
  const [isResettingPassword, setIsResettingPassword] = useState(false);
  useEffect(() => {
    let subscription: { data: { subscription: Subscription } };
    async function run() {
      subscription = supabase.auth.onAuthStateChange(
        async (event, newSession) => {
          if (newSession?.access_token && event === "SIGNED_IN") {
            router.refresh();

            router.push("/one-piece/character");
          }
          if (newSession?.access_token && event === "PASSWORD_RECOVERY") {
            router.refresh();

            router.push("/reset-password");
          }
        },
      );
    }
    run();
    return () => {
      subscription.data.subscription.unsubscribe();
    };
  }, [router, supabase.auth]);
  return (
    <div>
      <Auth
        supabaseClient={supabase}
        appearance={{ theme: ThemeSupa }}
        view="sign_in"
        showLinks={false}
        theme={currentTheme}
        providers={["google"]}
        redirectTo={`${process.env.NEXT_PUBLIC_REDIRECT_URL}/api/auth/callback`}
      />
      <div className="mt-4 text-center">
        <Button
          variant="ghost"
          disabled={isResettingPassword}
          loading={isResettingPassword}
          className="text-muted-foreground hover:text-muted-foreground/80 text-sm hover:underline"
          onClick={async () => {
            const emailInput = document.querySelector(
              'input[name="email"]',
            ) as HTMLInputElement;

            if (!emailInput || !emailInput.value) {
              showErrorToast("Missing Email", {
                description: "Please enter your email address first.",
              });
              return;
            }

            setIsResettingPassword(true);

            try {
              const { error } = await supabase.auth.resetPasswordForEmail(
                emailInput.value,
                {
                  redirectTo: `${process.env.NEXT_PUBLIC_REDIRECT_URL}/login`,
                },
              );

              if (error) {
                showErrorToast("Error", {
                  description: error.message,
                });
              } else {
                showSuccessToast("Password Reset Email Sent", {
                  description:
                    "If an account exists with this email, a password reset link will be sent. Check your email inbox.",
                });
              }
            } catch (err) {
              console.error("Password reset error:", err);
              showErrorToast("Error", {
                description:
                  "An error occurred while attempting to reset your password.",
              });
            } finally {
              // Re-enable the button after a delay
              setTimeout(() => {
                setIsResettingPassword(false);
              }, 2000);
            }
          }}
        >
          Forgot your password?
        </Button>
      </div>
    </div>
  );
}
*/
