import DownloadButton from "@/components/CardForm-components/DownloadButton";
import React, { Suspense } from "react";

import { Subscription } from "@/types";
import { User } from "@supabase/supabase-js";
import { Skeleton } from "@/components/ui/skeleton";

export default function AsyncDownloadButton({
  cardType,
  subscription,
  user,
}: {
  cardType: "leader" | "character" | "event" | "stage" | "don";
  subscription: Subscription;
  user:
    | {
        user: User;
      }
    | {
        user: null;
      };
}) {
  return (
    <>
      <Suspense fallback={<Skeleton className={"h-9"} />}>
        <AsyncButton
          cardType={cardType}
          subscription={subscription}
          user={user}
        />
      </Suspense>
    </>
  );
}

async function AsyncButton({
  cardType,
  subscription,
  user,
}: {
  cardType: "leader" | "character" | "event" | "stage" | "don";
  subscription: Subscription;
  user:
    | {
        user: User;
      }
    | {
        user: null;
      };
}) {
  return (
    <DownloadButton
      cardKind={cardType}
      subscription={subscription}
      user={user}
    />
  );
}
