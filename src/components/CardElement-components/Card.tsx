"use client";
import React, { useEffect, useRef } from "react";
import { useGetResponsiveFontsizeInPx } from "@/hooks/useGetResponsiveFontsizeInPx";
import OnePieceReactSvgWithGradient from "@/components/OnePieceReactSvgWithGradient";
import { useGetStoreState } from "@/helpers/useGetStoreState";
const innerBorderBottom = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Inner-Border-Bottom.svg`;
const printReadyInnerBorderBottom = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Print-Ready-Inner-Border-Bottom.svg`;
export default function Card({
  children,
  maxWRem = 0,
  maxWPx = 0,
  className = "",
}: {
  children: React.ReactNode;
  maxWRem?: number;
  maxWPx?: number;
  className?: string;
}) {
  const target = useRef<null | HTMLDivElement>(null);
  const observerRef = useRef<null | MutationObserver>(null);
  const printReady = useGetStoreState("printReady");
  useEffect(function () {
    const cardElement = document.getElementById("card-element");
    const protectedElement = document.getElementById("utcgcmwm");

    // Store original attributes
    const originalAttributes = {
      id: "utcgcmwm",
      className: protectedElement?.className || "",
      style: protectedElement?.getAttribute("style") || "",
    };

    // Observer for the protected element's attributes
    const attributeObserver = new MutationObserver((mutationsList) => {
      attributeObserver.disconnect();

      for (const mutation of mutationsList) {
        const element = mutation.target as HTMLElement;

        // Restore ID if changed
        if (mutation.attributeName === "id" && element.id !== "utcgcmwm") {
          element.id = "utcgcmwm";
        }

        // Restore class if changed
        if (mutation.attributeName === "class") {
          element.className = originalAttributes.className;
        }

        // Restore style if changed
        if (mutation.attributeName === "style") {
          if (originalAttributes.style) {
            element.setAttribute("style", originalAttributes.style);
          } else {
            element.removeAttribute("style");
          }
        }
      }

      // Reconnect to the current element (in case ID changed)
      const currentElement = document.getElementById("utcgcmwm");
      if (currentElement) {
        attributeObserver.observe(currentElement, {
          attributes: true,
          attributeOldValue: true,
        });
      }
    });

    // Observer for removal detection (only watches direct children)
    const removalObserver = new MutationObserver((mutationsList) => {
      for (const mutation of mutationsList) {
        if (mutation.type === "childList") {
          let removedProtected = false;
          for (let i = 0; i < mutation.removedNodes.length; i++) {
            const node = mutation.removedNodes[i] as HTMLElement;
            if (node?.id === "utcgcmwm") {
              removedProtected = true;
              break;
            }
          }

          if (!removedProtected) continue;

          removalObserver.disconnect();

          const element = document.createElement("div");
          element.id = "utcgcmwm";
          element.className = originalAttributes.className;
          if (originalAttributes.style) {
            element.setAttribute("style", originalAttributes.style);
          }
          cardElement?.insertAdjacentElement("afterbegin", element);

          // Restart attribute observer
          attributeObserver.observe(element, {
            attributes: true,
            attributeOldValue: true,
          });

          // Restart removal observer
          if (cardElement) {
            removalObserver.observe(cardElement, { childList: true });
          }
        }
      }
    });

    // Start observers
    if (protectedElement) {
      attributeObserver.observe(protectedElement, {
        attributes: true,
        attributeOldValue: true,
      });
    }

    if (cardElement && protectedElement) {
      removalObserver.observe(cardElement, { childList: true }); // Only direct children
    }

    // Store one observer for ref (TypeScript compatibility)
    observerRef.current = attributeObserver;

    return () => {
      attributeObserver.disconnect();
      removalObserver.disconnect();
    };
  }, []);
  const fontsize = useGetResponsiveFontsizeInPx();
  const borderRadius = useGetResponsiveFontsizeInPx({
    desiredTextSizeInPx: 33,
  });
  return (
    <div
      id={"card-element"}
      onContextMenu={(e) => {
        e.preventDefault();
      }}
      style={{
        fontSize: `${fontsize}px`,
        maxWidth: `${maxWRem ? maxWRem + "rem" : maxWPx ? maxWPx + "px" : ""}`,
        borderRadius: `${borderRadius}px`,
      }}
      ref={target}
      className={`relative ${className} font-roboto ${maxWRem ? "3xl:max-w-[32rem]!" : ""} sticky top-20 h-full overflow-clip text-white`}
    >
      {children}
      <OnePieceReactSvgWithGradient
        printReady={printReady}
        printReadySrc={printReadyInnerBorderBottom}
        standardSrc={innerBorderBottom}
        className={`relative top-0 z-4 opacity-0`}
      />
    </div>
  );
}
