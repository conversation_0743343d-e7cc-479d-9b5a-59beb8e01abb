import React, { Suspense } from "react";
import { Subscription } from "@/types";

export default function CardWaterMark({
  cardType = "",
  subscription,
}: {
  cardType?: "character" | "leader" | "event" | "stage" | "don" | "";
  subscription: Subscription;
}) {
  return (
    <Suspense fallback={null}>
      <WaterMark cardType={cardType} subscription={subscription} />
    </Suspense>
  );
}

async function WaterMark({
  cardType = "",
  subscription,
}: {
  cardType?: "character" | "leader" | "event" | "stage" | "don" | "";
  subscription: Subscription;
}) {
  // const subscription = await getSubscriptionNameAndStatus();

  if (!subscription.active)
    return (
      <>
        {/*<ReactSvgWithGradient*/}
        {/*  printReady={false}*/}
        {/*  printReadySrc={"https://r2.ultimatetcgcm.com/svg/watermark.svg"}*/}
        {/*  standardSrc={"https://r2.ultimatetcgcm.com/svg/watermark.svg"}*/}
        {/*  className={`absolute top-0 opacity-10`}*/}
        {/*  // colorArray={backgroundGradientSVG}*/}
        {/*  colorArray={["#fff"]}*/}
        {/*  style={{*/}
        {/*    zIndex: cardType === "character" ? "4" : "0",*/}
        {/*    top: "0",*/}
        {/*  }}*/}
        {/*  id={"utcgcmwm"}*/}
        {/*  blendMode={"difference"}*/}
        {/*/>*/}
        <div
          id={"utcgcmwm"}
          className={``}
          style={{
            zIndex: cardType === "character" ? "4" : "0",
            top: "0",
          }}
        />
      </>
    );
  return null;
}
