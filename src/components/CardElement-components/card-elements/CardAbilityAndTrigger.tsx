"use client";
import Card<PERSON><PERSON>Background from "@/components/CardElement-components/card-elements/CardAbilityBackground";
import CardAbilityText from "@/components/CardElement-components/card-elements/CardAbilityText";
import CardTrigger from "@/components/CardElement-components/card-elements/CardTrigger";

import { useGetResponsiveFontsizeInPx } from "@/hooks/useGetResponsiveFontsizeInPx";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import CardTriggerTextNew from "@/components/CardElement-components/card-elements/CardTriggerTextNew";
import {
  getPrintReadySizeNumber,
  getPrintReadySizeString,
} from "@/app/helpers/getPrintReadySize";
import { getPrintReadyDistanceString } from "@/app/helpers/getPrintReadyDistance";
const triggerFrame = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/EB02-Trigger-Frame.png`;

export default function CardAbilityAndTrigger({
  abilityBackGround = true,
  triggerQuality = 1,
  cardType,
  client = false,
}: {
  abilityBackGround?: boolean;
  triggerQuality: number;
  cardType: "character" | "leader" | "event" | "stage" | "don";
  client?: boolean;
}) {
  const printReady = useGetStoreState("printReady");
  const characterBorder = useGetStoreState("characterBorder");
  const eventBorder = useGetStoreState("eventBorder");
  const fontsize = useGetResponsiveFontsizeInPx({ desiredTextSizeInPx: 6 });
  const trigger = useGetStoreState("trigger");
  const cardKindRoute = cardType;
  let topOffset = "60.9217%";
  let hOffset = "21.3%";
  let background =
    "linear-gradient(to right, rgba(0, 0, 0, 1) 0%, rgba(0, 0, 0, 1) 100%)";
  if (
    (cardKindRoute === "event" && eventBorder !== "op-10") ||
    cardKindRoute === "stage"
  ) {
    topOffset = "58.7917%";
    hOffset = "23.45%";
  }
  if (cardType === "character" && characterBorder === "none") {
    background =
      "linear-gradient(to right, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.5) 100%)";
  }
  return (
    <div
      className={`$ absolute flex flex-col`}
      style={{
        zIndex: 4,
        left: getPrintReadyDistanceString("6.5%", "left-or-right", printReady),
        top: getPrintReadyDistanceString(
          topOffset,
          "top-or-bottom",
          printReady,
          -1,
        ),
        height: getPrintReadySizeString(hOffset, printReady, 25),
        width: getPrintReadySizeString("87.4%", printReady),
        gap: printReady ? getPrintReadySizeString("3%", printReady) : "2.8%",
        // top: topOffset,
        // height: hOffset,
      }}
    >
      <CardAbilityBackground backGround={abilityBackGround}>
        <CardAbilityText client={client} cardKindRoute={cardType} />
      </CardAbilityBackground>
      {trigger && (
        <div className={"relative"}>
          <div
            className={`relative overflow-clip`}
            style={{
              borderRadius: `${getPrintReadySizeNumber(fontsize, printReady)}px`,
              background: background,
            }}
          >
            <CardTrigger quality={triggerQuality} />
            <CardTriggerTextNew />
          </div>
          {cardType === "event" && eventBorder === "eb-02" && (
            <div
              className={"absolute"}
              style={{
                maskImage: `url(${triggerFrame})`,
                maskSize: "cover",
                maskRepeat: "no-repeat",
                background: `linear-gradient(135deg, #ce2020,  #ce2020)`,

                // zIndex: 1004,
                left: "-0.25em",
                top: "-0.4em",
                height: printReady ? "3.36em" : "3.62em",
                width: printReady ? "4.6em" : "5em",
              }}
            ></div>
          )}
        </div>
      )}
    </div>
  );
}
