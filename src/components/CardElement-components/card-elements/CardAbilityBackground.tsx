"use client";
import React from "react";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import { useGetResponsiveFontsizeInPx } from "@/hooks/useGetResponsiveFontsizeInPx";
import { getPrintReadySizeNumber } from "@/app/helpers/getPrintReadySize";

export default function CardAbilityBackground({
  children,
  backGround = true,
}: {
  children: React.ReactNode;
  backGround?: boolean;
  leader?: boolean;
}) {
  const printReady = useGetStoreState("printReady");
  const abilityBackground = useGetStoreState("abilityBackground");
  const dropShadow = useGetStoreState("dropShadow");
  const abilityDropShadow = useGetStoreState("abilityDropShadow");
  // const height = trigger && !leader ? "h-[18.6%]" : "h-[22.9%]";
  /*  const background =
    abilityBackground && backGround ? "bg-[rgba(255,255,255,0.75)]" : "";  */
  const background =
    abilityBackground && backGround ? "bg-[rgba(255,255,255,0.60)]" : "";
  const fontsize = useGetResponsiveFontsizeInPx({ desiredTextSizeInPx: 4.8 });
  return (
    <>
      <div
        className={` ${background} grow overflow-clip 
        ${dropShadow ? "no-ability-background " : ""}
        ${abilityDropShadow ? "no-colored-ability-background" : ""}
        `}
        style={{
          borderRadius: `${getPrintReadySizeNumber(fontsize, printReady)}px`,
        }}
      >
        {children}
      </div>
    </>
  );
}
