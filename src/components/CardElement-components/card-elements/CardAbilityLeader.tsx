"use client";
import Card<PERSON><PERSON>Background from "@/components/CardElement-components/card-elements/CardAbilityBackground";
import CardAbilityText from "@/components/CardElement-components/card-elements/CardAbilityText";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import { getPrintReadyDistanceString } from "@/app/helpers/getPrintReadyDistance";
import { getPrintReadySizeString } from "@/app/helpers/getPrintReadySize";

export default function CardAbilityLeader({
  client = false,
  donAbility = false,
  cardType = "leader",
}: {
  client?: boolean;
  donAbility?: boolean;
  cardType?: "leader" | "don";
}) {
  const printReady = useGetStoreState("printReady");
  if (cardType === "don" && !donAbility) return null;
  return (
    <div
      className={"absolute flex flex-col"}
      style={{
        zIndex: 4,
        left: getPrintReadyDistanceString("6.5%", "left-or-right", printReady),
        top: getPrintReadyDistanceString(
          "60.9217%",
          "top-or-bottom",
          printReady,
          -1,
        ),

        height: getPrintReadySizeString("21.3%", printReady, 25),
        width: getPrintReadySizeString("87.4%", printReady),
        gap: getPrintReadySizeString("2.8%", printReady),
      }}
    >
      <CardAbilityBackground>
        <CardAbilityText client={client} cardKindRoute={"leader"} />
      </CardAbilityBackground>
    </div>
  );
}
