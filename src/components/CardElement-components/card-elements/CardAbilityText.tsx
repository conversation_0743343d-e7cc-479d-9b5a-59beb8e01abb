"use client";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import { useRef } from "react";
import { useGetResponsiveFontsizeInPx } from "@/hooks/useGetResponsiveFontsizeInPx";
import { LINUX_ABILITY_TEXT_STYLES_OBJ } from "@/app/constants";

import { getPrintReadySizeNumber } from "@/app/helpers/getPrintReadySize";

export default function CardAbilityText({
  client = false,
  cardKindRoute,
}: {
  client?: boolean;
  cardKindRoute: "leader" | "character" | "event" | "stage" | "don";
}) {
  const printReady = useGetStoreState("printReady");
  const dropShadow = useGetStoreState("dropShadow");
  const abilityBackground = useGetStoreState("abilityBackground");
  const eventBorder = useGetStoreState("eventBorder");
  const ability = useGetStoreState("ability") as string;
  const target = useRef(null);
  const fontsize = useGetResponsiveFontsizeInPx({
    useTextSizeState: true,
  });

  return (
    <div
      id={`${dropShadow ? "ability-text" : ""}`}
      ref={target}
      dangerouslySetInnerHTML={{ __html: ability }}
      // dangerouslySetInnerHTML={{ __html: replaceSpacesInTextContent(ability) }}
      style={{
        fontSize: `${getPrintReadySizeNumber(fontsize, printReady)}px`,
        letterSpacing:
          client || process.env.NEXT_PUBLIC_WINDOWS
            ? ""
            : `${LINUX_ABILITY_TEXT_STYLES_OBJ.leading}em`,
      }}
      className={`${abilityBackground ? "px-[1.1%]!" : "px-[1.1%]!"} ability-text max-w-[105%] ${(cardKindRoute === "event" && eventBorder !== "op-10") || cardKindRoute === "stage" ? "font-normal!" : ""} font-geologica overflow-clip py-[2.175%]! break-words text-[#1c1917]`}
    ></div>
  );
}
