// "use client";
//
// import Image from "next/image";
//
// import { useGetStoreState } from "@/helpers/useGetStoreState";
// import { CharacterBorder, Color, LeaderBorder } from "@/types";
//
// export default function CardArtist({
//   quality,
//   cardType,
// }: {
//   quality: number;
//   cardType: "character" | "leader" | "event" | "stage" | "don";
// }) {
//   return null;
// }
//
// /*
// export default function CardArtist({
//                                      quality,
//                                      cardType,
//                                    }: {
//   quality: number;
//   cardType: "character" | "leader" | "event" | "stage" | "don";
// }) {
//   const characterBorder = useGetStoreState(
//     "characterBorder",
//   ) as CharacterBorder;
//   const artist = useGetStoreState("artist") as string;
//
//   const leaderBorder = useGetStoreState("leaderBorder") as LeaderBorder;
//   const leaderBorderEnabled = useGetStoreState("leaderBorderEnabled");
//   const color = useGetStoreState("color") as Color;
//
//   if (cardType === "character" || cardType === "event") {
//     if (artist.length > 0) {
//       if (cardType === "character" && characterBorder !== "standard")
//         return null;
//       if (leaderBorderEnabled) {
//         return (
//           <Image
//             width={89}
//             height={57}
//             className={`absolute right-[0.2%] top-[8.228%] h-[2.58%] w-auto `}
//             src={`/assets/yellowartist.png`}
//             alt={`Card trigger image`}
//             quality={quality}
//           />
//         );
//       } else {
//         return (
//           <Image
//             width={89}
//             height={57}
//             className={`absolute right-[0.2%] top-[8.228%] h-[2.58%] w-auto `}
//             src={`/assets/yellowartistOutline.png`}
//             alt={`Card trigger image`}
//             quality={quality}
//           />
//         );
//       }
//     } else {
//       return null;
//     }
//   }
//   if (cardType === "stage") {
//     if (artist.length > 0) {
//       return (
//         <Image
//           width={89}
//           height={57}
//           className={`absolute right-[0.2%] top-[8.228%] h-[2.58%] w-auto `}
//           src={`/assets/artist.png`}
//           alt={`Card trigger image`}
//           quality={quality}
//         />
//       );
//     } else {
//       return null;
//     }
//   }
//   if (cardType === "leader") {
//     if (artist.length > 0) {
//       if (leaderBorderEnabled) {
//         if (leaderBorder === "rainbow") {
//           return (
//             <Image
//               width={89}
//               height={57}
//               className={`absolute right-[0.2%] top-[8.228%] h-[2.58%] w-auto `}
//               src={`/assets/artist.png`}
//               alt={`Card trigger image`}
//               quality={quality}
//             />
//           );
//         } else if (leaderBorder === "op3 AA") {
//           return (
//             <Image
//               width={89}
//               height={57}
//               className={`absolute right-[0.2%] top-[8.228%] h-[2.58%] w-auto `}
//               src={`/assets/yellowartistOutline.png`}
//               alt={`Card trigger image`}
//               quality={quality}
//             />
//           );
//         } else if (color.toLocaleLowerCase().includes("yellow")) {
//           return (
//             <Image
//               width={89}
//               height={57}
//               className={`absolute right-[0.2%] top-[8.228%] h-[2.58%] w-auto `}
//               src={`/assets/yellowartist.png`}
//               alt={`Card trigger image`}
//               quality={quality}
//             />
//           );
//         } else {
//           return (
//             <Image
//               width={89}
//               height={57}
//               className={`absolute right-[0.2%] top-[8.228%] h-[2.58%] w-auto `}
//               src={`/assets/artist.png`}
//               alt={`Card trigger image`}
//               quality={quality}
//             />
//           );
//         }
//       } else {
//         return (
//           <Image
//             width={89}
//             height={57}
//             className={`absolute right-[0.2%] top-[8.228%] h-[2.58%] w-auto `}
//             src={`/assets/yellowartistOutline.png`}
//             alt={`Card trigger image`}
//             quality={quality}
//           />
//         );
//       }
//     } else {
//       return null;
//     }
//   }
// }*/
