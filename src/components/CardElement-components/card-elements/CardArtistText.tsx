"use client";
import { useTextBorderColor } from "@/hooks/useTextBorderColor";

import { useGetStoreState } from "@/helpers/useGetStoreState";
import { useGetResponsiveFontsizeInPx } from "@/hooks/useGetResponsiveFontsizeInPx";
import { getCardColor } from "@/app/helpers/getCardColor";
import { getTextColor } from "@/app/helpers/getTextColor";
import {
  getPrintReadySizeNumber,
  getPrintReadySizeString,
} from "@/app/helpers/getPrintReadySize";
import { getPrintReadyDistanceString } from "@/app/helpers/getPrintReadyDistance";

export default function CardArtistText({
  cardType,
}: {
  cardType: "character" | "leader" | "event" | "stage" | "don";
}) {
  const printReady = useGetStoreState("printReady");
  const characterBorder = useGetStoreState("characterBorder");
  const eventBorder = useGetStoreState("eventBorder");
  const colorArray = useGetStoreState("colorArray");
  const color = colorArray[0];
  const color2 = colorArray[colorArray.length - 1];

  const cardColor2 = getCardColor(color2);
  const shadowColor = getCardColor(color);
  const textColor = useTextBorderColor(cardType, "right");
  const artist = useGetStoreState("artist");
  const fontsize = useGetResponsiveFontsizeInPx({ desiredTextSizeInPx: 10.5 });
  const leaderBorder = useGetStoreState("leaderBorder");
  const textColorSp = getTextColor(
    color,
    characterBorder,
    cardType,
    leaderBorder,
    "artist",
    colorArray,
  );
  let backgroundImage = "";
  if (cardType === "character" && characterBorder === "none") {
    backgroundImage = `linear-gradient(to right, ${color === "yellow" ? "#000" : shadowColor},  ${color === "yellow" ? "#000" : shadowColor})`;
  }
  if (cardType === "character" && characterBorder !== "none") {
    backgroundImage = `linear-gradient(to right, white,  white)`;
  }
  let outline = "text-outline-large ";
  if (cardType === "leader") {
    outline = "text-outline-large ";
    if (leaderBorder === "standard" || leaderBorder === "AA-white") {
      backgroundImage = `linear-gradient(to right, ${cardColor2},  ${cardColor2})`;
    }
    if (
      leaderBorder === "standard-white" ||
      leaderBorder === "25th" ||
      leaderBorder === "AA-black" ||
      leaderBorder === "AA-black-and-white" ||
      leaderBorder === "rainbow" ||
      leaderBorder === "AA-white" ||
      leaderBorder === "full-art"
    ) {
      backgroundImage = `linear-gradient(to right, #fff,  #fff)`;
    }
  }
  if (cardType === "event" && eventBorder === "op-10") {
    backgroundImage = `linear-gradient(to right, #fff,  #fff)`;
  }
  return (
    <div
      style={{
        fontSize: `${getPrintReadySizeNumber(fontsize, printReady)}px`,
        zIndex: 4,
        top: getPrintReadyDistanceString(
          "43.373%",
          "top-or-bottom",
          printReady,
        ),
        right: getPrintReadyDistanceString(
          "-47.605%",
          "left-or-right",
          printReady,
        ),
        width: printReady
          ? getPrintReadySizeString("100%", printReady)
          : "100%",
        letterSpacing: printReady
          ? `${getPrintReadySizeNumber(0.04 + 0.0035, printReady)}em`
          : "0.04em",
      }}
      className={`font-mukta absolute h-auto rotate-[-90deg] text-right ${color.toLocaleLowerCase().includes("yellow") && cardType === "character" && characterBorder === "none" ? "text-neutral-950" : ""} ${cardType === "character" && characterBorder === "none" ? "" : textColor} `}
    >
      <p
        className={`text-outline ${outline} ${cardType === "character" || (cardType === "event" && eventBorder === "op-10") ? "text-outline-large font-bold" : ""}`}
        style={{
          color: `${cardType === "character" && characterBorder === "none" ? textColorSp : ""}`,
          backgroundImage:
            cardType === "character" && characterBorder !== "sr"
              ? backgroundImage
              : cardType === "leader"
                ? backgroundImage
                : cardType === "event" && eventBorder === "op-10"
                  ? backgroundImage
                  : "",
        }}
      >
        {artist}
      </p>
    </div>
  );
}
