"use client";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import Image from "next/image";
import { Card<PERSON><PERSON>ri<PERSON>e, CharacterBorder, LeaderBorder } from "@/types";
import { getPrintReadyDistanceString } from "@/app/helpers/getPrintReadyDistance";
import { getPrintReadySizeString } from "@/app/helpers/getPrintReadySize";

export default function CardAttribute({
  cardKind,
}: {
  cardKind: "character" | "leader" | "event" | "stage" | "don";
}) {
  const printReady = useGetStoreState("printReady");

  const attribute = useGetStoreState("attribute") as CardAttribute;

  const leaderBorder = useGetStoreState("leaderBorder") as LeaderBorder;
  const characterBorder = useGetStoreState(
    "characterBorder",
  ) as CharacterBorder;
  let V2 = false;
  const right = "1.5%";
  if (cardKind === "character") {
    if (
      characterBorder === "rare" ||
      characterBorder === "sec-aa" ||
      characterBorder === "mr" ||
      characterBorder === "none" ||
      characterBorder === "sp-v2"
    ) {
      V2 = true;
    }
  }
  if (cardKind === "leader") {
    if (
      leaderBorder === "25th" ||
      leaderBorder === "AA-black" ||
      leaderBorder === "full-art"
    ) {
      V2 = true;
    }
  }

  return (
    <>
      <Image
        width={103}
        height={103}
        priority
        loading={"eager"}
        title={`${attribute}${V2 ? "V2" : ""} attribute image`}
        className={`} absolute h-auto`}
        src={`${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/${attribute}${V2 ? "V2" : ""}.png`}
        alt={`${attribute}${V2 ? "V2" : ""} attribute image`}
        quality={100}
        style={{
          zIndex: 4,
          right: getPrintReadyDistanceString(
            right,
            "left-or-right",
            printReady,
          ),
          top: `${parseFloat(getPrintReadyDistanceString("0%", "top-or-bottom", printReady) || "") + -0.01}%`,
          width: getPrintReadySizeString("12.786%", printReady),
        }}
      />
    </>
  );
}
