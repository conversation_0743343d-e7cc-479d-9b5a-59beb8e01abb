"use client";
import Image from "next/image";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import { getCardType } from "@/helpers/getCardType";
import { LeaderBorder } from "@/types";
import { getCardColor } from "@/app/helpers/getCardColor";
import { generateMultiColorGradient } from "@/app/helpers/generateMultiColorGradient";
import React from "react";

// Import SVG assets
const cardBackgroundPattern = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Card-Background.svg`;
const printReadyCardBackgroundPattern = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Print-Ready-Card-Background.svg`;
import OnePieceReactSvgWithGradient from "@/components/OnePieceReactSvgWithGradient";

export default function CardBackground({
  cardType,
  quality,
  png = false,
}: {
  cardType: "character" | "leader" | "event" | "stage" | "don";
  quality: number;
  png?: boolean;
}) {
  const backgroundImageUrl = useGetStoreState("backgroundImageUrl") as string;
  const cardTypeRoute = getCardType(cardType);
  const printReady = useGetStoreState("printReady");
  const eventBorder = useGetStoreState("eventBorder");
  const colorArray = useGetStoreState("colorArray");
  const color =
    Array.isArray(colorArray) && colorArray.length > 0 ? colorArray[0] : "red";

  // Get background colors for the first and last colors
  const backgroundColor = getCardColor(color, false, false, false, false, true);

  // Generate multi-color gradient based on the number of colors
  const multiColorGradient = Array.isArray(colorArray)
    ? generateMultiColorGradient(colorArray, true)
    : `linear-gradient(to right, ${backgroundColor}, ${backgroundColor})`;
  const leaderBorder = useGetStoreState("leaderBorder") as LeaderBorder;

  if (backgroundImageUrl) {
    return (
      <img
        className={"absolute h-full w-full object-cover"}
        src={backgroundImageUrl}
        alt={"your image"}
      />
    );
  }

  return (
    <>
      {/*Card backgrounds*/}
      {(cardType === "character" ||
        (cardType === "event" && eventBorder !== "eb-02") ||
        cardType === "stage") && (
        <>
          <div
            // className={"absolute z-[-3] h-full w-full brightness-[65%] "}
            className={
              "absolute z-[-3] h-full w-full brightness-[65%] -hue-rotate-5 filter"
            }
            style={{
              background:
                Array.isArray(colorArray) && colorArray.length > 1
                  ? multiColorGradient
                  : backgroundColor,
            }}
          ></div>
        </>
      )}
      {cardType === "event" && eventBorder === "eb-02" && (
        <>
          <div
            className={"absolute z-[-3] h-full w-full"}
            style={{
              background: `linear-gradient(to right,#FAF7EF ,#FAF7EF)`,
            }}
          ></div>
        </>
      )}
      {cardType === "leader" &&
        leaderBorder !== "standard-white" &&
        leaderBorder !== "AA-black-and-white" && (
          <>
            <div
              // TODO: add custom colors so that the use for red and blue is more accurate.
              // className={`absolute z-[-3] h-full w-full brightness-[65%] ${color === "red" || color === "blue" ? "" : "-hue-rotate-5"} filter`}
              className={`absolute z-[-3] h-full w-full brightness-[65%] -hue-rotate-5 filter`}
              style={{
                background:
                  Array.isArray(colorArray) && colorArray.length > 1
                    ? multiColorGradient
                    : backgroundColor,
              }}
            ></div>
          </>
        )}
      {cardType === "leader" &&
        (leaderBorder === "standard-white" ||
          leaderBorder === "AA-black-and-white") && (
          <>
            <div
              className={"absolute z-[-3] h-full w-full brightness-[100%]"}
              style={{
                background: `linear-gradient(to right,#fff 50%,#fff 50%)`,
              }}
            ></div>
          </>
        )}
      {cardType === "don" && (
        <>
          <div
            className={"absolute z-[-3] h-full w-full brightness-[65%]"}
            style={{ backgroundColor: "#272424" }}
          ></div>
        </>
      )}

      {/* Card background patterns */}
      {(cardType === "character" ||
        (cardType === "event" && eventBorder !== "eb-02") ||
        cardType === "stage" ||
        cardType === "don") && (
        <>
          <OnePieceReactSvgWithGradient
            printReady={printReady}
            printReadySrc={printReadyCardBackgroundPattern}
            standardSrc={cardBackgroundPattern}
            className={"absolute -z-3 opacity-40"}
          />
        </>
      )}

      {cardType === "leader" &&
        leaderBorder !== "standard-white" &&
        leaderBorder !== "AA-black-and-white" &&
        leaderBorder !== "AA-white" && (
          <OnePieceReactSvgWithGradient
            printReady={printReady}
            printReadySrc={printReadyCardBackgroundPattern}
            standardSrc={cardBackgroundPattern}
            className={"absolute -z-3 opacity-40"}
          />
        )}

      {cardType === "leader" && leaderBorder === "AA-white" && (
        <OnePieceReactSvgWithGradient
          printReady={printReady}
          printReadySrc={printReadyCardBackgroundPattern}
          standardSrc={cardBackgroundPattern}
          className={"absolute -z-3 opacity-90"}
          colorArray={["#fff"]}
        />
      )}

      {cardType === "leader" && leaderBorder === "standard-white" && (
        <Image
          width={printReady ? 3677 : 3357}
          height={printReady ? 5011 : 4692}
          priority
          loading={"eager"}
          title={`${cardTypeRoute} card border`}
          className={`absolute top-0 max-w-full`}
          src={
            png
              ? `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/${printReady ? "Print-Ready-" : ""}Black-White-Pattern.png`
              : `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/${printReady ? "Print-Ready-" : ""}Black-White-Pattern.webp`
          }
          alt={`${cardTypeRoute} card border`}
          quality={quality}
          sizes={quality !== 100 ? "(max-width: 1400px) 100vw, 1400px" : ""}
        />
      )}
    </>
  );
}
