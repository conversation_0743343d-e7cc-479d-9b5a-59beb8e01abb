"use client";
import Image from "next/image";

import { useGetStoreState } from "@/helpers/useGetStoreState";
import { getCardType } from "@/helpers/getCardType";
const SPL2 = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Character-SP-V2-L2.svg`;
const printReadySPL2 = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Print-Ready-Character-SP-V2-L2.svg`;
const SPL1 = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Character-SP-V2-L1.svg`;
const printReadySPL1 = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Print-Ready-Character-SP-V2-L1.svg`;
const SPL3 = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Character-SP-V2-L3.svg`;
const printReadySPL3 = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Print-Ready-Character-SP-V2-L3.svg`;
const border = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Card-Border.svg`;
const printReadyBorder = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Print-Ready-Card-Border.svg`;
const borderEventStage = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Card-Border-Event-Stage-Don.svg`;
const printReadyBorderEventStage = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Print-Ready-Card-Border-Event-Stage-Don.svg`;
const borderEventEB02 = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Card-Border-Event-EB02-New.svg`;
const printReadyBorderEventEB02 = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Print-Ready-Card-Border-Event-EB02-New.svg`;
const srPattern = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Event-Stage-Character_SR-Border-Pattern.svg`;
const printReadySrPattern = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Print-Ready-Event-Stage-Character_SR-Border-Pattern.svg`;
const eventStagePattern = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Event-Stage-Character_SR-Border-Pattern-New.svg`;
const printReadyEventStagePattern = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Print-Ready-Event-Stage-Character_SR-Border-Pattern-New.svg`;
const secPattern = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/SEC-Border-Pattern.svg`;
const printReadySecPattern = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Print-Ready-SEC-Border-Pattern.svg`;
const innerBorder = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Character-Inner-Border.svg`;
const printReadyInnerBorder = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Print-Ready-Character-Inner-Border.svg`;
const innerBorderEventStage = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Event_Stage_Don-Inner-Border.svg`;
const printReadyInnerBorderEventStage = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Print-Ready-Event_Stage_Don-Inner-Border.svg`;
const innerBorderEventEB02 = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Card-Frame-Event-EB02-No-Wheel-Border.svg`;
const printReadyInnerBorderEventEB02 = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Print-Ready-Card-Frame-Event-EB02-No-Wheel-Border.svg`;
const leaderInnerBorder = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Leader-Inner-Border.svg`;
const printReadyLeaderInnerBorder = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Print-Ready-Leader-Inner-Border.svg`;
const leaderInnerBorderTop = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Leader-Inner-Border-Top-v2.svg`;
const printReadyLeaderInnerBorderTop = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Print-Ready-Leader-Inner-Border-Top-v2.svg`;
const leaderBorderPattern = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Leader-Border-Pattern-New.svg`;
const printReadyLeaderBorderPattern = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Print-Ready-Leader-Border-Pattern-New.svg`;
const eventStageAbility = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Event_Stage-Ability.svg`;
const printReadyEventStageAbility = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Print-Ready-Event_Stage-Ability.svg`;
const printReadyInnerBorderBottomShadow = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Print-Ready-Inner-Border-Bottom-Shadow-v2.svg`;
const innerBorderBottomShadow = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Inner-Border-Bottom-Shadow-v2.svg`;
const characterInnerBorderTop = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Character-Inner-Border-Top.svg`;
const printReadyCharacterInnerBorderTop = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Print-Ready-Character-Inner-Border-Top.svg`;
import { getCardColor } from "@/app/helpers/getCardColor";
import OnePieceReactSvgWithGradient from "@/components/OnePieceReactSvgWithGradient";
import React from "react";
import getMultiColorBorderGradientHex from "@/app/helpers/getMultiColorBorderGradientHex";
import getLeaderBorderGradientSVG from "@/app/helpers/getLeaderBorderGradientSVG";
import { getPatternGradientSVG } from "@/app/helpers/getPatternGradientSVG";
import OnePieceLeaderBorderReactSVGWithGradientOP from "@/components/OnePieceLeaderBorderReactSVGWithGradientOP";
import { getMultiColorGradientHex } from "@/app/helpers/getMultiColorGradientHex";
import { getCardColorHex } from "@/app/helpers/getCardColorHex";

export default function CardBorder({
  cardType,
  quality,
  png = false,
  className = "",
  zIndex = 0,
}: {
  cardType: "character" | "leader" | "event" | "stage" | "don";
  quality: number;
  png?: boolean;
  className?: string;
  zIndex?: number;
}) {
  const printReady = useGetStoreState("printReady");
  const cardTypeRoute = getCardType(cardType);
  const abilityBackground = useGetStoreState("abilityBackground");
  const characterBorder = useGetStoreState("characterBorder");

  const colorArray = useGetStoreState("colorArray");
  const color = colorArray[0];
  const color2 = colorArray[colorArray.length - 1];
  const foilBorder = useGetStoreState("foilBorder");
  const leaderBorderEnabled = useGetStoreState("leaderBorderEnabled");
  const leaderBorder = useGetStoreState("leaderBorder");
  const eventBorder = useGetStoreState("eventBorder");
  const cardColor = getCardColor(color);
  /*  let leaderBorderRoute;
  if (leaderBorder === "standard" || leaderBorder === "black") {
    leaderBorderRoute = "Color-Leader";
  }*/

  // Use the multi-color border gradient function if we have more than 2 colors
  let [backgroundGradientSVG, backgroundBlendModeSVG, patternGradientSVG] =
    (Array.isArray(colorArray) &&
      colorArray.length > 2 &&
      cardType !== "leader") ||
    (Array.isArray(colorArray) &&
      colorArray.length > 2 &&
      cardType === "leader" &&
      leaderBorder !== "rainbow")
      ? getMultiColorBorderGradientHex(colorArray, leaderBorder, cardType)
      : getLeaderBorderGradientSVG(color, color2, leaderBorder, cardType);

  // For multiple colors, set the appropriate blend mode
  if (
    (Array.isArray(colorArray) &&
      colorArray.length >= 2 &&
      color !== color2 &&
      cardType !== "leader") ||
    (Array.isArray(colorArray) &&
      colorArray.length >= 3 &&
      color !== color2 &&
      cardType === "leader")
  ) {
    // Use screen for 2 colors, and soft-light for 3+ colors for better blending with our overlapping gradients
    backgroundBlendModeSVG = colorArray.length === 2 ? "screen" : "soft-light";
  }

  // Generate a custom pattern gradient for multiple colors
  if (Array.isArray(colorArray) && colorArray.length > 2) {
    patternGradientSVG = getPatternGradientSVG(colorArray);
  }
  if (cardType === "character" && characterBorder === "sec") {
    backgroundGradientSVG = [[{ color: "#D5B842" }, { color: "#D5B842" }]];
  }
  if (cardType === "character" && characterBorder === "sr") {
    backgroundGradientSVG = [[{ color: "#DAE1E8" }, { color: "#DAE1E8" }]];
  }
  if (cardType === "character" && characterBorder === "standard") {
    backgroundGradientSVG = [[{ color: "#fff" }, { color: "#fff" }]];
  }
  if (cardType === "event") {
    backgroundGradientSVG = [[{ color: "#D5B563" }, { color: "#D5B563" }]];
    patternGradientSVG = [[{ color: "#A18D51" }, { color: "#A18D51" }]];
    if (foilBorder) {
      backgroundGradientSVG = [[{ color: "#A18D51" }, { color: "#A18D51" }]];
      patternGradientSVG = [[{ color: "#E5D181" }, { color: "#E5D181" }]];
    }
  }

  if (cardType === "stage") {
    backgroundGradientSVG = [[{ color: "#152A54" }, { color: "#152A54" }]];
    patternGradientSVG = [[{ color: "#4969AA" }, { color: "#4969AA" }]];
  }
  if (cardType === "don") {
    backgroundGradientSVG = [[{ color: "#000" }, { color: "#000" }]];
  }
  const colorHexValues = colorArray.map((color) => getCardColorHex({ color }));
  return (
    <>
      {cardType === "character" && (
        <>
          {/*Border around the card for standard, sr, and sec */}
          {(characterBorder === "standard" ||
            characterBorder === "sr" ||
            characterBorder === "sec") && (
            <OnePieceReactSvgWithGradient
              printReady={printReady}
              printReadySrc={printReadyBorder}
              standardSrc={border}
              className={`absolute top-0 ${className}`}
              colorArray={backgroundGradientSVG}
              style={{ zIndex: zIndex }}
            />
          )}
          {/*Border pattern around the card for sr and sec */}
          {(characterBorder === "sr" || characterBorder === "sec") && (
            <OnePieceReactSvgWithGradient
              printReady={printReady}
              printReadySrc={
                characterBorder === "sr"
                  ? printReadySrPattern
                  : printReadySecPattern
              }
              standardSrc={characterBorder === "sr" ? srPattern : secPattern}
              className={`absolute top-0 ${className}`}
              colorArray={characterBorder === "sr" ? ["#fff"] : ["#F6EF9D"]}
              style={{ zIndex: zIndex }}
            />
          )}
          {/*It has to be in CharacterCardBorderBottom because it's bellow the foreground if it's here.*/}
          {/*It also has to be here for the cropper.*/}
          {characterBorder === "sp-v2" && cardType === "character" && (
            <>
              {/*SP V2 Card name background 1 (dimmer version)*/}
              <OnePieceReactSvgWithGradient
                printReady={printReady}
                printReadySrc={printReadySPL1}
                standardSrc={SPL1}
                className={`absolute top-0 z-4 brightness-50`}
                colorArray={colorHexValues}
              />
              {/*SP V2 Card name background 2 (brighter version)*/}
              <OnePieceReactSvgWithGradient
                printReady={printReady}
                printReadySrc={printReadySPL2}
                standardSrc={SPL2}
                className={`absolute top-0 z-4`}
                colorArray={colorHexValues}
              />
              <OnePieceReactSvgWithGradient
                printReady={printReady}
                printReadySrc={printReadySPL3}
                standardSrc={SPL3}
                className={`absolute top-0 z-4`}
                // colorArray={colorHexValues}
              />
            </>
          )}

          {leaderBorderEnabled && (
            // Character Inner border shadow outline

            <OnePieceReactSvgWithGradient
              printReady={printReady}
              printReadySrc={printReadyInnerBorder}
              standardSrc={innerBorder}
              className={`border-gradient-shadow-svg ${className} ${(characterBorder === "none" || characterBorder === "sp-v2") && cardType === "character" ? "opacity-0" : "opacity-[7.5%]"}`}
              colorArray={["#000"]}
              style={{ zIndex: zIndex }}
            />
          )}
          {!leaderBorderEnabled && (
            <>
              {/* Character Inner border shadow outline top*/}
              <OnePieceReactSvgWithGradient
                printReady={printReady}
                printReadySrc={printReadyCharacterInnerBorderTop}
                standardSrc={characterInnerBorderTop}
                className={`border-gradient-shadow-svg ${className} ${(characterBorder === "none" || characterBorder === "sp-v2") && cardType === "character" ? "opacity-0" : "opacity-[7.5%]"}`}
                colorArray={["#000"]}
                style={{ zIndex: zIndex }}
              />
              {(characterBorder === "sec" || characterBorder === "sec-aa") && (
                <OnePieceReactSvgWithGradient
                  printReady={printReady}
                  printReadySrc={printReadyInnerBorderBottomShadow}
                  standardSrc={innerBorderBottomShadow}
                  className={`border-gradient-shadow-svg ${className} opacity-[7.5%]`}
                  colorArray={["#000"]}
                  style={{ zIndex: zIndex }}
                />
              )}
            </>
          )}
          {/*Character Inner border*/}
          <OnePieceReactSvgWithGradient
            printReady={printReady}
            printReadySrc={printReadyInnerBorder}
            standardSrc={innerBorder}
            className={`border-bottom-gradient-svg top-0 ${className} ${(characterBorder === "none" || characterBorder === "sp-v2") && cardType === "character" ? "opacity-0" : ""}`}
            colorArray={
              Array.isArray(colorArray)
                ? getMultiColorGradientHex(colorArray)
                : [cardColor]
            }
            style={{ zIndex: zIndex }}
          />
        </>
      )}

      {cardType === "leader" && leaderBorder !== "full-art" && (
        <>
          <OnePieceLeaderBorderReactSVGWithGradientOP
            printReady={printReady}
            printReadySrc={printReadyBorder}
            standardSrc={border}
            className={`border-bottom-gradient-svg top-0 ${
              leaderBorder === "standard" ||
              leaderBorder === "AA-white" ||
              leaderBorder === "AA-black-and-white" ||
              leaderBorder === "standard-white" ||
              leaderBorder === "rainbow"
                ? "opacity-100"
                : "opacity-0!"
            } ${className} `}
            colorArray={backgroundGradientSVG}
            style={{ zIndex: zIndex }}
            leaderBorder={leaderBorder}
            blendMode={backgroundBlendModeSVG}
          />

          {leaderBorder === "standard-white" && (
            <Image
              width={printReady ? 3677 : 3357}
              height={printReady ? 5011 : 4692}
              priority
              loading={"eager"}
              title={`${cardTypeRoute} card border`}
              className={`absolute top-0 max-w-full ${className}`}
              src={
                png
                  ? `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/${printReady ? "Print-Ready-" : ""}Black-White-Border-Pattern.png`
                  : `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/${printReady ? "Print-Ready-" : ""}Black-White-Border-Pattern.webp`
              }
              alt={`${cardTypeRoute} card border`}
              quality={quality}
              style={{ zIndex: zIndex }}
              sizes={quality !== 100 ? "(max-width: 1400px) 100vw, 1400px" : ""}
            />
          )}

          {leaderBorderEnabled && (
            <OnePieceReactSvgWithGradient
              printReady={printReady}
              printReadySrc={printReadyLeaderInnerBorder}
              standardSrc={leaderInnerBorder}
              className={`border-gradient-shadow-svg top-0 ${className} opacity-[15%]`}
              colorArray={["#000"]}
              style={{ zIndex: zIndex }}
            />
          )}
          {!leaderBorderEnabled && (
            <>
              <OnePieceReactSvgWithGradient
                printReady={printReady}
                printReadySrc={printReadyLeaderInnerBorderTop}
                standardSrc={leaderInnerBorderTop}
                className={`border-gradient-shadow-svg top-0 ${className} opacity-[15%]`}
                colorArray={["#000"]}
                style={{ zIndex: zIndex }}
              />
            </>
          )}

          {(leaderBorder === "25th" ||
            leaderBorder === "standard" ||
            leaderBorder === "rainbow") && (
            <>
              <OnePieceReactSvgWithGradient
                printReady={printReady}
                printReadySrc={printReadyLeaderInnerBorder}
                standardSrc={leaderInnerBorder}
                className={`border-bottom-gradient-svg top-0 ${className}`}
                colorArray={["#080809"]}
                style={{ zIndex: zIndex }}
              />
            </>
          )}

          <>
            <OnePieceReactSvgWithGradient
              printReady={printReady}
              printReadySrc={printReadyLeaderInnerBorder}
              standardSrc={leaderInnerBorder}
              className={`border-bottom-gradient-svg top-0 ${className} `}
              colorArray={leaderBorder === "AA-white" ? ["#fff"] : ["#080809"]}
              style={{ zIndex: zIndex }}
            />
            <OnePieceLeaderBorderReactSVGWithGradientOP
              leaderBorder={leaderBorder}
              printReady={printReady}
              printReadySrc={printReadyLeaderBorderPattern}
              standardSrc={leaderBorderPattern}
              className={`border-bottom-gradient-svg top-0 ${className} ${leaderBorder === "AA-white" ? "opacity-70" : leaderBorder === "AA-black" || leaderBorder === "AA-black-and-white" ? "opacity-50" : "opacity-0"}`}
              colorArray={patternGradientSVG}
              blendMode={backgroundBlendModeSVG}
              style={{ zIndex: zIndex }}
            />
          </>
        </>
      )}

      {((cardType === "event" && eventBorder === "standard") ||
        cardType === "stage") && (
        <>
          {/* Card border around the card for event and stage */}
          <OnePieceReactSvgWithGradient
            printReady={printReady}
            printReadySrc={printReadyBorderEventStage}
            standardSrc={borderEventStage}
            className={`border-bottom-gradient-svg top-0 ${className} `}
            colorArray={backgroundGradientSVG}
            style={{ zIndex: zIndex }}
          />

          {abilityBackground && (
            <>
              <OnePieceReactSvgWithGradient
                printReady={printReady}
                printReadySrc={printReadyEventStageAbility}
                standardSrc={eventStageAbility}
                className={`border-bottom-gradient-svg top-0 ${className} `}
                colorArray={["#F2F2DC"]}
                style={{ zIndex: zIndex }}
              />
            </>
          )}
          {/* Card border pattern for event and stage*/}
          <OnePieceReactSvgWithGradient
            printReady={printReady}
            printReadySrc={printReadyEventStagePattern}
            standardSrc={eventStagePattern}
            className={`border-bottom-gradient-svg top-0 ${className} `}
            colorArray={patternGradientSVG}
            style={{ zIndex: zIndex }}
          />

          {/* Inner border shadow for event and stage */}
          <OnePieceReactSvgWithGradient
            printReady={printReady}
            printReadySrc={printReadyInnerBorderEventStage}
            standardSrc={innerBorderEventStage}
            className={`border-gradient-shadow-svg top-0 ${className} opacity-[15%]`}
            colorArray={["#000"]}
            style={{ zIndex: zIndex }}
          />

          {/* Inner border for event and stage */}
          <OnePieceReactSvgWithGradient
            printReady={printReady}
            printReadySrc={printReadyInnerBorderEventStage}
            standardSrc={innerBorderEventStage}
            className={`border-bottom-gradient-svg top-0 ${className}`}
            colorArray={
              Array.isArray(colorArray)
                ? getMultiColorGradientHex(colorArray)
                : [cardColor]
            }
            style={{ zIndex: zIndex }}
          />
        </>
      )}
      {eventBorder === "op-10" && cardType === "event" && (
        <>
          {/*SP V2 Card name background 1 (dimmer version)*/}
          <OnePieceReactSvgWithGradient
            printReady={printReady}
            printReadySrc={printReadySPL1}
            standardSrc={SPL1}
            className={`border-bottom-gradient-svg top-0 ${className} brightness-50`}
            colorArray={["#160B0D"]}
            style={{ zIndex: 4 }}
          />
          <OnePieceReactSvgWithGradient
            printReady={printReady}
            printReadySrc={printReadySPL3}
            standardSrc={SPL3}
            className={`border-bottom-gradient-svg top-0 ${className} brightness-50`}
            colorArray={["#ff0303"]}
            style={{ zIndex: 4 }}
          />
        </>
      )}
      {cardType === "event" && eventBorder === "eb-02" && (
        <>
          <OnePieceReactSvgWithGradient
            printReady={printReady}
            printReadySrc={printReadyBorderEventEB02}
            standardSrc={borderEventEB02}
            className={`absolute top-0 max-w-full ${className}`}
            style={{ zIndex: zIndex }}
          />

          {/* Inner border for event and stage */}
          <OnePieceReactSvgWithGradient
            printReady={printReady}
            printReadySrc={printReadyInnerBorderEventEB02}
            standardSrc={innerBorderEventEB02}
            className={`border-bottom-gradient-svg top-0 ${className}`}
            colorArray={
              Array.isArray(colorArray)
                ? getMultiColorGradientHex(colorArray, false, {
                    border: cardType === "event" && eventBorder === "eb-02",
                    other: false,
                  })
                : [cardColor]
            }
            style={{ zIndex: zIndex }}
          />
        </>
      )}

      {cardType === "don" && (
        <>
          {/* Card border around the card for don*/}
          <OnePieceReactSvgWithGradient
            printReady={printReady}
            printReadySrc={printReadyBorderEventStage}
            standardSrc={borderEventStage}
            className={`border-bottom-gradient-svg top-0 ${className}`}
            colorArray={backgroundGradientSVG}
            style={{ zIndex: zIndex }}
          />

          {/* Inner border shadow for don */}
          <OnePieceReactSvgWithGradient
            printReady={printReady}
            printReadySrc={printReadyInnerBorderEventStage}
            standardSrc={innerBorderEventStage}
            className={`border-gradient-shadow-svg top-0 ${className} opacity-[8%]`}
            colorArray={["#000", "#fff"]}
            style={{ zIndex: zIndex }}
            gradientDirection={"vertical"}
            blendMode={"screen"}
            flatBlendFor2Colors={false}
          />
          {/* Inner border for don */}
          <OnePieceReactSvgWithGradient
            printReady={printReady}
            printReadySrc={printReadyInnerBorderEventStage}
            standardSrc={innerBorderEventStage}
            className={`border-bottom-gradient-svg top-0 ${className}`}
            colorArray={["#000"]}
            style={{ zIndex: zIndex }}
          />
        </>
      )}
    </>
  );
}
