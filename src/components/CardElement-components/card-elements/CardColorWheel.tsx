"use client";
const wheelFrame = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Wheel-Frame-New.png`;
const optimizedWheelFrame = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Wheel-Frame-New.webp`;
const wheelFrameShadows = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Wheel-Frame-Shadows-New.png`;
const wheelFrameOutline = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Wheel-Frame-Outline.png`;
const optimizedWheelFrameShadows = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Wheel-Frame-Shadows-New.webp`;
const optimizedWheelFrameOutline = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Wheel-Frame-Outline.webp`;
const wheelFrameHighlights = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Wheel-Frame-Highlights-New.png`;
const optimizedWheelFrameHighlights = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Wheel-Frame-Highlights-New.webp`;
const triangles = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Triangles-New.png`;
const optimizedTriangles = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Triangles-New.webp`;
import { useGetStoreState } from "@/helpers/useGetStoreState";
import { CharacterColor } from "@/types";
import { getCardColor } from "@/app/helpers/getCardColor";
import ColorWheelTriangle from "@/components/CardElement-components/card-elements/ColorWheelTriangle";
import { getColorWheelTrianglePosition } from "@/app/helpers/getColorWheelTrianglePosition";
import { getColorWheelGradient } from "@/app/helpers/getColorWheelGradient";

export default function CardColorWheel({
  png = false,
  cardKind,
}: {
  png?: boolean;
  cardKind: "character" | "leader" | "event" | "stage" | "don";
}) {
  const printReady = useGetStoreState("printReady");
  const colorArray = useGetStoreState("colorArray");
  const color = colorArray[0];
  const color2 = colorArray[colorArray.length - 1];

  const characterBorder = useGetStoreState("characterBorder");
  const cardColor = getCardColor(color, characterBorder === "none");
  const leaderBorder = useGetStoreState("leaderBorder");
  const eventBorder = useGetStoreState("eventBorder");
  // Use all colors from the colorArray
  const colors: CharacterColor[] = Array.isArray(colorArray)
    ? [...colorArray]
    : [color];

  let triangleBrightness = "";
  let wheelColor = "";
  let triangleColor = "";
  if (
    cardKind === "character" ||
    cardKind === "event" ||
    cardKind === "stage"
  ) {
    wheelColor = cardColor;
    triangleColor = cardColor;

    if (color === "red" || color === "black") {
      triangleBrightness = "brightness-[65%]";
    } else {
      triangleBrightness = "brightness-[75%]";
    }
  }
  if (cardKind === "leader") {
    wheelColor = "#131313";
    triangleColor = "#131313";
    colors.push(color2);
    if (leaderBorder === "standard") {
      triangleBrightness = "brightness-[65%]";
    }
    if (leaderBorder === "AA-white") {
      triangleBrightness = "brightness-[93%]";
      wheelColor = "#fff";
      triangleColor = "#fff";
    }
    if (
      leaderBorder === "AA-black" ||
      leaderBorder === "AA-black-and-white" ||
      leaderBorder === "full-art"
    ) {
      triangleBrightness = "brightness-[93%]";

      wheelColor = "#131313";
      triangleColor = "#fff";
    }
    if (leaderBorder === "rainbow") {
      // For rainbow border, always use the predefined rainbow colors
      colors.length = 0; // Clear the array
      colors.push("blue", "green", "red", "yellow", "black", "purple");
    }
  }
  if (cardKind !== "don")
    return (
      <div
        className={
          printReady
            ? "absolute top-[3.00%] left-[4.35%] h-[93.8%] w-[91.3%]"
            : ""
        }
      >
        {/* Inactive triangle placeholders */}
        {cardKind !== "event" && (
          <div
            className={`color-wheel-triangles ${triangleBrightness}`}
            style={{
              background:
                cardKind === "character" || cardKind === "stage"
                  ? getColorWheelGradient(colorArray)
                  : `linear-gradient(135deg, ${triangleColor},  ${triangleColor})`,
              maskImage: `url(${png ? triangles : optimizedTriangles})`,
            }}
          ></div>
        )}
        {cardKind === "event" && eventBorder !== "eb-02" && (
          <div
            className={`color-wheel-triangles ${triangleBrightness}`}
            style={{
              background: getColorWheelGradient(colorArray),

              maskImage: `url(${png ? triangles : optimizedTriangles})`,
            }}
          ></div>
        )}
        {colors.map((color, i) => {
          const cardColor = getCardColor(
            color,
            false,
            false,
            false,
            false,
            false,
            false,
            undefined,
            {
              border: false,
              other: eventBorder === "eb-02" && cardKind === "event",
            },
          );

          const position = getColorWheelTrianglePosition(color);
          return (
            <ColorWheelTriangle
              cardKind={cardKind}
              eventBorder={eventBorder}
              key={`${position}-${i}`}
              cardColor={cardColor}
              position={position}
              png={png}
            />
          );
        })}
        {cardKind !== "event" && (
          <div
            className={"color-wheel-frame-shadow"}
            style={{
              maskImage: `url(${png ? wheelFrame : optimizedWheelFrame})`,
            }}
          ></div>
        )}
        {cardKind === "event" && eventBorder !== "eb-02" && (
          <div
            className={"color-wheel-frame-shadow"}
            style={{
              maskImage: `url(${png ? wheelFrame : optimizedWheelFrame})`,
            }}
          ></div>
        )}
        {/*Color wheel frame*/}
        {cardKind !== "event" && (
          <div
            className={"color-wheel-frame"}
            style={{
              maskImage: `url(${png ? wheelFrame : optimizedWheelFrame})`,
              background:
                cardKind === "character" || cardKind === "stage"
                  ? getColorWheelGradient(colorArray)
                  : `linear-gradient(135deg, ${wheelColor},  ${wheelColor})`,
            }}
          ></div>
        )}
        {cardKind === "event" && eventBorder !== "eb-02" && (
          <div
            className={"color-wheel-frame"}
            style={{
              maskImage: `url(${png ? wheelFrame : optimizedWheelFrame})`,
              background: getColorWheelGradient(colorArray),
            }}
          ></div>
        )}

        {/*Color wheel frame Outline*/}
        {cardKind === "event" && eventBorder === "eb-02" && (
          <div
            className={"color-wheel-outline"}
            style={{
              maskImage: `url(${png ? wheelFrameOutline : optimizedWheelFrameOutline})`,
              background: getColorWheelGradient(colorArray, {
                border: false,
                other: true,
              }),
            }}
          ></div>
        )}

        {(cardKind === "leader" && leaderBorder === "AA-black") ||
        (cardKind === "leader" && leaderBorder === "AA-black-and-white") ||
        (cardKind === "event" && eventBorder === "eb-02") ? null : (
          <>
            <div
              className={"color-wheel-highlights"}
              style={{
                maskImage: `url(${png ? wheelFrameHighlights : optimizedWheelFrameHighlights})`,
              }}
            ></div>
            <div
              className={"color-wheel-shadows"}
              style={{
                maskImage: `url(${png ? wheelFrameShadows : optimizedWheelFrameShadows})`,
              }}
            ></div>
          </>
        )}
      </div>
    );
  return null;
}
