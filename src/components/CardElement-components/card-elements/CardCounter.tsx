"use client";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import { CharacterBorder } from "@/types";

const counter = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/CounterV5.svg`;
const counterMulticolor = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Counter-Multicolor-new.svg`;
const printReadyCounterMulticolor = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Print-Ready-Counter-Multicolor.svg`;
const printReadyCounter = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Print-Ready-CounterV5.svg`;
import { getCardColor } from "@/app/helpers/getCardColor";
import OnePieceReactSvgWithGradient from "@/components/OnePieceReactSvgWithGradient";
import React from "react";
import { getMultiColorGradientHex } from "@/app/helpers/getMultiColorGradientHex";
export default function CardCounter() {
  const printReady = useGetStoreState("printReady");
  const colorArray = useGetStoreState("colorArray");
  const color = colorArray[0];
  const characterBorder = useGetStoreState(
    "characterBorder",
  ) as CharacterBorder;
  const counterText = useGetStoreState("counterText");
  const characterBorderEnabled = useGetStoreState("leaderBorderEnabled");
  const shadowColor = getCardColor(color, characterBorder === "none");
  if (
    counterText.length > 0 &&
    characterBorder !== "none" &&
    characterBorder !== "sp-v2"
  ) {
    if (colorArray.length > 2) {
      return (
        <>
          {/*SVG CODE*/}
          <OnePieceReactSvgWithGradient
            printReady={printReady}
            printReadySrc={printReadyCounterMulticolor}
            standardSrc={counterMulticolor}
            className={`border-bottom-gradient-svg absolute top-0`}
            colorArray={getMultiColorGradientHex(colorArray)}
            style={{
              zIndex: !characterBorderEnabled ? "0" : "4",
            }}
          />
        </>
      );
    }
    return (
      <>
        <OnePieceReactSvgWithGradient
          printReady={printReady}
          printReadySrc={printReadyCounter}
          standardSrc={counter}
          className={`absolute`}
          colorArray={[shadowColor]}
          style={{
            zIndex: !characterBorderEnabled ? "0" : "4",
            left: printReady ? `3.945155289638293%` : "4.3%",
            top: printReady ? `29.38474875274396%` : "31.358%",
            height: printReady ? `30.047960293717703%` : "28.75%",
            width: printReady ? `13.012268153385913%` : "4.80%",
          }}
        />
      </>
    );
  }

  return null;
}
//
