"use client";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import { useGetResponsiveFontsizeInPx } from "@/hooks/useGetResponsiveFontsizeInPx";
import { CharacterBorder } from "@/types";
import { getTextColor } from "@/app/helpers/getTextColor";
import { getCardColor } from "@/app/helpers/getCardColor";
import { getPrintReadyDistanceString } from "@/app/helpers/getPrintReadyDistance";
import {
  getPrintReadySizeNumber,
  getPrintReadySizeString,
} from "@/app/helpers/getPrintReadySize";
export default function CardCounterText() {
  const printReady = useGetStoreState("printReady");
  const counterText = useGetStoreState("counterText") as string;
  const characterBorder = useGetStoreState(
    "characterBorder",
  ) as CharacterBorder;
  const colorArray = useGetStoreState("colorArray");
  const color = colorArray[0];
  const textColor = getTextColor(
    color,
    characterBorder,
    "character",
    false,
    "counter",
    colorArray,
  );
  const shadowColor = getCardColor(
    color,
    characterBorder === "none",
    characterBorder === "sec" || characterBorder === "sec-aa",
    characterBorder === "sp-v2",
    false,
    false,
    true,
    colorArray,
  );
  const colorIsYellow =
    color.toLocaleLowerCase().includes("yellow") && colorArray.length <= 2;
  const fontsize1 = useGetResponsiveFontsizeInPx({
    desiredTextSizeInPx: 17.44,
  });
  const fontsize2 = useGetResponsiveFontsizeInPx({ desiredTextSizeInPx: 28 });
  const fontsize3 = useGetResponsiveFontsizeInPx({ desiredTextSizeInPx: 22 });
  if (counterText.length > 0) {
    return (
      <div
        className={"absolute rotate-[-90deg] text-left"}
        style={{
          zIndex: 4,
          bottom: printReady
            ? getPrintReadyDistanceString("51.47%", "top-or-bottom", printReady)
            : "51.37%",
          left: printReady
            ? getPrintReadyDistanceString("-3.22%", "left-or-right", printReady)
            : "-3.1%",
          height: getPrintReadySizeString("6.9759%", printReady),
          width: getPrintReadySizeString("25%", printReady),
        }}
      >
        <div
          style={{
            fontSize: `${getPrintReadySizeNumber(fontsize1, printReady)}px`,
          }}
          className={`font-hind-siliguri relative uppercase ${
            colorIsYellow &&
            characterBorder !== "none" &&
            characterBorder !== "sp-v2"
              ? "font-semibold text-black"
              : "font-medium"
          } ${color.toLocaleLowerCase().includes("yellow") && characterBorder === "none" ? "text-neutral-950" : ""} `}
        >
          <span
            className={`text-outline ${characterBorder === "sp-v2" || characterBorder === "none" ? (characterBorder === "sp-v2" && color === "yellow" ? "text-outline-small" : "text-outline-medium") : "text-outline-small"} font-character-power absolute top-[-19%] left-[-11.4%] text-[145%]`}
            style={{
              backgroundImage: `${characterBorder !== "none" ? `linear-gradient(to right, ${color === "yellow" ? "#000" : shadowColor},  ${color === "yellow" ? "#000" : shadowColor})` : "linear-gradient(to right, #fff,  #fff)"} `,
              color: `#E9DB6C`,
            }}
          >
            :
          </span>
          <span
            className={"text-outline text-outline-medium text-[1.01em]"}
            style={{
              backgroundImage: `${characterBorder !== "none" ? `linear-gradient(to right, ${shadowColor},  ${shadowColor})` : "linear-gradient(to right, #fff,  #fff)"}`,
              color: `${textColor !== "brightness-0" ? textColor : ""}`,
              letterSpacing: "0.03em",
            }}
          >
            counter
          </span>
          <div
            className={"absolute text-left font-normal"}
            style={{
              right: printReady
                ? getPrintReadyDistanceString(
                    "1.74%",
                    "left-or-right",
                    printReady,
                  )
                : "2%",
              top: printReady
                ? getPrintReadyDistanceString(
                    "-33.8%",
                    "top-or-bottom",
                    printReady,
                  )
                : "-28%",
              height: getPrintReadySizeString("0.4359%", printReady),
              width: getPrintReadySizeString("45%", printReady),
            }}
          >
            <span
              style={{
                fontSize: `${getPrintReadySizeNumber(fontsize2, printReady)}px`,
                color: `${textColor !== "brightness-0" ? textColor : ""}`,
                backgroundImage: `${characterBorder !== "none" ? `linear-gradient(to right, ${shadowColor},  ${shadowColor})` : "linear-gradient(to right, #fff,  #fff)"}`,
              }}
              className={`text-outline text-outline-medium relative inline-block font-medium`}
            >
              +
            </span>
          </div>
          <div
            className={"absolute text-left font-normal"}
            style={{
              right: printReady
                ? getPrintReadyDistanceString(
                    "-12.3%",
                    "left-or-right",
                    printReady,
                  )
                : "-10.8%",
              top: printReady
                ? getPrintReadyDistanceString(
                    "-14%",
                    "top-or-bottom",
                    printReady,
                  )
                : "-10%",
              height: getPrintReadySizeString("0.4359%", printReady),
              width: getPrintReadySizeString("45%", printReady),
            }}
          >
            <span
              style={{
                fontSize: `${getPrintReadySizeNumber(fontsize3, printReady)}px`,
                color: `${textColor !== "brightness-0" ? textColor : ""}`,
                backgroundImage: `${characterBorder !== "none" ? `linear-gradient(to right, ${shadowColor},  ${shadowColor})` : "linear-gradient(to right, #fff,  #fff)"}`,
                letterSpacing: `0.09em`,
              }}
              className="text-outline text-outline-medium font-pixymbols relative inline-block tracking-widest"
            >
              {counterText.startsWith("1")
                ? `1 ${counterText.slice(1)}`
                : counterText}
            </span>
          </div>
        </div>
      </div>
    );
  } else {
    return null;
  }
}
// + 7085 561
