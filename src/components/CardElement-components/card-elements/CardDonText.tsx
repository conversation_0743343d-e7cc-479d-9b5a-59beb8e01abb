"use client";
import { useGetStoreState } from "@/helpers/useGetStoreState";

import { useGetResponsiveFontsizeInPx } from "@/hooks/useGetResponsiveFontsizeInPx";
import { getPrintReadyDistanceString } from "@/app/helpers/getPrintReadyDistance";
import {
  getPrintReadySizeNumber,
  getPrintReadySizeString,
} from "@/app/helpers/getPrintReadySize";

export default function CardDonText() {
  const printReady = useGetStoreState("printReady");

  const donText = useGetStoreState("donText") as string;
  const donPower = useGetStoreState("donPower") as string;
  const donFontSize = useGetStoreState("donFontSize") as number;
  const fontsize = useGetResponsiveFontsizeInPx({
    desiredTextSizeInPx: donFontSize,
  });
  return (
    <div
      className={"absolute flex items-center justify-center text-center"}
      style={{
        zIndex: 4,
        left: getPrintReadyDistanceString("5.5%", "left-or-right", printReady),
        bottom: getPrintReadyDistanceString(
          "4.86%",
          "top-or-bottom",
          printReady,
          4,
        ),
        height: getPrintReadySizeString("6.9759%", printReady),

        width: getPrintReadySizeString("88.75%", printReady),
      }}
    >
      <p
        style={{
          fontSize: `${getPrintReadySizeNumber(fontsize, printReady)}px`,
        }}
        className={`relative max-w-[87.695%] overflow-hidden align-middle tracking-[0.01em] ${"text-neutral-50"} ${"font-medium"} font-mukta flex flex-row items-end justify-between`}
      >
        <span className={"mb-[0.13em]"}>
          {donText.length === 0 ? "Your Turn" : donText}
        </span>
        <span className={"opacity-0"}>+</span>
        <span className={""}>
          <span
            className={"font-roboto! relative font-extrabold"}
            style={{
              fontSize: `${getPrintReadySizeNumber(1.2, printReady)}em `,
            }}
          >
            +
          </span>
          <span
            style={{
              fontSize: `${getPrintReadySizeNumber(fontsize ? fontsize + 3 : fontsize, printReady)}px`,

              letterSpacing: `0.09em`,
            }}
            className="text-outline text-outline-medium font-pixymbols relative inline-block tracking-widest"
          >
            {donPower.startsWith("1") ? `1` : ""}
          </span>
          <span
            style={{
              fontSize: `${getPrintReadySizeNumber(fontsize ? fontsize + 3 : fontsize, printReady)}px`,

              letterSpacing: `0.09em`,
            }}
            className="text-outline text-outline-medium font-pixymbols relative inline-block tracking-widest"
          >
            {donPower.startsWith("1") ? `${donPower.slice(1)}` : donPower}
          </span>
        </span>
      </p>
    </div>
  );
}
