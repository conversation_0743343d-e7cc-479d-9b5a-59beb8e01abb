"use client";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import { useGetResponsiveFontsizeInPx } from "@/hooks/useGetResponsiveFontsizeInPx";

import { getCardColor } from "@/app/helpers/getCardColor";
import { getTextColor } from "@/app/helpers/getTextColor";
import {
  getPrintReadySizeNumber,
  getPrintReadySizeString,
} from "@/app/helpers/getPrintReadySize";
import { getPrintReadyDistanceString } from "@/app/helpers/getPrintReadyDistance";

export default function CardKind({
  cardType,
}: {
  cardType: "character" | "leader" | "event" | "stage" | "don";
}) {
  const printReady = useGetStoreState("printReady");
  const characterBorder = useGetStoreState("characterBorder");
  const eventBorder = useGetStoreState("eventBorder");
  const colorArray = useGetStoreState("colorArray");
  const color = colorArray[0];
  const shadowColor = getCardColor(
    color,
    cardType === "character" && characterBorder === "none",
    false,
    cardType === "character" && characterBorder === "sp-v2",
    false,
    false,
    false,
    colorArray,
  );
  const leaderBorder = useGetStoreState("leaderBorder");
  const spacing = useGetResponsiveFontsizeInPx({ desiredTextSizeInPx: 6.3 });
  const fontsize = useGetResponsiveFontsizeInPx({
    desiredTextSizeInPx: 18.7,
  });
  const textColor = getTextColor(
    color,
    characterBorder,
    cardType,
    leaderBorder,
    "card_kind",
    colorArray,
    eventBorder,
  );
  let backgroundImage = "";
  let textOutline = "text-outline-large";
  if (
    (cardType === "character" ||
      cardType === "stage" ||
      (cardType === "event" && eventBorder !== "eb-02")) &&
    colorArray.length > 1
  ) {
    backgroundImage = `linear-gradient(to right, #000,  #000)`;
    textOutline = "text-outline-ms";
  }
  if (
    cardType === "character" &&
    (characterBorder === "none" || characterBorder === "sp-v2")
  ) {
    textOutline = "text-outline-large";
  }
  if (
    cardType === "character" &&
    (characterBorder === "none" ||
      characterBorder === "sec-aa" ||
      characterBorder === "sec" ||
      characterBorder === "sp-v2" ||
      colorArray.length > 1)
  ) {
    backgroundImage = `linear-gradient(to right, ${shadowColor},  ${shadowColor})`;
    if (
      cardType === "character" &&
      (characterBorder === "sec-aa" || characterBorder === "sec")
    ) {
      textOutline = "text-outline-ms";
    }
  }

  if (
    cardType === "character" &&
    (characterBorder === "mr" ||
      characterBorder === "sec-aa" ||
      characterBorder === "sec") &&
    color === "yellow" &&
    colorArray.length === 1
  ) {
    backgroundImage = `linear-gradient(to right, #000,  #000)`;
    textOutline = "text-outline-ms";
  }

  if (cardType === "leader" && leaderBorder === "full-art") {
    backgroundImage = `linear-gradient(to right, #fff,  #fff)`;
    textOutline = "text-outline-xxl";
  }
  if (cardType === "event" && eventBorder === "op-10") {
    backgroundImage = `linear-gradient(to right, #000,  #000)`;
    textOutline = "text-outline-large";
  }

  return (
    <div
      className={"absolute text-center"}
      style={{
        zIndex: 4,
        bottom: getPrintReadyDistanceString(
          "13.345%",
          "top-or-bottom",
          printReady,
          1,
        ),
        left: getPrintReadyDistanceString("36.2%", "left-or-right", printReady),
        width: getPrintReadySizeString("28.25%", printReady),
      }}
    >
      <p
        style={{
          letterSpacing: `${getPrintReadySizeNumber(spacing, printReady)}px`,
          fontSize: `${getPrintReadySizeNumber(fontsize, printReady)}px`,
          backgroundImage: backgroundImage,
          color: `${textColor !== "brightness-0" ? textColor : ""}`,
        }}
        className={`text-outline ${textOutline} font-balboa-plus-fill uppercase ${textColor === "brightness-0" ? "brightness-0" : ""} ${
          leaderBorder === "AA-white" && cardType === "leader"
            ? "text-stroke"
            : ""
        }`}
      >
        {cardType === "don" ? (
          <>
            <span>don</span>
            <span className={"pr-[0.5em] -tracking-[-0.05em] italic"}>!!</span>
            <span> CARD</span>
          </>
        ) : (
          cardType
        )}
      </p>
    </div>
  );
}
