"use client";
import { useTextBorderColor } from "@/hooks/useTextBorderColor";
import { useGetResponsiveFontsizeInPx } from "@/hooks/useGetResponsiveFontsizeInPx";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import { getCardColor } from "@/app/helpers/getCardColor";
import { getTextColor } from "@/app/helpers/getTextColor";

export default function CardMadeWith({
  cardType,
}: {
  cardType: "character" | "leader" | "event" | "stage" | "don";
}) {
  const printReady = useGetStoreState("printReady");
  const textColor = useTextBorderColor(cardType, "left");
  const fontsize = useGetResponsiveFontsizeInPx({
    desiredTextSizeInPx: printReady ? 9.72 : 10.72,
  });
  const leaderBorder = useGetStoreState("leaderBorder");
  const eventBorder = useGetStoreState("eventBorder");
  const characterBorder = useGetStoreState("characterBorder");
  const colorArray = useGetStoreState("colorArray");
  const color = colorArray[0];
  const shadowColor = getCardColor(color);
  const textColorSp = getTextColor(
    color,
    characterBorder,
    cardType,
    leaderBorder,
    "made_with",
    colorArray,
  );
  let backgroundImage = "";
  if (cardType === "character" && characterBorder === "none") {
    backgroundImage = `linear-gradient(to right, ${color === "yellow" ? "#000" : shadowColor},  ${color === "yellow" ? "#000" : shadowColor})`;
  }
  if (cardType === "character" && characterBorder !== "none") {
    backgroundImage = `linear-gradient(to right, white,  white)`;
  }
  if (cardType === "leader") {
    if (
      leaderBorder === "standard-white" ||
      leaderBorder === "25th" ||
      leaderBorder === "AA-black" ||
      leaderBorder === "AA-black-and-white" ||
      leaderBorder === "full-art"
    ) {
      backgroundImage = `linear-gradient(to right, #fff,  #fff)`;
    }
  }
  if (cardType === "event" && eventBorder === "op-10") {
    backgroundImage = `linear-gradient(to right, #fff,  #fff)`;
  }
  return (
    <div
      style={{
        fontSize: `${fontsize}px`,
        backgroundImage: backgroundImage,
        zIndex: 4,
      }}
      className={`text-outline text-outline-large absolute ${printReady ? "bottom-[4.1732%] left-[15.525%]" : "bottom-[1.0732%] left-[12.225%]"} font-geist-sans font-medium ${cardType === "character" && characterBorder === "none" ? "" : textColor} ${
        leaderBorder === "AA-black" && cardType === "leader"
          ? "text-neutral-950 [text-shadow:-0.0625em_-0.0625em_0_#fff,0.0625em_-0.0625em_0_#fff,-0.0625em_0.0625em_0_#fff,0.0625em_0.0625em_0_#fff]"
          : ""
      } ${
        (leaderBorder === "standard" || leaderBorder === "AA-white") &&
        cardType === "leader" &&
        color.toLocaleLowerCase().startsWith("yellow")
          ? "text-neutral-950"
          : ""
      } `}
    >
      <p
        className={""}
        style={{
          color:
            cardType === "character" &&
            characterBorder === "none" &&
            color.toLocaleLowerCase().startsWith("yellow")
              ? textColorSp
              : "",
        }}
      >
        Made with Ultimate TCG Card Maker
      </p>
    </div>
  );
}
