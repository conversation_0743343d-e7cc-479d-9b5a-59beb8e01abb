"use client";
import { useGetStoreState } from "@/helpers/useGetStoreState";

import { useGetResponsiveFontsizeInPx } from "@/hooks/useGetResponsiveFontsizeInPx";

import { getCardColor } from "@/app/helpers/getCardColor";
import { getTextColor } from "@/app/helpers/getTextColor";
import { getPrintReadyDistanceString } from "@/app/helpers/getPrintReadyDistance";
import {
  getPrintReadySizeNumber,
  getPrintReadySizeString,
} from "@/app/helpers/getPrintReadySize";

export default function CardName({
  cardType,
}: {
  cardType: "character" | "leader" | "event" | "stage" | "don";
}) {
  const printReady = useGetStoreState("printReady");
  const characterBorder = useGetStoreState("characterBorder");
  const colorArray = useGetStoreState("colorArray");
  const color = colorArray[0];
  let backgroundImage = "";
  const shadowColor = getCardColor(
    color,
    cardType === "character" &&
      (characterBorder === "none" || characterBorder === "sp-v2"),
  );
  const leaderBorder = useGetStoreState("leaderBorder");
  const eventBorder = useGetStoreState("eventBorder");
  const textColor = getTextColor(
    color,
    characterBorder,
    cardType,
    leaderBorder,
    false,
    colorArray,
    eventBorder,
  );
  const name = useGetStoreState("name") as string;
  const nameFontSize = useGetStoreState("nameFontSize") as number;
  const fontsize = useGetResponsiveFontsizeInPx({
    desiredTextSizeInPx: nameFontSize,
  });
  if (
    (cardType === "character" ||
      cardType === "stage" ||
      (cardType === "event" && eventBorder !== "eb-02")) &&
    colorArray.length > 1
  ) {
    backgroundImage = `linear-gradient(to right, #000,  #000)`;
  }
  if (
    cardType === "character" &&
    (characterBorder === "none" ||
      characterBorder === "sp-v2" ||
      (characterBorder === "sec-aa" && colorArray.length === 1) ||
      (characterBorder === "sec" && colorArray.length === 1))
  ) {
    backgroundImage = `linear-gradient(to right, ${shadowColor},  ${shadowColor})`;
  }
  if (
    cardType === "character" &&
    (characterBorder === "mr" ||
      characterBorder === "sec-aa" ||
      characterBorder === "sec") &&
    color === "yellow"
  ) {
    backgroundImage = `linear-gradient(to right, #000,  #000)`;
  }
  if (cardType === "leader" && leaderBorder === "full-art") {
    backgroundImage = `linear-gradient(to right, #fff,  #fff)`;
  }
  if (
    cardType === "character" &&
    colorArray.length > 1 &&
    characterBorder === "sp-v2"
  ) {
    backgroundImage = `linear-gradient(to right, #000,  #000)`;
  }
  if (cardType === "event" && eventBorder === "op-10") {
    backgroundImage = `linear-gradient(to right, #000,  #000)`;
  }
  return (
    <div
      className={"absolute flex items-center justify-center text-center"}
      style={{
        zIndex: 4,
        left: getPrintReadyDistanceString("5.5%", "left-or-right", printReady),
        bottom: getPrintReadyDistanceString(
          "6.86%",
          "top-or-bottom",
          printReady,
          4,
        ),
        height: getPrintReadySizeString("6.9759%", printReady),

        width: getPrintReadySizeString("88.75%", printReady),
      }}
    >
      <p
        style={{
          fontSize: `${getPrintReadySizeNumber(fontsize, printReady)}px`,
          backgroundImage: backgroundImage,
          color: `${textColor !== "brightness-0" ? textColor : ""}`,
        }}
        className={`text-outline ${(cardType === "character" && (characterBorder === "sec" || characterBorder === "mr" || characterBorder === "sec-aa")) || (colorArray.length > 1 && cardType !== "leader" && cardType !== "character" && cardType !== "event") || (cardType === "event" && eventBorder !== "op-10") || (colorArray.length > 1 && cardType === "character" && characterBorder !== "none" && characterBorder !== "sp-v2") ? "text-outline-xs" : "text-outline-standard"} relative inline-block max-w-[87.695%] overflow-hidden align-middle tracking-[0.01em] ${characterBorder === "none" || characterBorder === "sp-v2" ? "text-neutral-50" : ""} ${textColor === "brightness-0" ? "font-medium brightness-0" : "font-medium"} font-mukta ${
          leaderBorder === "AA-white" && cardType === "leader"
            ? "text-neutral-950 [text-shadow:-0.0150em_-0.0150em_0_#fff,0.0150em_-0.0150em_0_#fff,-0.0150em_0.0150em_0_#fff,0.0150em_0.0150em_0_#fff]"
            : ""
        }`}
      >
        {name}
      </p>
    </div>
  );
}
