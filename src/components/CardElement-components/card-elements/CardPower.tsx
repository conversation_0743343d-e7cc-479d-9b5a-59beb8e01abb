"use client";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import { useGetResponsiveFontsizeInPx } from "@/hooks/useGetResponsiveFontsizeInPx";
import { CharacterBorder, LeaderBorder } from "@/types";
import { getCardColor } from "@/app/helpers/getCardColor";
import { getTextColor } from "@/app/helpers/getTextColor";
import {
  getPrintReadyDistanceNumber,
  getPrintReadyDistanceString,
} from "@/app/helpers/getPrintReadyDistance";
import { getPrintReadySizeNumber } from "@/app/helpers/getPrintReadySize";

export default function CardPower({
  cardType,
}: {
  cardType: "character" | "leader";
}) {
  const printReady = useGetStoreState("printReady");
  const characterBorder = useGetStoreState(
    "characterBorder",
  ) as CharacterBorder;
  const colorArray = useGetStoreState("colorArray");
  const color = colorArray[0];
  const color2 = colorArray[colorArray.length - 1];
  const shadowColor = getCardColor(
    color,
    cardType === "character" && characterBorder === "none",
    false,
    cardType === "character" && characterBorder === "sp-v2",
    true,
  );
  const leaderShadowColor = getCardColor(color2, false, false, false, true);
  const power = useGetStoreState("power");
  const powerBlack = useGetStoreState("powerBlack");
  const leaderBorder = useGetStoreState("leaderBorder") as LeaderBorder;
  const mr1 = useGetResponsiveFontsizeInPx({ desiredTextSizeInPx: 7.6 });
  const mr2 = useGetResponsiveFontsizeInPx({ desiredTextSizeInPx: 12.6 });

  const fontsize = useGetResponsiveFontsizeInPx({
    desiredTextSizeInPx:
      parseInt(power) >= 10000
        ? getPrintReadySizeNumber(50.3, printReady)
        : getPrintReadySizeNumber(51.28, printReady),
  });
  const tracking = useGetResponsiveFontsizeInPx({
    desiredTextSizeInPx:
      parseInt(power) >= 10000
        ? getPrintReadySizeNumber(-1.4, printReady)
        : power === "1000" || power === "4000"
          ? getPrintReadySizeNumber(-2.8, printReady)
          : cardType === "leader"
            ? getPrintReadySizeNumber(-2.2, printReady)
            : getPrintReadySizeNumber(-2.5, printReady),
  });

  let right = getPrintReadyDistanceString("13.5%", "left-or-right", printReady);
  let top = getPrintReadyDistanceString("0.5637%", "top-or-bottom", printReady);
  const fontWeight = "400";
  if (cardType === "leader") {
    top = getPrintReadyDistanceString("0.6517%", "top-or-bottom", printReady);
    // fontWeight = "900";
  }
  if (parseInt(power) >= 10000) {
    // fontWeight = "900";
    top = getPrintReadyDistanceString("0.5937%", "top-or-bottom", printReady);

    right = getPrintReadyDistanceString("13.1%", "left-or-right", printReady);
  } else if (parseInt(power) === 0) {
    right = getPrintReadyDistanceString("20.35%", "left-or-right", printReady);
  }
  const textColor = getTextColor(
    color,
    characterBorder,
    cardType,
    leaderBorder,
    "power",
    colorArray,
  );
  const textColorLeader = getTextColor(
    color2,
    characterBorder,
    cardType,
    leaderBorder,
    "power",
    colorArray,
  );

  let backgroundImage = "";
  if (
    (cardType === "character" && characterBorder === "none") ||
    characterBorder === "sp-v2"
  ) {
    backgroundImage = `linear-gradient(to right, ${shadowColor},  ${shadowColor})`;
  }
  if (
    cardType === "character" &&
    (characterBorder === "sec" ||
      characterBorder === "rare" ||
      characterBorder === "sec-aa" ||
      characterBorder === "mr")
  ) {
    backgroundImage = `linear-gradient(to right, #000,  #000)`;
  }
  if (cardType === "leader") {
    backgroundImage = `linear-gradient(to right, ${leaderShadowColor},  ${leaderShadowColor})`;
    if (leaderBorder === "standard-white" || leaderBorder === "AA-black") {
      backgroundImage = `linear-gradient(to right, #fff,  #fff)`;
    }

    if (
      leaderBorder === "25th" ||
      leaderBorder === "AA-white" ||
      leaderBorder === "AA-black-and-white" ||
      leaderBorder === "rainbow" ||
      leaderBorder === "full-art"
    ) {
      backgroundImage = `linear-gradient(to right, #212121,  #212121)`;
    }
  }
  if (powerBlack && cardType === "leader") {
    if (textColorLeader === "#fff") {
      backgroundImage = `linear-gradient(to right, #212121,  #212121)`;
    } else {
      backgroundImage = `linear-gradient(to right, #fff,  #fff)`;
    }
  }

  return (
    <div
      className={`absolute`}
      style={{
        right: `${right}`,
        top: `${top}`,
        zIndex: 4,
      }}
    >
      <p
        style={{
          fontSize: `${fontsize}px`,
          letterSpacing: `${tracking}px`,
          fontWeight: fontWeight,
          backgroundImage: backgroundImage,
          color: cardType === "leader" ? textColorLeader : textColor,
        }}
        className={`text-outline text-outline-xs relative inline-block overflow-hidden ${textColor} align-middle ${cardType === "leader" && parseInt(power) < 10000 ? "font-leader-power" : parseInt(power) >= 10000 ? "font-character-power-10k" : "font-character-power"} `}
      >
        {power === "11000" ? (
          <span
            style={{
              marginRight: `${getPrintReadyDistanceNumber(printReady ? Number(mr2) - 4.5 : mr2, "left-or-right", printReady)}px`,
            }}
            className={`inline-block`}
          >
            1
          </span>
        ) : null}
        {parseInt(power) >= 10000 ? (
          <span
            style={{
              marginRight: `${getPrintReadyDistanceNumber(printReady ? Number(mr1) - 4.7 : Number(mr1), "left-or-right", printReady)}px`,
            }}
            className={`inline-block`}
          >
            1
          </span>
        ) : null}
        {power.includes("1")
          ? power === "11000"
            ? power.slice(2)
            : `${parseInt(power) < 10000 ? "1 " : ""}` + power.slice(1)
          : power}
      </p>
    </div>
  );
}
