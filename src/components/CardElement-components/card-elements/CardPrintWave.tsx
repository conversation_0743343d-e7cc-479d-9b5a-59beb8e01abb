"use client";

import Image from "next/image";

import { useGetStoreState } from "@/helpers/useGetStoreState";

import { getCardColor } from "@/app/helpers/getCardColor";
import { getPrintReadyDistanceString } from "@/app/helpers/getPrintReadyDistance";
import { getPrintReadySizeString } from "@/app/helpers/getPrintReadySize";

export default function CardPrintWave({
  quality,
  cardType = "standard",
  cardKindRoute,
}: {
  quality: number;
  cardType?: "standard" | "leader" | "character";
  cardKindRoute: "leader" | "character" | "event" | "stage" | "don";
}) {
  const printReady = useGetStoreState("printReady");
  const leaderBorder = useGetStoreState("leaderBorder");
  const eventBorder = useGetStoreState("eventBorder");
  const colorArray = useGetStoreState("colorArray");
  const color = colorArray[0];
  const shadowColor = getCardColor(color);
  const shadowColorFinal = color === "yellow" ? "#000" : shadowColor;
  const characterBorder = useGetStoreState("characterBorder");
  let filter = "";
  if (
    (cardKindRoute === "character" ||
      (cardKindRoute === "event" && eventBorder !== "eb-02") ||
      cardKindRoute === "stage") &&
    colorArray.length > 1
  ) {
    filter = `drop-shadow(-0.025em -0.04em #000) drop-shadow(-0.025em 0.05em #000) drop-shadow(0.04em -0.00em #000) drop-shadow(0.025em 0.0em #000) `;
  }
  if (cardType === "character" && characterBorder === "none") {
    filter = `drop-shadow(-0.05em -0.05em ${shadowColorFinal}) drop-shadow(-0.05em 0.05em ${shadowColorFinal}) drop-shadow(-0.03em 0.03em ${shadowColorFinal}) drop-shadow(0.07em -0.07em ${shadowColorFinal}) drop-shadow(0.1em 0.1em ${shadowColorFinal})`;
  }
  if (cardType === "character" && characterBorder === "sp-v2") {
    filter = `drop-shadow(-0.05em -0.08em #000) drop-shadow(-0.05em 0.1em #000) drop-shadow(0.08em -0.00em #000) drop-shadow(0.05em 0.0em #000) `;
  }
  if (
    cardType === "character" &&
    ((characterBorder === "sec" && colorArray.length === 1) ||
      (characterBorder === "mr" && colorArray.length === 1) ||
      (characterBorder === "sec-aa" && colorArray.length === 1))
  ) {
    if (
      color === "yellow" &&
      colorArray.length === 1 &&
      characterBorder === "mr"
    ) {
      filter = `drop-shadow(-0.025em -0.04em ${shadowColorFinal}) drop-shadow(-0.025em 0.05em ${shadowColorFinal}) drop-shadow(0.04em -0.00em ${shadowColorFinal}) drop-shadow(0.025em 0.0em ${shadowColorFinal}) `;
    } else {
      filter = `drop-shadow(-0.025em -0.04em ${shadowColorFinal}) drop-shadow(-0.025em 0.05em ${shadowColorFinal}) drop-shadow(0.04em -0.00em ${shadowColorFinal}) drop-shadow(0.025em 0.0em ${shadowColorFinal}) `;
    }
  }
  if (cardType === "leader" && leaderBorder === "AA-white") {
    filter = `drop-shadow(-0.025em -0.04em #fff) drop-shadow(-0.025em 0.05em #fff) drop-shadow(0.04em -0.00em #fff) drop-shadow(0.025em 0.0em #fff) `;
  }
  if (cardType === "leader" && leaderBorder === "full-art") {
    filter = `drop-shadow(-0.05em -0.08em #fff) drop-shadow(-0.05em 0.1em #fff) drop-shadow(0.08em -0.00em #fff) drop-shadow(0.05em 0.0em #fff) `;
  }
  if (cardKindRoute === "event" && eventBorder === "op-10") {
    filter = `drop-shadow(-0.05em -0.08em #000) drop-shadow(-0.05em 0.1em #000) drop-shadow(0.08em -0.00em #000) drop-shadow(0.05em 0.0em #000) `;
  }
  return (
    <Image
      width={114}
      height={142}
      priority
      loading={"eager"}
      title={`Card print wave image`}
      className={`absolute w-auto ${
        (cardType === "leader" || cardKindRoute === "leader") &&
        leaderBorder === "AA-white"
          ? "brightness-0"
          : ""
      } ${
        cardType === "standard"
          ? color.toLocaleLowerCase().includes("yellow") &&
            colorArray.length === 1
            ? "brightness-0"
            : ""
          : ""
      } ${
        cardType === "character" &&
        characterBorder !== "none" &&
        characterBorder !== "sec" &&
        characterBorder !== "sec-aa" &&
        characterBorder !== "mr"
          ? color.toLocaleLowerCase().includes("yellow") &&
            colorArray.length === 1
            ? "brightness-0"
            : ""
          : ""
      } `}
      style={{
        filter: filter,
        zIndex: 4,
        bottom: getPrintReadyDistanceString(
          "3.777%",
          "top-or-bottom",
          printReady,
        ),
        right: getPrintReadyDistanceString(
          "5.945%",
          "left-or-right",
          printReady,
        ),
        height: printReady
          ? getPrintReadySizeString("2.93%", printReady)
          : "2.85%",
      }}
      src={`${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/printWave${(cardType === "character" && (characterBorder === "sec" || characterBorder === "sec-aa" || characterBorder === "mr")) || (cardType === "character" && color === "yellow" && characterBorder === "none") ? "YellowSP" : ""}${(cardType === "leader" && (leaderBorder === "AA-white" || leaderBorder === "full-art")) || (cardKindRoute === "event" && eventBorder === "eb-02") ? "Black" : ""}.png`}
      alt={`Card print wave image`}
      quality={quality}
    />
  );
}
