"use client";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import { useGetResponsiveFontsizeInPx } from "@/hooks/useGetResponsiveFontsizeInPx";

import { getCardColor } from "@/app/helpers/getCardColor";
import {
  LINUX_PRINT_WAVE_STYLES_OBJ,
  WINDOWS_PRINT_WAVE_STYLES_OBJ,
} from "@/app/constants";
import {
  getPrintReadySizeNumber,
  getPrintReadySizeString,
} from "@/app/helpers/getPrintReadySize";
import { getPrintReadyDistanceString } from "@/app/helpers/getPrintReadyDistance";

export default function CardPrintWaveText({
  cardType,
  client = false,
}: {
  cardType: "character" | "leader" | "event" | "stage" | "don";
  client?: boolean;
}) {
  const printReady = useGetStoreState("printReady");
  let textColor;
  const characterBorder = useGetStoreState("characterBorder");
  const colorArray = useGetStoreState("colorArray");
  const color = colorArray[0];
  const shadowColor = getCardColor(
    color,
    false,
    false,
    characterBorder === "sp-v2",
  );
  const shadowColorFinal = color === "yellow" ? "#000" : shadowColor;
  const leaderBorder = useGetStoreState("leaderBorder");
  const eventBorder = useGetStoreState("eventBorder");
  const printWave = useGetStoreState("printWave");
  const fontsize = useGetResponsiveFontsizeInPx({ desiredTextSizeInPx: 16 });
  if (
    cardType === "character" ||
    (cardType === "event" && eventBorder !== "eb-02") ||
    cardType === "stage"
  ) {
    if (cardType === "character") {
      textColor =
        color === "yellow" &&
        colorArray.length === 1 &&
        characterBorder !== "sp-v2"
          ? "text-white"
          : "brightness-0";
    } else {
      textColor =
        color === "yellow" && colorArray.length === 1 ? "text-white" : "";
    }
  }
  if (cardType === "leader") {
    if (leaderBorder === "AA-white" || leaderBorder === "full-art") {
      textColor = "text-white";
    } else {
      textColor = "brightness-0";
    }
  }
  if (cardType === "event" && eventBorder === "eb-02") {
    textColor = "text-white";
  }
  if (cardType === "event" && eventBorder === "op-10") {
    textColor = "text-black";
  }
  return (
    <div
      style={{
        fontSize: `${getPrintReadySizeNumber(fontsize, printReady)}px`,
        zIndex: 4,
        width: getPrintReadySizeString("2.81%", printReady),
        bottom:
          client || process.env.NEXT_PUBLIC_WINDOWS
            ? getPrintReadyDistanceString(
                WINDOWS_PRINT_WAVE_STYLES_OBJ.bottom,
                "top-or-bottom",
                printReady,
              )
            : getPrintReadyDistanceString(
                LINUX_PRINT_WAVE_STYLES_OBJ.bottom,
                "top-or-bottom",
                printReady,
              ),
        right:
          client || process.env.NEXT_PUBLIC_WINDOWS
            ? getPrintReadyDistanceString(
                WINDOWS_PRINT_WAVE_STYLES_OBJ.right,
                "left-or-right",
                printReady,
              )
            : getPrintReadyDistanceString(
                LINUX_PRINT_WAVE_STYLES_OBJ.right,
                "left-or-right",
                printReady,
              ),
      }}
      className={`absolute w-[2.81%] text-center font-extrabold text-black ${cardType === "character" && characterBorder === "none" ? "" : textColor}`}
    >
      <span
        className={"text-center"}
        style={{
          color:
            cardType === "character" && characterBorder === "none"
              ? shadowColorFinal
              : cardType === "character" &&
                  (characterBorder === "sec" ||
                    characterBorder === "sec-aa" ||
                    characterBorder === "mr")
                ? "#000"
                : "",
        }}
      >
        {printWave}
      </span>
    </div>
  );
}
