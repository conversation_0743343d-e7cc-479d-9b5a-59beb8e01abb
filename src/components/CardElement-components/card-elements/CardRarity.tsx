"use client";

import Image from "next/image";

import { useGetStoreState } from "@/helpers/useGetStoreState";

import { getCardColor } from "@/app/helpers/getCardColor";
import { getPrintReadyDistanceString } from "@/app/helpers/getPrintReadyDistance";
import { getPrintReadySizeString } from "@/app/helpers/getPrintReadySize";

export default function CardRarity({
  quality,
  cardType = "standard",
  cardKindRoute,
}: {
  quality: number;
  cardType?: "leader" | "standard" | "character";
  cardKindRoute: "leader" | "character" | "event" | "stage" | "don";
}) {
  const printReady = useGetStoreState("printReady");
  const characterBorder = useGetStoreState("characterBorder");
  const leaderBorder = useGetStoreState("leaderBorder");
  const eventBorder = useGetStoreState("eventBorder");

  const colorArray = useGetStoreState("colorArray");
  const color = colorArray[0];
  const shadowColor = getCardColor(color);
  const shadowColorFinal = color === "yellow" ? "#000" : shadowColor;
  let filter = "";
  if (
    (cardKindRoute === "character" ||
      (cardKindRoute === "event" && eventBorder !== "eb-02") ||
      cardKindRoute === "stage") &&
    colorArray.length > 1
  ) {
    filter = `drop-shadow(-0.025em -0.04em #000) drop-shadow(-0.025em 0.05em #000) drop-shadow(0.04em -0.00em #000) drop-shadow(0.025em 0.0em #000) `;
  }
  if (cardType === "character" && characterBorder === "none") {
    filter = `drop-shadow(-0.05em -0.08em ${shadowColorFinal}) drop-shadow(-0.05em 0.1em ${shadowColorFinal}) drop-shadow(0.08em -0.00em ${shadowColorFinal}) drop-shadow(0.05em 0.0em ${shadowColorFinal}) `;
  }
  if (cardType === "character" && characterBorder === "sp-v2") {
    filter = `drop-shadow(-0.05em -0.08em #000) drop-shadow(-0.05em 0.1em #000) drop-shadow(0.08em -0.00em #000) drop-shadow(0.05em 0.0em #000) `;
  }
  if (cardType === "leader" && leaderBorder === "full-art") {
    filter = `drop-shadow(-0.05em -0.08em #fff) drop-shadow(-0.05em 0.1em #fff) drop-shadow(0.08em -0.00em #fff) drop-shadow(0.05em 0.0em #fff) `;
  }
  if (
    cardType === "character" &&
    ((characterBorder === "sec" && colorArray.length === 1) ||
      (characterBorder === "mr" && colorArray.length === 1) ||
      (characterBorder === "sec-aa" && colorArray.length === 1))
  ) {
    if (color === "yellow" && characterBorder === "mr") {
      filter = `drop-shadow(-0.025em -0.04em ${shadowColorFinal}) drop-shadow(-0.025em 0.05em ${shadowColorFinal}) drop-shadow(0.04em -0.00em ${shadowColorFinal}) drop-shadow(0.025em 0.0em ${shadowColorFinal}) `;
    } else {
      filter = `drop-shadow(-0.025em -0.04em ${shadowColorFinal}) drop-shadow(-0.025em 0.05em ${shadowColorFinal}) drop-shadow(0.04em -0.00em ${shadowColorFinal}) drop-shadow(0.025em 0.0em ${shadowColorFinal}) `;
    }
  }
  if (cardType === "leader" && leaderBorder === "AA-white") {
    filter = `drop-shadow(-0.025em -0.04em #fff) drop-shadow(-0.025em 0.05em #fff) drop-shadow(0.04em -0.00em #fff) drop-shadow(0.025em 0.0em #fff) `;
  }

  if (cardKindRoute === "event" && eventBorder === "op-10") {
    filter = `drop-shadow(-0.05em -0.08em #000) drop-shadow(-0.05em 0.1em #000) drop-shadow(0.08em -0.00em #000) drop-shadow(0.05em 0.0em #000) `;
  }
  return (
    <Image
      width={89}
      height={57}
      priority
      loading={"eager"}
      title={`Card Rarity image`}
      className={`absolute w-auto ${
        (cardType === "leader" || cardKindRoute === "leader") &&
        (leaderBorder === "AA-white" || leaderBorder === "full-art")
          ? "brightness-0"
          : ""
      } ${
        cardType === "standard"
          ? color.toLocaleLowerCase().includes("yellow") &&
            colorArray.length === 1
            ? "brightness-0"
            : ""
          : ""
      } ${
        cardType === "character" && characterBorder !== "sec"
          ? characterBorder === "none"
            ? ""
            : color.toLocaleLowerCase().includes("yellow") &&
                colorArray.length === 1
              ? "brightness-0"
              : ""
          : ""
      } `}
      style={{
        filter: filter,
        zIndex: 4,
        background: "",
        bottom: getPrintReadyDistanceString(
          "4.095%",
          "top-or-bottom",
          printReady,
        ),
        right: getPrintReadyDistanceString("9.7%", "left-or-right", printReady),
        height: printReady
          ? getPrintReadySizeString("1.322%", printReady)
          : "1.282%",
      }}
      src={`${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/rarity${(cardType === "character" && (characterBorder === "sec" || characterBorder === "mr" || characterBorder === "sec-aa")) || (cardType === "character" && color.toLocaleLowerCase().includes("yellow") && characterBorder === "none") ? "YellowSP" : ""}${(cardType === "leader" && (leaderBorder === "AA-white" || leaderBorder === "full-art")) || (cardKindRoute === "event" && eventBorder === "eb-02") ? "Black" : ""}.png`}
      alt={`Card Rarity image`}
      quality={quality}
    />
  );
}
