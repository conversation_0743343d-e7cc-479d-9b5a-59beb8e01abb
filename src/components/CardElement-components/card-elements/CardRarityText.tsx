"use client";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import { useGetResponsiveFontsizeInPx } from "@/hooks/useGetResponsiveFontsizeInPx";

import { getCardColor } from "@/app/helpers/getCardColor";
import {
  CF_RARITY_STAR_STYLES_CB_ELSE_OBJ,
  CF_RARITY_STAR_STYLES_CB_NONE_OBJ,
  LINUX_RARITY_STAR_STYLES_CB_ELSE_OBJ,
  LINUX_RARITY_STAR_STYLES_CB_NONE_OBJ,
  WINDOWS_RARITY_STAR_STYLES_CB_ELSE_OBJ,
  WINDOWS_RARITY_STAR_STYLES_CB_NONE_OBJ,
} from "@/app/constants";
import {
  getPrintReadySizeNumber,
  getPrintReadySizeString,
} from "@/app/helpers/getPrintReadySize";
import { getPrintReadyDistanceString } from "@/app/helpers/getPrintReadyDistance";

export default function CardRarityText({
  cardType,
  client = false,
  softwareAcceleration,
  cfWorker,
}: {
  cardType: "character" | "leader" | "event" | "stage" | "don";
  client?: boolean;
  softwareAcceleration?: boolean;
  cfWorker?: boolean;
}) {
  const printReady = useGetStoreState("printReady");
  let textColor;
  let fontSize = 0.81;

  const characterBorder = useGetStoreState("characterBorder");
  const colorArray = useGetStoreState("colorArray");
  const color = colorArray[0];
  const leaderBorder = useGetStoreState("leaderBorder");
  const eventBorder = useGetStoreState("eventBorder");
  const aaStar = useGetStoreState("aaStar") as boolean;
  const rarity = useGetStoreState("rarity");
  const fontsize = useGetResponsiveFontsizeInPx({ desiredTextSizeInPx: 14 });
  const shadowColor = getCardColor(color);
  const shadowColorFinal = color === "yellow" ? "#000" : shadowColor;
  let rarityRight;
  let rarityBottom;
  let fontFamily = "serif";
  if (client || process.env.NEXT_PUBLIC_WINDOWS) {
    rarityRight = WINDOWS_RARITY_STAR_STYLES_CB_ELSE_OBJ.right;
    fontSize = 0.73;
  } else if (
    cardType === "character" &&
    (characterBorder === "sec" ||
      characterBorder === "sec-aa" ||
      characterBorder === "mr")
  ) {
    if (softwareAcceleration) {
      if (color === "yellow") {
        rarityRight = LINUX_RARITY_STAR_STYLES_CB_NONE_OBJ.right;
      } else {
        rarityRight = LINUX_RARITY_STAR_STYLES_CB_ELSE_OBJ.right;
      }
    } else if (cfWorker) {
      if (color === "yellow") {
        rarityRight = CF_RARITY_STAR_STYLES_CB_NONE_OBJ.right;
      } else {
        rarityRight = CF_RARITY_STAR_STYLES_CB_ELSE_OBJ.right;
      }
    }
  } else if (softwareAcceleration) {
    rarityRight = LINUX_RARITY_STAR_STYLES_CB_ELSE_OBJ.right;
  } else if (cfWorker) {
    rarityRight = CF_RARITY_STAR_STYLES_CB_ELSE_OBJ.right;
  }

  if (
    (cardType === "character" &&
      (characterBorder === "sec" ||
        characterBorder === "sec-aa" ||
        characterBorder === "mr") &&
      color.includes("yellow")) ||
    (cardType === "character" &&
      (characterBorder === "sp-v2" || characterBorder === "none")) ||
    (cardType === "event" && eventBorder === "op-10") ||
    ((cardType === "character" ||
      (cardType === "event" && eventBorder !== "eb-02") ||
      cardType === "stage") &&
      colorArray.length > 1) ||
    (cardType === "leader" && leaderBorder === "full-art")
  ) {
    if (client || process.env.NEXT_PUBLIC_WINDOWS) {
      rarityBottom = WINDOWS_RARITY_STAR_STYLES_CB_NONE_OBJ.bottom;
    } else if (softwareAcceleration) {
      fontSize = 0.91;
      rarityBottom = LINUX_RARITY_STAR_STYLES_CB_NONE_OBJ.bottom;
      rarityRight = LINUX_RARITY_STAR_STYLES_CB_NONE_OBJ.right;
    } else if (cfWorker) {
      rarityBottom = CF_RARITY_STAR_STYLES_CB_NONE_OBJ.bottom;
    } else {
      rarityBottom = "5.23%";
    }
  } else {
    if (client || process.env.NEXT_PUBLIC_WINDOWS) {
      rarityBottom = WINDOWS_RARITY_STAR_STYLES_CB_ELSE_OBJ.bottom;
    } else if (softwareAcceleration) {
      fontSize = 0.91;
      rarityBottom = LINUX_RARITY_STAR_STYLES_CB_ELSE_OBJ.bottom;
      rarityRight = LINUX_RARITY_STAR_STYLES_CB_ELSE_OBJ.right;
    } else if (cfWorker) {
      rarityBottom = CF_RARITY_STAR_STYLES_CB_ELSE_OBJ.bottom;
    } else {
      rarityBottom = "4.83%";
    }
  }
  // This is for linux Hardware Accelerated styles
  if (!client && !softwareAcceleration && !cfWorker) {
    fontSize = 0.91;
    fontFamily = "math";
  }

  if (
    cardType === "character" ||
    (cardType === "event" && eventBorder !== "eb-02") ||
    cardType === "stage"
  ) {
    textColor =
      color === "yellow" && colorArray.length === 1
        ? "text-white"
        : "brightness-0";
    if (
      cardType === "character" &&
      (characterBorder === "sec" ||
        characterBorder === "sec-aa" ||
        characterBorder === "mr")
    ) {
      textColor = "brightness-0";
    }
  }
  if (cardType === "leader") {
    if (leaderBorder === "AA-white" || leaderBorder === "full-art") {
      textColor = "text-white";
    } else {
      textColor = "brightness-0";
    }
  }
  if (cardType === "event" && eventBorder === "eb-02") {
    textColor = "text-white";
  }
  if (cardType === "event" && eventBorder === "op-10") {
    textColor = "text-black";
  }

  let backgroundImage = "";
  if (
    (cardType === "character" ||
      (cardType === "event" && eventBorder !== "eb-02") ||
      cardType === "stage") &&
    colorArray.length > 1
  ) {
    backgroundImage = `linear-gradient(to right, #000,  #000)`;
  }
  if (
    cardType === "character" &&
    (characterBorder === "sec" ||
      characterBorder === "sec-aa" ||
      characterBorder === "mr") &&
    color === "yellow"
  ) {
    backgroundImage = `linear-gradient(to right, #000,  #000)`;
  }
  if (
    cardType === "character" &&
    (characterBorder === "sp-v2" || characterBorder === "none")
  ) {
    backgroundImage = `linear-gradient(to right, #000,  #000)`;
  }
  if (cardType === "leader" && leaderBorder === "full-art") {
    backgroundImage = `linear-gradient(to right, #fff,  #fff)`;
  }
  if (cardType === "event" && eventBorder === "op-10") {
    backgroundImage = `linear-gradient(to right, #000,  #000)`;
  }
  // const spNoneStyle = process.env?.LINUX_RARITY_STAR_STYLES_CB_NONE && process.env?.LINUX_RARITY_STAR_STYLES_CB_NONE?.length > 0 ? ""  :

  return (
    <>
      {aaStar && (
        <p
          className={`text-outline text-outline-xs absolute right-[9.52%] w-[2.81%]`}
          style={{
            fontFamily: fontFamily,
            color:
              cardType === "character" &&
              (characterBorder === "sec" ||
                characterBorder === "sec-aa" ||
                characterBorder === "mr")
                ? "#D5AC08"
                : (cardType === "character" &&
                      color === "yellow" &&
                      colorArray.length === 1 &&
                      characterBorder !== "sp-v2" &&
                      characterBorder !== "none") ||
                    (cardType === "leader" &&
                      (leaderBorder === "AA-white" ||
                        leaderBorder === "full-art")) ||
                    (((cardType === "event" && eventBorder !== "op-10") ||
                      cardType === "stage") &&
                      color === "yellow" &&
                      colorArray.length === 1) ||
                    (cardType === "event" && eventBorder === "eb-02")
                  ? "#000"
                  : "",
            zIndex: 4,
            backgroundImage: backgroundImage,
            fontSize: `${getPrintReadySizeNumber(fontSize, printReady)}em`,

            right: getPrintReadyDistanceString(
              rarityRight,
              "left-or-right",
              printReady,
            ),
            width: getPrintReadySizeString("2.81%", printReady),
            bottom: getPrintReadyDistanceString(
              rarityBottom,
              "top-or-bottom",
              printReady,
            ),
          }}
        >
          ★
        </p>
      )}
      <div
        style={{
          fontSize: `${getPrintReadySizeNumber(fontsize, printReady)}px`,
          zIndex: 4,
          bottom: getPrintReadyDistanceString(
            "3.565%",
            "top-or-bottom",
            printReady,
          ),
          right: getPrintReadyDistanceString(
            "9.695%",
            "left-or-right",
            printReady,
          ),
          width: printReady
            ? getPrintReadySizeString("2.81%", printReady)
            : "2.81%",
        }}
        className={`font-mohave absolute right-[9.695%] bottom-[3.555%] w-[2.81%] text-center font-bold ${cardType === "character" && (characterBorder === "none" || characterBorder === "sec-aa" || characterBorder === "sec") ? "text-black" : textColor}`}
      >
        <span
          className={"text-center"}
          style={{
            color:
              cardType === "character" &&
              (characterBorder === "none" || characterBorder === "sp-v2")
                ? shadowColorFinal
                : "",
          }}
        >
          {rarity}
        </span>
      </div>
    </>
  );
}
