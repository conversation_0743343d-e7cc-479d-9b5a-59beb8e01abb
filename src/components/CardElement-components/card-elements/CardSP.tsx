"use client";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import { CharacterBorder } from "@/types";
import Image from "next/image";
import { useGetResponsiveFontsizeInPx } from "@/hooks/useGetResponsiveFontsizeInPx";
import { getCardColor } from "@/app/helpers/getCardColor";
import { getTextColor } from "@/app/helpers/getTextColor";
import { getPrintReadySizeNumber } from "@/app/helpers/getPrintReadySize";
import { getPrintReadyDistanceString } from "@/app/helpers/getPrintReadyDistance";

export default function CardSP({
  quality,
  cardType = "character",
}: {
  quality: number;
  cardType: "character" | "leader" | "event" | "stage" | "don";
}) {
  const printReady = useGetStoreState("printReady");
  const characterBorder = useGetStoreState(
    "characterBorder",
  ) as CharacterBorder;
  const colorArray = useGetStoreState("colorArray");
  const color = colorArray[0];
  const SPTop = useGetStoreState("SPTop");
  const fontsize = useGetResponsiveFontsizeInPx({ desiredTextSizeInPx: 12 });
  const height = useGetResponsiveFontsizeInPx({ desiredTextSizeInPx: 19.5 });
  const width = useGetResponsiveFontsizeInPx({ desiredTextSizeInPx: 26.5 });
  const leaderBorder = useGetStoreState("leaderBorder");
  const textColor = getTextColor(
    color,
    characterBorder,
    cardType,
    leaderBorder,
    "card_number_and_set",
    colorArray,
  );
  const shadowColor = getCardColor(color);
  const shadowColorFinal = color === "yellow" ? "#000" : shadowColor;
  const rarity = useGetStoreState("rarity2");
  let filter = "";
  if (cardType !== "leader" && cardType !== "don" && colorArray.length > 1) {
    filter = `drop-shadow(-0.025em -0.04em #000) drop-shadow(-0.025em 0.05em #000) drop-shadow(0.04em -0.00em #000) drop-shadow(0.025em 0.0em #000) `;
  }
  if (cardType === "character" && characterBorder === "none") {
    filter = `drop-shadow(-0.05em -0.08em ${shadowColorFinal}) drop-shadow(-0.05em 0.1em ${shadowColorFinal}) drop-shadow(0.08em -0.00em ${shadowColorFinal}) drop-shadow(0.05em 0.0em ${shadowColorFinal}) `;
  }
  if (cardType === "character" && characterBorder === "sp-v2") {
    filter = `drop-shadow(-0.05em -0.08em #000) drop-shadow(-0.05em 0.1em #000) drop-shadow(0.08em -0.00em #000) drop-shadow(0.05em 0.0em #000) `;
  }
  if (
    cardType === "character" &&
    (characterBorder === "sec" || characterBorder === "sec-aa") &&
    colorArray.length === 1
  ) {
    filter = `drop-shadow(-0.025em -0.04em ${shadowColorFinal}) drop-shadow(-0.025em 0.05em ${shadowColorFinal}) drop-shadow(0.04em -0.00em ${shadowColorFinal}) drop-shadow(0.025em 0.0em ${shadowColorFinal}) `;
  }
  if (
    cardType === "character" &&
    characterBorder === "mr" &&
    color === "yellow"
  ) {
    filter = `drop-shadow(-0.025em -0.04em ${shadowColorFinal}) drop-shadow(-0.025em 0.05em ${shadowColorFinal}) drop-shadow(0.04em -0.00em ${shadowColorFinal}) drop-shadow(0.025em 0.0em ${shadowColorFinal}) `;
  }
  if (cardType === "character" && rarity.length > 0)
    return (
      <div
        style={{
          height: `${getPrintReadySizeNumber(height, printReady)}px`,
          width: `${getPrintReadySizeNumber(width, printReady)}px`,
          zIndex: 4,
          left: SPTop
            ? printReady
              ? `${getPrintReadySizeNumber(1.84, printReady)}em`
              : "1.6em"
            : printReady
              ? "0.06em"
              : "",
          top: SPTop
            ? printReady
              ? `${getPrintReadySizeNumber(-0.96, printReady)}em`
              : "-0.9em"
            : printReady
              ? "0.025em"
              : "",
        }}
        className={`relative`}
      >
        <Image
          width={89}
          height={57}
          priority
          loading={"eager"}
          title={`Card Rarity image`}
          className={`absolute h-[58.252%] w-auto ${textColor === "brightness-0" ? "brightness-0" : ""} `}
          src={`${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/rarity${(cardType === "character" && (characterBorder === "sec" || characterBorder === "mr" || characterBorder === "sec-aa")) || (cardType === "character" && color.toLocaleLowerCase().includes("yellow") && characterBorder === "none") ? "YellowSP" : ""}.png`}
          alt={`Card Rarity image`}
          quality={quality}
          style={{
            filter: filter,
            bottom: getPrintReadyDistanceString(
              "16.5%",
              "top-or-bottom",
              printReady,
            ),
            right: getPrintReadyDistanceString(
              "8.5%",
              "left-or-right",
              printReady,
            ),
          }}
        />
        <div
          style={{
            fontSize: `${getPrintReadySizeNumber(fontsize, printReady)}px`,
            color:
              cardType === "character" && characterBorder === "none"
                ? shadowColorFinal
                : cardType === "character" &&
                    (characterBorder === "sec" ||
                      characterBorder === "mr" ||
                      characterBorder === "sec-aa" ||
                      characterBorder === "sp-v2")
                  ? "#000"
                  : color.toLocaleLowerCase().includes("yellow")
                    ? ""
                    : "#000",
            bottom: printReady
              ? getPrintReadyDistanceString(
                  "-0.85%",
                  "top-or-bottom",
                  printReady,
                )
              : "0.0%",
            right: printReady
              ? getPrintReadyDistanceString(
                  "8.45%",
                  "left-or-right",
                  printReady,
                )
              : "8.85%",
          }}
          className="font-one-piece-rarity absolute w-[66.95%] text-center font-extrabold"
        >
          <span className={"text-center"}>{rarity}</span>
        </div>
      </div>
    );
  return null;
}
