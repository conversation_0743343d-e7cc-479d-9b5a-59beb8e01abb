"use client";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import { useGetResponsiveFontsizeInPx } from "@/hooks/useGetResponsiveFontsizeInPx";

import React from "react";
import { getCardColor } from "@/app/helpers/getCardColor";
import CardSP from "@/components/CardElement-components/card-elements/CardSP";
import { getTextColor } from "@/app/helpers/getTextColor";

import {
  getPrintReadySizeNumber,
  getPrintReadySizeString,
} from "@/app/helpers/getPrintReadySize";
import { getPrintReadyDistanceString } from "@/app/helpers/getPrintReadyDistance";

export default function CardSetAndNum({
  cardType,
}: {
  cardType: "character" | "leader" | "event" | "stage" | "don";
}) {
  const printReady = useGetStoreState("printReady");
  const characterBorder = useGetStoreState("characterBorder");
  const leaderBorder = useGetStoreState("leaderBorder");
  const eventBorder = useGetStoreState("eventBorder");
  const colorArray = useGetStoreState("colorArray");
  const color = colorArray[0];
  let backgroundImage = "";
  let textOutline = "text-outline-large";
  const shadowColor = getCardColor(
    color,
    false,
    false,
    characterBorder === "sp-v2",
  );

  const textColor = getTextColor(
    color,
    characterBorder,
    cardType,
    leaderBorder,
    "card_number_and_set",
    colorArray,
    eventBorder,
  );

  const set = useGetStoreState("set");
  const cardNum = useGetStoreState("cardNum");
  const fontsize = useGetResponsiveFontsizeInPx({
    desiredTextSizeInPx: 14.4,
  });
  const spacing = useGetResponsiveFontsizeInPx({ desiredTextSizeInPx: -0.4 });
  const spacing2 = useGetResponsiveFontsizeInPx({ desiredTextSizeInPx: -0.2 });
  if (
    (cardType === "character" ||
      (cardType === "event" && eventBorder !== "eb-02") ||
      cardType === "stage") &&
    colorArray.length > 1
  ) {
    backgroundImage = `linear-gradient(to right, #000,  #000)`;
    textOutline = "text-outline-xs";
  }
  if (
    cardType === "character" &&
    (characterBorder === "none" || characterBorder === "sp-v2")
  ) {
    textOutline = "text-outline-large";
  }
  if (
    cardType === "character" &&
    (characterBorder === "none" ||
      characterBorder === "sp-v2" ||
      (characterBorder === "sec-aa" && colorArray.length === 1) ||
      (characterBorder === "sec" && colorArray.length === 1))
  ) {
    backgroundImage = `linear-gradient(to right, ${color === "yellow" ? "#000" : shadowColor},  ${color === "yellow" ? "#000" : shadowColor})`;
    if (characterBorder !== "none" && characterBorder !== "sp-v2") {
      textOutline = "text-outline-standard";
    }
  }
  if (
    cardType === "character" &&
    characterBorder === "mr" &&
    color === "yellow"
  ) {
    backgroundImage = `linear-gradient(to right, #000,  #000)`;
    textOutline = "text-outline-standard";
  }
  if (leaderBorder === "AA-white" && cardType === "leader") {
    backgroundImage = `linear-gradient(to right, #fff,  #fff)`;
    textOutline = "text-outline-standard text-neutral-950 font-normal!";
  }
  if (leaderBorder === "full-art" && cardType === "leader") {
    backgroundImage = `linear-gradient(to right, #fff,  #fff)`;
    textOutline = "text-outline-large text-neutral-950 font-normal!";
  }
  if (eventBorder === "op-10" && cardType === "event") {
    backgroundImage = `linear-gradient(to right, #000,  #000)`;
    textOutline = "text-outline-large";
  }
  return (
    <div
      style={{
        fontSize: `${getPrintReadySizeNumber(fontsize, printReady)}px`,
        zIndex: 4,
        bottom: getPrintReadyDistanceString(
          "3.823%",
          "top-or-bottom",
          printReady,
          4,
        ),
        right: getPrintReadyDistanceString(
          "12.65%",
          "left-or-right",
          printReady,
        ),
        height: getPrintReadySizeString("2.146%", printReady),
      }}
      className={`absolute flex! w-auto text-right font-medium whitespace-nowrap`}
    >
      <div className={"flex"}>
        <CardSP quality={1} cardType={cardType} />

        <span
          style={{
            letterSpacing: `${getPrintReadySizeNumber(spacing2, printReady)}px`,
            // WebkitTextStroke:
            //   characterBorder === "none" ? `0.06em ${shadowColor}` : "",
            // WebkitTextFillColor: characterBorder === "none" ? "white" : "",
            color: `${textColor !== "brightness-0" ? textColor : ""}`,
            backgroundImage: backgroundImage,
            marginRight: `${getPrintReadySizeNumber(-0.22, printReady)}em`,
          }}
          className={`text-outline ${textOutline} font-wix ${textColor === "brightness-0" ? "brightness-0" : ""}`}
        >
          {set}
        </span>
      </div>

      <span
        className={`text-outline ${textOutline} } relative font-medium ${textColor === "brightness-0" ? "brightness-0" : ""}`}
        style={{
          color: `${textColor !== "brightness-0" ? textColor : ""}`,
          backgroundImage: backgroundImage,
          letterSpacing: `${printReady ? getPrintReadySizeNumber(-1.9, printReady) : -1}px`,
          marginRight: `${getPrintReadySizeNumber(-0.15, printReady)}em`,
        }}
      >
        -
      </span>
      <span
        style={{
          letterSpacing: `${getPrintReadySizeNumber(spacing, printReady)}px`,
          color: `${textColor !== "brightness-0" ? textColor : ""}`,
          backgroundImage: backgroundImage,
        }}
        className={`text-outline ${textOutline} font-wix ${textColor === "brightness-0" ? "brightness-0" : ""}`}
      >
        {cardNum}
      </span>
    </div>
  );
}
