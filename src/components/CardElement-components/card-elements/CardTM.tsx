// "use client";
// import { useTextBorderColor } from "@/hooks/useTextBorderColor";
// import { useGetResponsiveFontsizeInPx } from "@/hooks/useGetResponsiveFontsizeInPx";
// import { useGetStoreState } from "@/helpers/useGetStoreState";
//
// export default function CardTM({
//   cardType,
// }: {
//   cardType: "character" | "leader" | "event" | "stage" | "don";
// }) {
//   const textColor = useTextBorderColor(cardType);
//   const fontsize = useGetResponsiveFontsizeInPx({ desiredTextSizeInPx: 10.72 });
//   const leaderBorder = useGetStoreState("leaderBorder");
//   const color = useGetStoreState("color")[0];
//   const leaderBorderEnabled = useGetStoreState("leaderBorderEnabled");
//   const outline1 =
//     leaderBorder === "op3 AA" && cardType === "leader"
//       ? "text-neutral-950 [text-shadow:-0.0625em_-0.0625em_0_#fff,0.0625em_-0.0625em_0_#fff,-0.0625em_0.0625em_0_#fff,0.0625em_0.0625em_0_#fff]"
//       : "";
//   const outline2 =
//     !leaderBorderEnabled && leaderBorder !== "op3 AA" && cardType === "leader"
//       ? "text-neutral-950 [text-shadow:-0.0625em_-0.0625em_0_#fff,0.0625em_-0.0625em_0_#fff,-0.0625em_0.0625em_0_#fff,0.0625em_0.0625em_0_#fff]"
//       : "";
//   const outline3 =
//     leaderBorderEnabled &&
//     leaderBorder !== "op3 AA" &&
//     leaderBorder !== "rainbow" &&
//     cardType === "leader" &&
//     color.toLocaleLowerCase().includes("yellow")
//       ? "text-neutral-950 "
//       : "";
//   return null;
//   // return (
//   //   <div
//   //     style={{ fontSize: `${fontsize}px` }}
//   //     className={`absolute bottom-[14.846%] right-[-15%] rotate-[-90deg] font-medium ${
//   //       cardType !== "leader" ? textColor : ""
//   //     }        ${outline1} ${outline2} ${outline3}`}
//   //   >
//   //     <p className={""}>©EO/S ©E.O./S., T.A. BANDAI MADE IN JAPAN</p>
//   //   </div>
//   // );
// }
