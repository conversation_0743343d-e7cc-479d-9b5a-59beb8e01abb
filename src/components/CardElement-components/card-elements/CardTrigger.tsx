"use client";
import Image from "next/image";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import { getPrintReadySizeNumber } from "@/app/helpers/getPrintReadySize";
export default function CardTrigger({ quality }: { quality: number }) {
  const printReady = useGetStoreState("printReady");
  return (
    <div className={`font-geologica`}>
      <span
        className={"absolute z-20 block text-black"}
        style={{
          marginLeft: printReady ? "0.90%" : "0.9%",
          marginTop: printReady ? "0.32%" : "0.3%",
          fontSize: `${getPrintReadySizeNumber(1.18, printReady)}em`,
          letterSpacing: `-0.035em`,
        }}
      >
        Trigger
      </span>
      <Image
        width={2933}
        height={172}
        priority
        loading={"eager"}
        title={`Card trigger image`}
        className={"absolute w-[100%]"}
        src={`${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/trigger4.png`}
        alt={`Card trigger image`}
        quality={quality}
      />
    </div>
  );
}
