import { useGetStoreState } from "@/helpers/useGetStoreState";
import { useGetResponsiveFontsizeInPx } from "@/hooks/useGetResponsiveFontsizeInPx";

export default function CardTriggerText() {
  const triggerText = useGetStoreState("triggerText");
  const fontsize = useGetResponsiveFontsizeInPx();
  return (
    <div className={"relative left-[15.25%] w-[84.8%] text-left break-words"}>
      <p
        style={{ fontSize: `${fontsize}px` }}
        className={`relative inline-block w-[100%] overflow-hidden pt-[0.6%]! pr-[6%]! align-middle leading-[175%] font-normal tracking-wide`}
      >
        {triggerText}
      </p>
    </div>
  );
}
