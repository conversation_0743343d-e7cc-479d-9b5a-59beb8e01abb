"use client";
import { useGetStoreState } from "@/helpers/useGetStoreState";

import { useGetResponsiveFontsizeInPx } from "@/hooks/useGetResponsiveFontsizeInPx";
import { getCardColor } from "@/app/helpers/getCardColor";
import { getTextColor } from "@/app/helpers/getTextColor";
import { useEffect, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import { handeSPTopChange } from "@/app/helpers/storeUpdaterFunctions";
import { getPrintReadyDistanceString } from "@/app/helpers/getPrintReadyDistance";
import {
  getPrintReadySizeNumber,
  getPrintReadySizeString,
} from "@/app/helpers/getPrintReadySize";

export default function CardType({
  cardType,
  softwareAcceleration = false,
}: {
  cardType: "character" | "leader" | "event" | "stage" | "don";
  softwareAcceleration?: boolean;
}) {
  const printReady = useGetStoreState("printReady");
  const eventBorder = useGetStoreState("eventBorder");

  const textRef = useRef<null | HTMLSpanElement>(null);
  const SPTop = useGetStoreState("SPTop");
  const dispatch = useDispatch();

  const maxWidth = useGetResponsiveFontsizeInPx({
    desiredTextSizeInPx: getPrintReadySizeNumber(294.44496, printReady),
  });
  const maxWidth2 = useGetResponsiveFontsizeInPx({
    desiredTextSizeInPx: getPrintReadySizeNumber(334.04496, printReady),
  });
  const containerWidth =
    textRef.current?.parentElement?.parentElement?.offsetWidth;

  // console.log("width", Number(containerWidth) > Number(maxWidth));
  const characterBorder = useGetStoreState("characterBorder");
  const colorArray = useGetStoreState("colorArray");
  const color = colorArray[0];
  const shadowColor = getCardColor(
    color,
    cardType === "character" && characterBorder === "none",
    false,
    cardType === "character" && characterBorder === "sp-v2",
    false,
    false,
    false,
    undefined,
    { border: cardType === "event" && eventBorder === "eb-02", other: false },
  );
  const characterBorderEnabled = useGetStoreState("leaderBorderEnabled");

  const leaderBorder = useGetStoreState("leaderBorder");
  const cardTypeText = useGetStoreState("cardType") as string;
  const typeFontSize = useGetStoreState("typeFontSize") as number;
  const fontsize = useGetResponsiveFontsizeInPx({
    desiredTextSizeInPx: typeFontSize,
  });
  const fontsize2 = useGetResponsiveFontsizeInPx({
    desiredTextSizeInPx: 19.2,
  });
  const padding = useGetResponsiveFontsizeInPx({ desiredTextSizeInPx: 20 });
  const padding2 = useGetResponsiveFontsizeInPx({ desiredTextSizeInPx: 10 });
  const [scaleMultiplier, setScaleMultiplier] = useState(1);
  const [transformOrigin, setTransformOrigin] = useState<"center" | "left">(
    "center",
  );
  const minTextLength = 20;
  useEffect(() => {
    if (
      Number(containerWidth) > Number(maxWidth) &&
      !SPTop &&
      characterBorder !== "sp-v2" &&
      characterBorder !== "none"
    ) {
      handeSPTopChange(dispatch, true);
    } else if (
      Number(containerWidth) < Number(maxWidth) &&
      SPTop &&
      characterBorder !== "sp-v2" &&
      characterBorder !== "none"
    ) {
      handeSPTopChange(dispatch, false);
    }

    if (
      Number(containerWidth) > Number(maxWidth2) &&
      !SPTop &&
      (characterBorder === "sp-v2" || characterBorder === "none")
    ) {
      handeSPTopChange(dispatch, true);
    } else if (
      (Number(containerWidth) < Number(maxWidth2) &&
        SPTop &&
        (characterBorder === "sp-v2" || characterBorder === "none")) ||
      containerWidth === undefined
    ) {
      handeSPTopChange(dispatch, false);
    }
    if (textRef.current) {
      // Get the parent element width dynamically
      let parentWidth = 0;
      if (textRef.current.parentElement?.offsetWidth) {
        parentWidth = textRef.current.parentElement?.offsetWidth + 12;
      }

      // Set the minWidth dynamically to the parent's width
      const minWidth = parentWidth;

      // Get the current width of the text element
      const newWidth = textRef.current.offsetWidth;

      // Calculate the scale factor based on minWidth
      const sc = minWidth / newWidth;

      // Adjust transform origin based on width comparison
      if (newWidth >= minWidth && transformOrigin !== "left") {
        setTransformOrigin("left");
      } else if (transformOrigin !== "center" && newWidth < minWidth) {
        setTransformOrigin("center");
      }

      // Update scaleMultiplier if text exceeds minTextLength
      if (cardTypeText.length > minTextLength && scaleMultiplier !== sc) {
        setScaleMultiplier(sc);
      }
    }
    // console.log(cardTypeText);
  }, [
    cardTypeText,
    minTextLength,
    scaleMultiplier,
    transformOrigin,
    characterBorder,
    containerWidth,
    maxWidth,
    SPTop,
    maxWidth2,
    dispatch,
  ]);

  const scale = scaleMultiplier;
  const textColor = getTextColor(
    color,
    characterBorder,
    cardType,
    leaderBorder,
    "card_type",
    colorArray,
    eventBorder,
  );
  let backgroundImage = "";
  let highlightBoxShadow = "";
  let backgroundColor = "rgba(0, 0, 0, 0.15)";
  if (
    (cardType === "character" ||
      cardType === "stage" ||
      (cardType === "event" && eventBorder !== "eb-02")) &&
    colorArray.length > 1
  ) {
    backgroundImage = "linear-gradient(to right, #000,  #000)";
  }
  if (cardType === "event" && eventBorder === "op-10") {
    backgroundImage = `linear-gradient(to right, #000,  #000)`;
  }
  if (
    cardType === "character" &&
    (characterBorder === "none" ||
      characterBorder === "sp-v2" ||
      (characterBorder === "sec-aa" && colorArray.length === 1) ||
      (characterBorder === "sec" && colorArray.length === 1))
  ) {
    backgroundImage = `linear-gradient(to right, ${shadowColor},  ${shadowColor})`;
  }

  if (
    cardType === "character" &&
    (characterBorder === "mr" ||
      characterBorder === "sec-aa" ||
      characterBorder === "sec") &&
    color === "yellow"
  ) {
    backgroundImage = `linear-gradient(to right, #000,  #000)`;
  }
  if (
    (cardType === "character" &&
      characterBorder !== "none" &&
      characterBorder !== "sp-v2") ||
    cardType === "event" ||
    cardType === "stage"
  ) {
    highlightBoxShadow = `rgba(255, 255, 255, 0.5) -0.1em -0.53em 0.1em -0.4em, 
                rgba(255, 255, 255, 0.3) -0.2em -0.23em 0.25em -0.15em, 
                rgba(255, 255, 255, 0.05) 0.1em 0.55em 0em -0.4em, 
                rgba(255, 255, 255, 0.05) 0.1em 0.32em 0em -0.15em , 
                rgba(255, 255, 255, 0.05) 0.16em 0em 0em -0em`;
  }
  if (cardType === "leader" && leaderBorder === "standard") {
    highlightBoxShadow = `rgba(255, 255, 255, 0.0) -0.1em -0.53em 0.1em -0.4em, 
                rgba(255, 255, 255, 0.005) -0.2em -0.23em 0.25em -0.15em, 
                rgba(255, 255, 255, 0.00) 0.1em 0.55em 0em -0.4em, 
                rgba(255, 255, 255, 0.10) 0.1em 0.32em 0em -0.15em , 
                rgba(255, 255, 255, 0.05) 0.16em 0em 0em -0em`;
  }
  if (cardType !== "leader" && cardType !== "don" && colorArray.length > 1) {
    if (
      cardType === "character" &&
      (characterBorder === "none" || characterBorder === "sp-v2")
    ) {
    } else {
      highlightBoxShadow = `rgba(255, 255, 255, 0.0) -0.1em -0.53em 0.1em -0.4em, 
                rgba(255, 255, 255, 0.3) -0.2em -0.23em 0.25em -0.15em, 
                rgba(255, 255, 255, 0.05) 0.1em 0.55em 0em -0.4em, 
                rgba(255, 255, 255, 0.05) 0.1em 0.32em 0em -0.15em , 
                rgba(255, 255, 255, 0.05) 0.16em 0em 0em -0em`;
    }
  }
  if (
    cardType === "leader" &&
    (leaderBorder === "AA-black-and-white" || leaderBorder === "AA-black")
  ) {
    backgroundColor = "#080809";
  }
  if (cardType === "leader" && leaderBorder === "AA-white") {
    backgroundColor = "#fff";
  }
  if (cardType === "leader" && leaderBorder === "full-art") {
    backgroundImage = `linear-gradient(to right, #fff,  #fff)`;
  }
  return (
    <>
      <div
        className="font-libre-franklin absolute flex items-center justify-center text-center"
        style={{
          bottom: printReady
            ? getPrintReadyDistanceString("4.216%", "top-or-bottom", printReady)
            : "4.156%",
          left: getPrintReadyDistanceString("23%", "left-or-right", printReady),
          height: getPrintReadySizeString("3.1759%", printReady),
          width: getPrintReadySizeString("53.1%", printReady),
        }}
      >
        {cardTypeText.length > 0 && (
          <div
            style={{
              boxShadow:
                cardType === "event" &&
                (eventBorder === "eb-02" || eventBorder === "op-10")
                  ? ""
                  : highlightBoxShadow,
              zIndex: characterBorderEnabled
                ? 4
                : cardType === "character" &&
                    (characterBorder === "sec" || characterBorder === "sec-aa")
                  ? 2
                  : 4,
            }}
            className={`relative z-0 ${transformOrigin === "left" ? "w-[100%]" : ""} max-w-[100%] min-w-[74%] rounded-full`}
          >
            <p
              style={{
                fontSize: `${getPrintReadySizeNumber(fontsize2, printReady)}px`,
                borderColor: `linear-gradient(to right, ${shadowColor},  #000)`,

                // boxShadow: `rgba(0, 0, 0, 1) 0px ${shadowSize}px 0.0525em -1.042em inset,  rgba(0, 0, 0, 1) ${shadowSize}px 0px 0.0525em -1.042em inset`,
                boxShadow:
                  colorArray.length === 1
                    ? (cardType === "character" &&
                        (characterBorder === "none" ||
                          characterBorder === "sp-v2")) ||
                      cardType === "leader"
                      ? ``
                      : cardType === "event" && eventBorder === "eb-02"
                        ? `${shadowColor} 0em 0em 0em 0.1em`
                        : cardType === "event" && eventBorder === "op-10"
                          ? ""
                          : `${shadowColor} -0em -0.15em 0.05em -0em `
                    : cardType === "event" && eventBorder === "eb-02"
                      ? `#000 0em 0em 0em 0.1em`
                      : "",

                letterSpacing: `${getPrintReadySizeNumber(0.057, printReady)}em`,
              }}
              className={` ${(cardType === "character" && (characterBorder === "sec" || characterBorder === "mr" || characterBorder === "sec-aa")) || (colorArray.length > 1 && cardType !== "leader" && cardType !== "character" && cardType !== "event") || (cardType === "event" && eventBorder !== "op-10") || (colorArray.length > 1 && cardType === "character" && characterBorder !== "none" && characterBorder !== "sp-v2") ? "text-outline-small" : "text-outline-large"} relative grid h-[120%] max-w-[100%] min-w-[74%] items-center overflow-hidden rounded-full ${cardType === "event" && eventBorder === "eb-02" ? "border-[0.1em]" : ""} align-middle`}
            >
              <span
                style={{
                  fontSize: `${getPrintReadySizeNumber(fontsize, printReady)}px`,
                  boxShadow:
                    (cardType === "character" &&
                      characterBorder !== "none" &&
                      characterBorder !== "sp-v2") ||
                    (cardType === "leader" && leaderBorder !== "full-art") ||
                    (cardType === "event" &&
                      eventBorder !== "eb-02" &&
                      eventBorder !== "op-10") ||
                    cardType === "stage"
                      ? softwareAcceleration
                        ? `rgba(0, 0, 0, 0.4) 0px 1.62em 0.1525em -1.46em inset, 
                    rgba(0, 0, 0, 0.2) 1.142em 1.25em 0.1525em -1.4063em inset, 
                    rgba(0, 0, 0, 0.4) 1.56em 0.625em 0.1525em -1.4063em inset, 
                    rgba(0, 0, 0, 0.4) 1.094em -1.042em 0.1525em -1.4063em inset, 
                    rgba(0, 0, 0, 1) 0px -1.186em 0.521em -1.46em inset, 
                    rgba(0, 0, 0, 1) -1.146em 0px 0.521em -1.425em inset,
                    rgba(0, 0, 0, 0.4) -1.094em 1.042em 0.0525em -1.4063em inset,
                    ${backgroundColor} 0 2.865em 0.0525em -1.4063em inset`
                        : `rgba(0, 0, 0, 0.4) 0px 1.62em 0.1525em -1.46em inset, 
                    rgba(0, 0, 0, 0.2) 1.142em 1.25em 0.1525em -1.4063em inset, 
                    rgba(0, 0, 0, 0.4) 1.56em 0.625em 0.1525em -1.4063em inset, 
                    rgba(0, 0, 0, 0.4) 1.094em -1.042em 0.1525em -1.4063em inset, 
                    rgba(0, 0, 0, 1) 0px -1.146em 0.521em -1.46em inset, 
                    rgba(0, 0, 0, 1) -1.146em 0px 0.521em -1.46em inset,
                    rgba(0, 0, 0, 0.4) -1.094em 1.042em 0.0525em -1.4063em inset,
                    ${backgroundColor} 0 2.865em 0.0525em -1.4063em inset`
                      : "",
                  padding: `0 ${transformOrigin === "left" ? getPrintReadySizeNumber(padding2, printReady) + "px" : getPrintReadySizeNumber(padding, printReady) + "px"}`,
                  // letterSpacing: 0,
                  // transform: "scale(0.7, 1)",
                }}
                className={"rounded-full"}
              >
                <span
                  className={`text-outline ${textColor === "brightness-0" ? "brightness-0" : ""} font-stretch inline-block whitespace-nowrap`}
                  style={{
                    // letterSpacing: 0,
                    // transform: `${cardTypeText.length > 30 ? `scale(${scale}, 1) translateX(-${translateX}px)` : ""}`,
                    // transformOrigin: `${cardTypeText.length > 30 ? "center center" : ""}`,
                    color: `${textColor !== "brightness-0" ? textColor : ""}`,
                    backgroundImage: backgroundImage,
                    transform: `${transformOrigin === "left" ? `scale(${scale}, 1) ` : ""}`,
                    transformOrigin: `${transformOrigin === "left" ? `${transformOrigin} center` : ""}`,
                  }}
                >
                  {cardTypeText}
                </span>
              </span>
              <span
                className={`${textColor === "brightness-0" ? "brightness-0" : ""} font-stretch absolute inline-block whitespace-nowrap opacity-0`}
                style={
                  {
                    // letterSpacing: 0,
                    // transform: `${cardTypeText.length > 30 ? `scale(${scale}, 1) translateX(-${translateX}px)` : ""}`,
                    // transformOrigin: `${cardTypeText.length > 30 ? "center center" : ""}`,
                  }
                }
                ref={textRef}
              >
                {cardTypeText}
              </span>
            </p>
          </div>
        )}
      </div>
      <div
        className="font-libre-franklin absolute flex items-center justify-center text-center"
        style={{
          bottom: printReady
            ? getPrintReadyDistanceString("4.206%", "top-or-bottom", printReady)
            : "4.156%",
          left: printReady
            ? getPrintReadyDistanceString(
                "22.9995%",
                "left-or-right",
                printReady,
              )
            : "23%",
          height: getPrintReadySizeString("3.1759%", printReady),
          width: getPrintReadySizeString("53.1%", printReady),
        }}
      >
        <div
          style={{
            zIndex: !characterBorderEnabled ? 4 : -1,
          }}
          className={`relative z-0 ${transformOrigin === "left" ? "w-[100%]" : ""} max-w-[100%] min-w-[74%] rounded-full`}
        >
          <p
            style={{
              fontSize: `${getPrintReadySizeNumber(fontsize2, printReady)}px`,
              letterSpacing: `${getPrintReadySizeNumber(0.057, printReady)}em`,
            }}
            className={` ${(cardType === "character" && (characterBorder === "sec" || characterBorder === "mr" || characterBorder === "sec-aa")) || (colorArray.length > 1 && cardType !== "leader" && cardType !== "character" && cardType !== "event") || (cardType === "event" && eventBorder !== "op-10") || (colorArray.length > 1 && cardType === "character" && characterBorder !== "none" && characterBorder !== "sp-v2") ? "text-outline-small" : "text-outline-large"} relative grid h-[120%] max-w-[100%] min-w-[74%] items-center overflow-hidden rounded-full align-middle`}
          >
            <span
              style={{
                fontSize: `${getPrintReadySizeNumber(fontsize, printReady)}px`,
                padding: `0 ${transformOrigin === "left" ? getPrintReadySizeNumber(padding2, printReady) + "px" : getPrintReadySizeNumber(padding, printReady) + "px"}`,
              }}
              className={"rounded-full"}
            >
              <span
                className={`text-outline ${textColor === "brightness-0" ? "brightness-0" : ""} font-stretch inline-block whitespace-nowrap`}
                style={{
                  color: `${textColor !== "brightness-0" ? textColor : ""}`,
                  backgroundImage: backgroundImage,
                  transform: `${transformOrigin === "left" ? `scale(${scale}, 1) ` : ""}`,
                  transformOrigin: `${transformOrigin === "left" ? `${transformOrigin} center` : ""}`,
                }}
              >
                {cardTypeText}
              </span>
            </span>
          </p>
        </div>
      </div>
    </>
  );
}
// ${transformOrigin === "left" ? "left-[-0.09em]" : ""}
