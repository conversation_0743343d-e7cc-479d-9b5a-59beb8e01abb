"use client";

const innerBorderBottom = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Inner-Border-Bottom.svg`;
const printReadyInnerBorderBottom = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Print-Ready-Inner-Border-Bottom.svg`;
const borderBottom = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Card-Border-Bottom.svg`;
const printReadyBorderBottom = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Print-Ready-Card-Border-Bottom.svg`;
const printReadySrPatternBorderBottom = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Print-Ready-Event-Stage-Character_SR-Border-Pattern-Border-Bottom.svg`;
const srPatternBorderBottom = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Event-Stage-Character_SR-Border-Pattern-Border-Bottom.svg`;
const innerBorderBottomShadow = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Inner-Border-Bottom-Shadow-v2.svg`;
const printReadyInnerBorderBottomShadow = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Print-Ready-Inner-Border-Bottom-Shadow-v2.svg`;
const SPL2 = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Character-SP-V2-L2.svg`;
const printReadySPL2 = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Print-Ready-Character-SP-V2-L2.svg`;
const SPL1 = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Character-SP-V2-L1.svg`;
const printReadySPL1 = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Print-Ready-Character-SP-V2-L1.svg`;
const SPL3 = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Character-SP-V2-L3.svg`;
const printReadySPL3 = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Print-Ready-Character-SP-V2-L3.svg`;
import { useGetStoreState } from "@/helpers/useGetStoreState";
import { CharacterBorder } from "@/types";

import OnePieceReactSvgWithGradient from "@/components/OnePieceReactSvgWithGradient";

import React from "react";
import { getCardColorHex } from "@/app/helpers/getCardColorHex";

export default function CharacterCardBorderBottom({
  cardType,
}: {
  cardType: "character" | "leader" | "event" | "stage" | "don";
  quality: number;
}) {
  const printReady = useGetStoreState("printReady");
  const characterBorder = useGetStoreState(
    "characterBorder",
  ) as CharacterBorder;
  const leaderBorderEnabled = useGetStoreState("leaderBorderEnabled");
  const colorArray = useGetStoreState("colorArray");

  const colorHexValues = colorArray.map((color) => getCardColorHex({ color }));

  let innerBorder = true;
  if (characterBorder === "sec" || characterBorder === "sec-aa") {
    innerBorder = false;
  }

  return (
    <>
      {(cardType === "character" ||
        cardType === "event" ||
        cardType === "stage") && (
        <>
          {(characterBorder === "standard" || characterBorder === "sr") && (
            <>
              {/*Border around the card*/}
              <OnePieceReactSvgWithGradient
                printReady={printReady}
                printReadySrc={printReadyBorderBottom}
                standardSrc={borderBottom}
                className={`absolute top-0 z-4`}
                colorArray={characterBorder === "sr" ? ["#DAE1E8"] : ["#fff"]}
              />
              {/*Border around the card SR*/}
              {characterBorder === "sr" && (
                <OnePieceReactSvgWithGradient
                  printReady={printReady}
                  printReadySrc={printReadySrPatternBorderBottom}
                  standardSrc={srPatternBorderBottom}
                  className={`absolute top-0 z-4`}
                  colorArray={["#fff"]}
                />
              )}
            </>
          )}

          {innerBorder && !leaderBorderEnabled && (
            <>
              {(characterBorder === "standard" ||
                characterBorder === "sr" ||
                characterBorder === "rare" ||
                characterBorder === "mr") && (
                // Inner border shadow outline
                <>
                  <OnePieceReactSvgWithGradient
                    printReady={printReady}
                    printReadySrc={printReadyInnerBorderBottomShadow}
                    standardSrc={innerBorderBottomShadow}
                    className={`absolute top-[-0.3%] left-[-0.3%] z-4 h-[100.5%] w-[100.7%] opacity-[7.5%]`}
                  />
                </>
              )}
              {/*Inner border*/}
              <OnePieceReactSvgWithGradient
                printReady={printReady}
                printReadySrc={printReadyInnerBorderBottom}
                standardSrc={innerBorderBottom}
                className={`absolute top-0 z-4 ${(characterBorder === "none" || characterBorder === "sp-v2") && cardType === "character" ? "opacity-0" : ""}`}
                colorArray={colorHexValues}
              />
            </>
          )}
          {characterBorder === "sp-v2" && cardType === "character" && (
            <>
              {/*SP V2 Card name background 1 (dimmer version)*/}
              <OnePieceReactSvgWithGradient
                printReady={printReady}
                printReadySrc={printReadySPL1}
                standardSrc={SPL1}
                className={`absolute top-0 z-4 brightness-50`}
                colorArray={colorHexValues}
              />
              {/*SP V2 Card name background 2 (brighter version) */}
              <OnePieceReactSvgWithGradient
                printReady={printReady}
                printReadySrc={printReadySPL2}
                standardSrc={SPL2}
                className={`absolute top-0 z-4`}
                colorArray={colorHexValues}
              />

              <OnePieceReactSvgWithGradient
                printReady={printReady}
                printReadySrc={printReadySPL3}
                standardSrc={SPL3}
                className={`absolute top-0 z-4`}
                // colorArray={colorHexValues}
              />
            </>
          )}
        </>
      )}
    </>
  );
}
