"use client";
const triangleTopRight = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Trinagle-Top-Right-New.png`;
const optimizedTriangleTopRight = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Trinagle-Top-Right-New.webp`;
const triangleTopLeft = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Trinagle-Top-Left-New.png`;
const optimizedTriangleTopLeft = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Trinagle-Top-Left-New.webp`;
const triangleBottomLeft = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Trinagle-Bottom-Left-New.png`;
const optimizedTriangleBottomLeft = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Trinagle-Bottom-Left-New.webp`;
const triangleBottomRight = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Trinagle-Bottom-Right-New.png`;
const optimizedTriangleBottomRight = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Trinagle-Bottom-Right-New.webp`;
const triangleLeft = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Trinagle-Left-New.png`;
const optimizedTriangleLeft = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Trinagle-Left-New.webp`;
const triangleRight = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Trinagle-Right-New.png`;
const optimizedTriangleRight = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Trinagle-Right-New.webp`;

const triangleTopRightEvent = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Trinagle-Top-Right-Event.png`;
const optimizedTriangleTopRightEvent = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Trinagle-Top-Right-Event.webp`;
const triangleTopLeftEvent = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Trinagle-Top-Left-Event.png`;
const optimizedTriangleTopLeftEvent = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Trinagle-Top-Left-Event.webp`;
const triangleBottomLeftEvent = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Trinagle-Bottom-Left-Event.png`;
const optimizedTriangleBottomLeftEvent = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Trinagle-Bottom-Left-Event.webp`;
const triangleBottomRightEvent = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Trinagle-Bottom-Right-Event.png`;
const optimizedTriangleBottomRightEvent = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Trinagle-Bottom-Right-Event.webp`;
const triangleLeftEvent = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Trinagle-Left-Event.png`;
const optimizedTriangleLeftEvent = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Trinagle-Left-Event.webp`;
const triangleRightEvent = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Trinagle-Right-Event.png`;
const optimizedTriangleRightEvent = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Trinagle-Right-Event.webp`;
import { EventBorder, TrianglePosition } from "@/types";
export default function ColorWheelTriangle({
  cardColor,
  position,
  png = false,
  cardKind,
  eventBorder,
}: {
  cardColor: string;
  position?: TrianglePosition;
  png?: boolean;
  cardKind: "character" | "leader" | "event" | "stage" | "don";
  eventBorder: EventBorder;
}) {
  let triangleImageTopRight = "";
  let triangleImageTopLeft = "";
  let triangleImageBottomLeft = "";
  let triangleImageBottomRight = "";
  let triangleImageLeft = "";
  let triangleImageRight = "";
  if (cardKind === "event" && eventBorder === "eb-02") {
    triangleImageTopRight = png
      ? triangleTopRightEvent
      : optimizedTriangleTopRightEvent;
    triangleImageTopLeft = png
      ? triangleTopLeftEvent
      : optimizedTriangleTopLeftEvent;
    triangleImageBottomLeft = png
      ? triangleBottomLeftEvent
      : optimizedTriangleBottomLeftEvent;
    triangleImageBottomRight = png
      ? triangleBottomRightEvent
      : optimizedTriangleBottomRightEvent;
    triangleImageLeft = png ? triangleLeftEvent : optimizedTriangleLeftEvent;
    triangleImageRight = png ? triangleRightEvent : optimizedTriangleRightEvent;
  } else {
    triangleImageTopRight = png ? triangleTopRight : optimizedTriangleTopRight;
    triangleImageTopLeft = png ? triangleTopLeft : optimizedTriangleTopLeft;
    triangleImageBottomLeft = png
      ? triangleBottomLeft
      : optimizedTriangleBottomLeft;
    triangleImageBottomRight = png
      ? triangleBottomRight
      : optimizedTriangleBottomRight;
    triangleImageLeft = png ? triangleLeft : optimizedTriangleLeft;
    triangleImageRight = png ? triangleRight : optimizedTriangleRight;
  }
  if (position === "top-right") {
    return (
      <>
        <div
          className={"color-wheel-triangle brightness-[100%]"}
          style={{
            background: `linear-gradient(135deg, ${cardColor},  ${cardColor})`,
            maskImage: `url(${triangleImageTopRight})`,
          }}
        ></div>
        <div
          className={"color-wheel-triangle opacity-[20%]"}
          style={{
            background: `linear-gradient(35deg, #fff 50%,  #000 90%)`,
            maskImage: `url(${triangleImageTopRight})`,
          }}
        ></div>
      </>
    );
  }

  if (position === "right") {
    return (
      <>
        <div
          className={"color-wheel-triangle brightness-[100%]"}
          style={{
            background: `linear-gradient(135deg, ${cardColor},  ${cardColor})`,
            maskImage: `url(${triangleImageRight}) `,
          }}
        ></div>
        <div
          className={"color-wheel-triangle opacity-[20%]"}
          style={{
            background: `linear-gradient(90deg, #fff 82%,  #000 100%)`,
            maskImage: `url(${triangleImageRight}) `,
          }}
        ></div>
      </>
    );
  }
  if (position === "bottom-right") {
    return (
      <>
        <div
          className={"color-wheel-triangle brightness-[100%]"}
          style={{
            background: `linear-gradient(135deg, ${cardColor},  ${cardColor})`,
            maskImage: `url(${triangleImageBottomRight}) `,
          }}
        ></div>
        <div
          className={"color-wheel-triangle opacity-[20%]"}
          style={{
            background: `linear-gradient(140deg, #fff 60%,  #000 85%)`,
            maskImage: `url(${triangleImageBottomRight}) `,
          }}
        ></div>
      </>
    );
  }
  if (position === "bottom-left") {
    return (
      <>
        <div
          className={"color-wheel-triangle brightness-[100%]"}
          style={{
            background: `linear-gradient(135deg, ${cardColor},  ${cardColor})`,
            maskImage: `url(${triangleImageBottomLeft}) `,
          }}
        ></div>
        <div
          className={"color-wheel-triangle opacity-[20%]"}
          style={{
            background: `linear-gradient(30deg, #000 20%,  #fff 60%)`,
            maskImage: `url(${triangleImageBottomLeft}) `,
          }}
        ></div>
      </>
    );
  }
  if (position === "left") {
    return (
      <>
        <div
          className={"color-wheel-triangle brightness-[100%]"}
          style={{
            background: `linear-gradient(135deg, ${cardColor},  ${cardColor})`,
            maskImage: `url(${triangleImageLeft}) `,
          }}
        ></div>
        <div
          className={"color-wheel-triangle opacity-[20%]"}
          style={{
            background: `linear-gradient(90deg, #000 0%,  #fff 20%)`,
            maskImage: `url(${triangleImageLeft}) `,
          }}
        ></div>
      </>
    );
  }
  if (position === "top-left") {
    return (
      <>
        <div
          className={"color-wheel-triangle brightness-[100%]"}
          style={{
            background: `linear-gradient(135deg, ${cardColor},  ${cardColor})`,
            maskImage: `url(${triangleImageTopLeft}) `,
          }}
        ></div>
        <div
          className={"color-wheel-triangle opacity-[20%]"}
          style={{
            background: `linear-gradient(150deg, #000 13%,  #fff 32%)`,
            maskImage: `url(${triangleImageTopLeft}) `,
          }}
        ></div>
      </>
    );
  }
}
