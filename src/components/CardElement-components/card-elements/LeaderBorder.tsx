"use client";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import CardBorder from "@/components/CardElement-components/card-elements/CardBorder";
import React from "react";

export default function LeaderBorder() {
  const leaderBorderEnabled = useGetStoreState("leaderBorderEnabled");
  if (leaderBorderEnabled)
    return <CardBorder cardType={"leader"} quality={25} zIndex={4} />;
  return null;
}
