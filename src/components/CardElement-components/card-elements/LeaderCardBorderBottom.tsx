"use client";
import Image from "next/image";

import { useGetStoreState } from "@/helpers/useGetStoreState";
import { getCardType } from "@/helpers/getCardType";
const borderBottom = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Card-Border-Bottom.svg`;
const printReadyBorderBottom = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Print-Ready-Card-Border-Bottom.svg`;

const leaderInnerBorder = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Leader-Inner-Border-Bottom.svg`;
const printReadyLeaderInnerBorder = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Print-Ready-Leader-Inner-Border-Bottom.svg`;

const leaderBorderPattern = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Leader-Inner-Border-Bottom-Pattern-New.svg`;
const printReadyLeaderBorderPattern = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Print-Ready-Leader-Inner-Border-Bottom-Pattern-New.svg`;

const innerBorderBottomShadow = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Inner-Border-Bottom-Shadow-v2.svg`;
const printReadyInnerBorderBottomShadow = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Print-Ready-Inner-Border-Bottom-Shadow-v2.svg`;
import getLeaderBorderGradient from "@/app/helpers/getLeaderBorderGradient";
import getMultiColorBorderGradient from "@/app/helpers/getMultiColorBorderGradient";
import { LeaderBorder } from "@/types";
import getMultiColorBorderGradientHex from "@/app/helpers/getMultiColorBorderGradientHex";
import getLeaderBorderGradientSVG from "@/app/helpers/getLeaderBorderGradientSVG";
import OnePieceLeaderBorderReactSVGWithGradientOP from "@/components/OnePieceLeaderBorderReactSVGWithGradientOP";
import React from "react";
import OnePieceReactSvgWithGradient from "@/components/OnePieceReactSvgWithGradient";
import { getPatternGradientSVG } from "@/app/helpers/getPatternGradientSVG";

export default function LeaderCardBorderBottom({
  absolute = false,
  cardType,
  quality,
  png = false,
}: {
  cardType: "character" | "leader" | "event" | "stage" | "don";
  quality: number;
  png?: boolean;
  absolute?: boolean;
}) {
  const printReady = useGetStoreState("printReady");
  const cardTypeRoute = getCardType(cardType);

  const colorArray = useGetStoreState("colorArray");
  const color = colorArray[0];
  const color2 = colorArray[colorArray.length - 1];
  const leaderBorderEnabled = useGetStoreState("leaderBorderEnabled");

  const leaderBorder = useGetStoreState("leaderBorder") as LeaderBorder;

  // Use the multi-color border gradient function if we have more than 2 colors
  const useMultiColor =
    Array.isArray(colorArray) &&
    colorArray.length > 2 &&
    (cardType !== "leader" ||
      (cardType === "leader" && leaderBorder !== "rainbow"));

  // First gradient (CSS background)
  const [backgroundGradient, initialBlendMode] = useMultiColor
    ? getMultiColorBorderGradient(colorArray, leaderBorder, cardType)
    : getLeaderBorderGradient(color, color2, leaderBorder, cardType);

  let backgroundBlendMode = initialBlendMode;

  // Second gradient (SVG)
  const [
    backgroundGradientSVG,
    initialBlendModeSVG,
    initialPatternGradientSVG,
  ] = useMultiColor
    ? getMultiColorBorderGradientHex(colorArray, leaderBorder, cardType)
    : getLeaderBorderGradientSVG(color, color2, leaderBorder, cardType);

  let backgroundBlendModeSVG = initialBlendModeSVG;
  let patternGradientSVG = initialPatternGradientSVG;

  // For multiple colors, set the appropriate blend mode
  if (
    (Array.isArray(colorArray) &&
      colorArray.length >= 2 &&
      color !== color2 &&
      cardType !== "leader") ||
    (Array.isArray(colorArray) &&
      colorArray.length >= 3 &&
      color !== color2 &&
      cardType === "leader")
  ) {
    // Use screen for 2 colors, and soft-light for 3+ colors
    backgroundBlendMode = colorArray.length === 2 ? "screen" : "soft-light";
    backgroundBlendModeSVG = colorArray.length === 2 ? "screen" : "soft-light";
  }

  // Generate a custom pattern gradient for multiple colors
  if (Array.isArray(colorArray) && colorArray.length > 2) {
    patternGradientSVG = getPatternGradientSVG(colorArray);
  }

  return (
    <>
      {!leaderBorderEnabled && cardType === "leader" && (
        <>
          <OnePieceLeaderBorderReactSVGWithGradientOP
            printReady={printReady}
            printReadySrc={printReadyBorderBottom}
            standardSrc={borderBottom}
            className={`border-bottom-gradient-svg top-0 ${
              leaderBorder === "standard" ||
              leaderBorder === "AA-white" ||
              leaderBorder === "AA-black-and-white" ||
              leaderBorder === "standard-white"
                ? "opacity-100"
                : "opacity-0!"
            }`}
            colorArray={backgroundGradientSVG}
            style={{ zIndex: 4 }}
            leaderBorder={leaderBorder}
            blendMode={backgroundBlendModeSVG}
          />

          {leaderBorder === "rainbow" && (
            <div
              className={`border-bottom-gradient top-0 ${absolute ? "absolute" : "relative"} `}
              style={{
                maskImage: `url(${printReady ? printReadyBorderBottom : borderBottom})`,
                background: backgroundGradient,
                backgroundBlendMode: backgroundBlendMode,
                zIndex: 4,
              }}
            ></div>
          )}

          <>
            {leaderBorder === "standard-white" && (
              <Image
                width={printReady ? 3677 : 3357}
                height={printReady ? 5011 : 4692}
                priority
                loading={"eager"}
                title={`${cardTypeRoute} card border`}
                className={`absolute top-0 max-w-full`}
                src={
                  png
                    ? `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/${printReady ? "Print-Ready-" : ""}Black-White-Border-Bottom-Pattern.png`
                    : `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/${printReady ? "Print-Ready-" : ""}Black-White-Border-Bottom-Pattern.webp`
                }
                alt={`${cardTypeRoute} card border`}
                quality={quality}
                style={{ zIndex: 4 }}
                sizes={
                  quality !== 100 ? "(max-width: 1400px) 100vw, 1400px" : ""
                }
              />
            )}
            {leaderBorder !== "full-art" && (
              <>
                <OnePieceReactSvgWithGradient
                  printReady={printReady}
                  printReadySrc={printReadyInnerBorderBottomShadow}
                  standardSrc={innerBorderBottomShadow}
                  className={`border-gradient-shadow-svg top-0 opacity-[15%]`}
                  colorArray={["#000"]}
                  style={{ zIndex: 4 }}
                />

                <OnePieceReactSvgWithGradient
                  printReady={printReady}
                  printReadySrc={printReadyLeaderInnerBorder}
                  standardSrc={leaderInnerBorder}
                  className={`border-bottom-gradient-svg top-0`}
                  colorArray={
                    leaderBorder === "AA-white" ? ["#fff"] : ["#080809"]
                  }
                  style={{ zIndex: 4 }}
                />
                <OnePieceLeaderBorderReactSVGWithGradientOP
                  leaderBorder={leaderBorder}
                  printReady={printReady}
                  printReadySrc={printReadyLeaderBorderPattern}
                  standardSrc={leaderBorderPattern}
                  className={`border-bottom-gradient-svg top-0 ${leaderBorder === "AA-white" ? "opacity-70" : leaderBorder === "AA-black" || leaderBorder === "AA-black-and-white" ? "opacity-50" : "opacity-0"}`}
                  colorArray={patternGradientSVG}
                  blendMode={backgroundBlendModeSVG}
                  style={{ zIndex: 4 }}
                />
              </>
            )}
          </>
        </>
      )}
    </>
  );
}
