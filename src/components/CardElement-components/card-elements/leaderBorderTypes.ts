import { LeaderBorder } from "@/types";

export const LEADER_BORDER_TYPES: { value: LeaderBorder; label: string }[] = [
  { value: "standard", label: "Standard" },
  { value: "standard-white", label: "Standard white" },
  { value: "25th", label: "25th Anniversary" },
  { value: "AA-white", label: "AA White" },
  { value: "AA-black", label: "AA Black" },
  { value: "AA-black-and-white", label: "AA Black and White" },
  { value: "rainbow", label: "Rainbow" },
  { value: "full-art", label: "Full Art" },
];
