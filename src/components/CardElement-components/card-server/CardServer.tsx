// import Image from "next/image";
// import { getCardType } from "@/helpers/getCardType";
// import { useGetStoreState } from "@/helpers/useGetStoreState";
//
// export default function CardServer() {
//   const cardTypeRoute = getCardType("character");
//   const color = "red";
//   return (
//     <div id={"card"} className={"relative h-[55.9rem] w-[40rem]"}>
//       <img
//         width={3357}
//         height={4692}
//         className={"absolute"}
//         src={`/assets/Color-Character/redBackground.png`}
//         alt={`${cardTypeRoute} ${color} card background`}
//       />
//     </div>
//   );
// }
