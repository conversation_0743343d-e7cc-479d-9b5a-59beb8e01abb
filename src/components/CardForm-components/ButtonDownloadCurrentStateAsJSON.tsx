"use client";

import { useSelector } from "react-redux";
import { storeState } from "@/store/store";
import { Button } from "@/components/ui/button";
import { MouseEventHandler } from "react";

export default function ButtonDownloadCurrentStateAsJson({
  downloadFor,
}: {
  downloadFor: "character" | "leader" | "event" | "stage" | "don";
}) {
  const state = useSelector((state: storeState) => state?.mainFormSlice);
  const download: MouseEventHandler<HTMLButtonElement> = (e) => {
    e.preventDefault();
    const cardName = state.name.trim() || "unnamed";
    const cardType = state.cardType.trim() || "card";
    const cardKindName = downloadFor;
    const safeCardName = cardName.replace(/[^a-zA-Z0-9-_]/g, "_");
    const safeCardType = cardType.replace(/[^a-zA-Z0-9-_]/g, "_");
    const filename = `${safeCardName}-${safeCardType}-${cardKindName}.json`;
    // Convert the object to a JSON string
    const jsonString = JSON.stringify(state, null, 2);

    // Create a Blob containing the JSON data
    const blob = new Blob([jsonString], { type: "application/json" });

    // Create a data URL from the Blob
    const url = URL.createObjectURL(blob);

    // Create an anchor tag
    const link = document.createElement("a");
    link.style.display = "none";
    // Set the download attribute and the href to the data URL
    link.href = url;
    link.download = filename;

    link.click();

    // Release the Blob URL
    URL.revokeObjectURL(url);
  };
  return (
    <Button
      className={"min-w-[189.23px] grow tracking-wide sm:grow-0"}
      onClick={download}
    >
      Download Preset
    </Button>
  );
}
