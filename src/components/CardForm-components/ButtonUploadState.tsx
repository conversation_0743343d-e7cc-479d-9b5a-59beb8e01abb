"use client";
import { useDispatch } from "react-redux";
import { onSetDefaultStore } from "@/store/formSlice";
import { MouseEvent<PERSON>and<PERSON>, useRef, useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

export default function ButtonUploadState({
  uploadFor,
  subscription,
}: {
  uploadFor: "character" | "leader" | "event" | "stage" | "don";
  subscription: {
    active: boolean;
    subscriptionName: string;
    lifetime?: boolean;
  };
}) {
  const dispatch = useDispatch();
  const [fileName, setFileName] = useState<string | null>(null);
  const [error, setError] = useState("");
  const inputRef = useRef<HTMLInputElement>(null);
  const handleClick: MouseEventHandler<HTMLButtonElement> = (e) => {
    e.preventDefault();
    inputRef.current?.click();
  };
  return (
    <>
      <Input
        placeholder={"Upload Preset"}
        className={"max-w-[189.23px] min-w-[189.23px] grow sm:grow-0"}
        accept={"application/json"}
        type={"file"}
        hidden
        aria-label={"Upload Preset"}
        ref={inputRef}
        // classNames={{
        //   input: "bg-white dark:bg-[#2E2E2E] ",
        //   error: "absolute mt-[5px]",
        //   wrapper: "mb-0",
        // }}
        onChange={(e) => {
          const file = e.target.files?.[0];
          if (file) {
            const reader = new FileReader();

            reader.onload = (e) => {
              try {
                if (e?.target?.result) {
                  const parsedData = JSON.parse(e?.target?.result as string);

                  if (parsedData.cardKindRoute === uploadFor) {
                    const withoutLinks = stripLinksKeepText_Browser(
                      parsedData.ability,
                    );
                    setError("");
                    if (parsedData.printReady) {
                      if (
                        subscription.lifetime ||
                        (subscription.active &&
                          subscription.subscriptionName.includes("Creator"))
                      ) {
                        setFileName(file.name);
                        dispatch(
                          onSetDefaultStore({
                            ...parsedData,
                            ability: withoutLinks,
                          }),
                        );
                      } else {
                        setError(
                          `You need to subscribe to "Creator" plan to use print ready presets.`,
                        );
                      }
                    } else {
                      setFileName(file.name);
                      dispatch(
                        onSetDefaultStore({
                          ...parsedData,
                          ability: withoutLinks,
                        }),
                      );
                    }
                  } else {
                    setError(
                      `This preset is for ${parsedData.cardKindRoute[0].toLocaleUpperCase()}${parsedData.cardKindRoute.slice(1)}.`,
                    );
                  }
                }
              } catch (error) {
                if (error instanceof TypeError) {
                  console.error("A TypeError occurred:", error.message);
                }
              }
            };

            // Read the contents of the file as text
            reader.readAsText(file);
          }
        }}
      />
      <div className={"relative flex min-w-[189.23px] grow flex-col sm:grow-0"}>
        <Button
          onClick={handleClick}
          variant={"outline"}
          className={`bg-background ${error && "border-red-500 text-red-500"} inline-block truncate py-0! duration-75! md:max-w-[189.23px]`}
        >
          {fileName ? fileName : "Upload preset"}
        </Button>
        <span
          className={
            "absolute mt-10 text-sm text-red-500 transition-all! duration-75!"
          }
        >
          {error}
        </span>
      </div>
    </>
  );
}
function stripLinksKeepText_Browser(htmlString: string) {
  const tempDiv = document.createElement("div");
  tempDiv.innerHTML = htmlString;

  const anchors = tempDiv.querySelectorAll("a");
  if (anchors.length >= 1) {
    anchors.forEach((anchor) => {
      const span = document.createElement("span");
      span.textContent = anchor.textContent || ""; // Get the text
      if (anchor.parentNode) {
        anchor.parentNode.replaceChild(span, anchor); // Replace <a> with <span>
      }
    });
  }

  return tempDiv.innerHTML;
}
