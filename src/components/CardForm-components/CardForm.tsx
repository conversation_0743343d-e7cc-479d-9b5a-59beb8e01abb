"use client";

import { useDispatch } from "react-redux";

import { onCardKindRoute } from "@/store/formSlice";
import { ReactNode, useEffect } from "react";

export default function CardForm({
  children,
  formFor,
}: {
  children: ReactNode;
  formFor: "character" | "leader" | "event" | "stage" | "don";
}) {
  const dispatch = useDispatch();
  useEffect(
    function () {
      dispatch(onCardKindRoute(formFor));
    },
    [formFor, dispatch],
  );
  return (
    <div
      className={
        "relative mb-20 flex max-w-full flex-col gap-8 px-4! pt-7! md:mb-0"
      }
    >
      {children}
    </div>
  );
}
