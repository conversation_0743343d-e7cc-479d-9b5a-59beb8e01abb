"use client";

export type ColorKeys =
  | "red"
  | "green"
  | "blue"
  | "yellow"
  | "purple"
  | "black"
  | "blackYellow"
  | "blueBlack"
  | "bluePurple"
  | "blueYellow"
  | "greenBlack"
  | "greenBlue"
  | "greenPurple"
  | "greenYellow"
  | "purpleBlack"
  | "redBlack"
  | "redBlue"
  | "redGreen"
  | "redPurple"
  | "redYellow"
  | "purpleYellow";

export default function ColorOption({ color }: { color: ColorKeys }) {
  const position = findFirstUppercaseIndex(color);
  const secondColor = position !== null ? color.slice(position) : null;

  const firstColor = secondColor
    ? color.split(secondColor)[0][0].toLocaleUpperCase() +
      color.split(secondColor)[0].slice(1)
    : color[0].toLocaleUpperCase() + color.slice(1);
  const colorFinalName = secondColor
    ? firstColor + " " + secondColor
    : firstColor;
  return (
    <option
      value={color}
      // style={{
      //   backgroundColor: colors[bgColor as ColorKeys],
      //   color: color === "yellow" ? "#0c0a09" : "#e7e5e4",
      // }}
      className={"text-stone-950 hover:text-stone-950"}
    >
      {colorFinalName}
    </option>
  );
}
function findFirstUppercaseIndex(str: string) {
  for (let i = 0; i < str.length; i++) {
    if (/[A-Z]/.test(str[i])) {
      return i;
    }
  }
  return null; // No uppercase letter found
}
