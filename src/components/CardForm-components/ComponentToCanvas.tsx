// "use client";
// import React, {
//   CanvasHTMLAttributes,
//   ReactNode,
//   useEffect,
//   useRef,
//   useState,
// } from "react";
// import ReactDOM from "react-dom";
//
// export default function ComponentToCanvas({
//   children,
//   width,
//   height,
// }: {
//   children: ReactNode;
//   width: number;
//   height: number;
// }) {
//   const canvasRef = useRef<HTMLCanvasElement>(null);
//   const containerRef = useRef<HTMLDivElement>(null);
//   const [key, setKey] = useState(0);
//
//   useEffect(() => {
//     const canvas = canvasRef.current;
//     const container = containerRef.current;
//     if (!canvas || !container) return;
//
//     const ctx = canvas.getContext("2d");
//
//     // Get HTML content from the rendered component
//     const data = `
//       <svg xmlns="http://www.w3.org/2000/svg" width="${width}" height="${height}">
//         <foreignObject width="100%" height="100%">
//           <div xmlns="http://www.w3.org/1999/xhtml">
//             ${container.innerHTML}
//           </div>
//         </foreignObject>
//       </svg>
//     `;
//
//     // Create image from SVG
//     const img = new Image();
//     const blob = new Blob([data], { type: "image/svg+xml" });
//     const url = URL.createObjectURL(blob);
//
//     img.onload = () => {
//       // @ts-ignore
//       ctx.clearRect(0, 0, width, height);
//       // @ts-ignore
//       ctx.drawImage(img, 0, 0);
//       URL.revokeObjectURL(url);
//     };
//
//     img.onerror = (err) => {
//       console.error("Error loading image:", err);
//     };
//
//     img.src = url;
//
//     // Set up MutationObserver to watch for changes
//     const observer = new MutationObserver(() => {
//       setKey((prev) => prev + 1);
//     });
//
//     observer.observe(container, {
//       childList: true,
//       subtree: true,
//       attributes: true,
//       characterData: true,
//     });
//
//     return () => {
//       observer.disconnect();
//     };
//   }, [children, width, height, key]);
//
//   return (
//     <div className="relative">
//       <div
//         ref={containerRef}
//         style={{
//           position: "absolute",
//           visibility: "hidden",
//           width: width,
//           height: height,
//           pointerEvents: "none",
//         }}
//       >
//         {children}
//       </div>
//       <canvas
//         ref={canvasRef}
//         width={width}
//         height={height}
//         className="rounded-sm border border-gray-200"
//       />
//     </div>
//   );
// }
//
// // Example usage
// const ExampleUsage = () => {
//   return (
//     <div className="p-4">
//       <h2 className="mb-4 text-xl font-bold">React Component in Canvas</h2>
//       <ComponentToCanvas width={400} height={300}>
//         <div className="rounded-sm bg-blue-100 p-4 shadow-sm">
//           <h3 className="text-lg font-semibold text-blue-800">Hello Canvas!</h3>
//           <p className="mt-2 text-gray-700">
//             This React component is rendered directly to canvas.
//           </p>
//           <button className="mt-4 rounded-sm bg-blue-500 px-4 py-2 text-white">
//             Sample Button
//           </button>
//         </div>
//       </ComponentToCanvas>
//     </div>
//   );
// };
