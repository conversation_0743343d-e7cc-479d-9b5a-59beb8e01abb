"use client";
import { showErrorToast, showInfoToast } from "@/lib/toast";

import React, { useRef, useState, useEffect } from "react";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import { useSelector } from "react-redux";
import { state } from "@/store/formSlice";
import { storeState } from "@/store/store";
import { Subscription } from "@/types";
import { User } from "@supabase/supabase-js";
import { usePostHog } from "posthog-js/react";
import { createClient } from "@/utils/supabase/client";
import axios from "axios";
import { useServerStatus } from "@/context/ServerStatusContext";
import { Button } from "@/components/ui/button";
import CustomLoader from "@/components/CustomLoader";

const supabase = createClient();
type StatusCode = 0 | 401 | 413 | 429 | 500;

type ErrorInfo = {
  title: string;
  message: string | React.ReactNode;
};
export default function DownloadButton({
  cardKind,
  subscription,
  user,
}: {
  cardKind: "character" | "leader" | "event" | "stage" | "don";
  subscription: Subscription;
  user:
    | {
        user: User;
      }
    | {
        user: null;
      };
}) {
  const [uploadProgress, setUploadProgress] = useState(0);
  const [downloadProgress, setDownloadProgress] = useState(0);
  const [generating, setGenerating] = useState(false);
  const [requestFullySent, setRequestFullySent] = useState(false);
  const [queuePosition, setQueuePosition] = useState<number | null>(null);
  // const [jobId, setJobId] = useState<string | null>(null);
  const [apiVersion, setApiVersion] = useState<"backend" | "cloudflare" | null>(
    null,
  );
  const [hasShownCloudflareToast, setHasShownCloudflareToast] = useState(false);

  const abortControllerRef = useRef<AbortController | null>(null);
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const [cardTokens, setCardTokens] = useState(subscription.cardTokens);

  const isCropping = useGetStoreState("isCropping") as boolean;
  const isCroppingBackground = useGetStoreState(
    "isCroppingBackground",
  ) as boolean;
  const storeState = useSelector<storeState>(
    (state) => state.mainFormSlice,
  ) as state;
  const posthog = usePostHog();
  const { setServerConnectionError, setIsDismissed } = useServerStatus();

  // Cleanup intervals on unmount
  useEffect(() => {
    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
        pollingIntervalRef.current = null;
      }
      abortFetchRequest();
    };
  }, []);

  // Show a one-time toast if we detect cloudflare apiVersion
  useEffect(() => {
    if (apiVersion === "cloudflare" && !hasShownCloudflareToast) {
      showInfoToast("Please be patient", {
        description:
          "Using Backup API. Please be patient — this may take a bit longer.",
      });
      setHasShownCloudflareToast(true);
    }
  }, [apiVersion, hasShownCloudflareToast]);

  const abortFetchRequest = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
    }
  };

  // Poll job position
  const pollJobStatus = async (jobId: string) => {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_GET_CARD_API_V2_URL}/api/queue/${jobId}/position`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
          signal: abortControllerRef.current?.signal,
        },
      );

      if (response.ok) {
        const data = await response.json();
        // data should include: jobId, position, status, apiVersion
        if (data.apiVersion && data.apiVersion !== apiVersion) {
          setApiVersion(data.apiVersion);
        }

        setQueuePosition(data.position);

        // If position is -1, job is complete, fetch result
        if (data.position === -1) {
          if (pollingIntervalRef.current) {
            clearInterval(pollingIntervalRef.current);
            pollingIntervalRef.current = null;
          }
          await fetchJobResult(jobId);
        }
      } else {
        throw new Error(`Failed to get job position: ${response.status}`);
      }
    } catch (error: unknown) {
      if (error instanceof Error && error.name !== "AbortError") {
        console.error("Error polling job status:", error);
        if (pollingIntervalRef.current) {
          clearInterval(pollingIntervalRef.current);
          pollingIntervalRef.current = null;
        }
        await handleErrorResponse({
          status: 500,
          data: { error: error.message },
        });
      }
    }
  };

  // Fetch job result with download progress tracking
  const fetchJobResult = async (jobId: string) => {
    try {
      const response = await axios.get(
        `${process.env.NEXT_PUBLIC_GET_CARD_API_V2_URL}/api/queue/${jobId}/result`,
        {
          signal: abortControllerRef.current?.signal,
          responseType: "arraybuffer",
          onDownloadProgress: (progressEvent) => {
            if (progressEvent.total) {
              const percentCompleted = Math.round(
                (progressEvent.loaded * 100) / progressEvent.total,
              );
              setDownloadProgress(percentCompleted);
            }
          },
        },
      );

      // apiVersion can be included in JSON or as X-API-Version header
      const headerApiVersion = response.headers["x-api-version"];
      if (
        headerApiVersion &&
        (headerApiVersion === "backend" || headerApiVersion === "cloudflare") &&
        headerApiVersion !== apiVersion
      ) {
        setApiVersion(headerApiVersion);
      }

      // Set download progress to 100% when download is complete
      setDownloadProgress(100);

      // Create blob from response data
      const blob = new Blob([response.data], { type: "image/png" });
      const url = window.URL.createObjectURL(blob);

      setGenerating(false);
      setUploadProgress(0);
      setDownloadProgress(0);
      setQueuePosition(null);
      // setJobId(null);
      setApiVersion(null);
      setHasShownCloudflareToast(false);

      handleDownloadClick(url);

      posthog.capture("Successfully Generated Image", {
        cardType: cardKind,
        subscription: subscription?.subscriptionName,
        isSubscribed: subscription?.active,
      });
    } catch (error: unknown) {
      if (error instanceof Error && error.name !== "AbortError") {
        console.error("Error fetching job result:", error);
        await handleErrorResponse({
          status: 500,
          data: { error: error.message },
        });
      }
    }
  };

  const handleErrorResponse = async (response: {
    status: number;
    data?: { error: string } | string;
  }) => {
    setGenerating(false);
    setUploadProgress(0);
    setDownloadProgress(0);
    setRequestFullySent(false);
    setQueuePosition(null);
    // setJobId(null);
    setApiVersion(null);
    setHasShownCloudflareToast(false);

    // Stop polling if running
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
    }

    const status = response.status;
    let errorMessage = "Something went wrong please try again.";

    // Extract error message
    try {
      if (response.data) {
        if (typeof response.data === "object" && "error" in response.data) {
          const obj = response.data as { error: string };
          if (obj.error) errorMessage = obj.error;
        } else if (typeof response.data === "string") {
          try {
            const parsedData = JSON.parse(response.data);
            if (parsedData.error) {
              errorMessage = parsedData.error;
            }
          } catch {
            errorMessage = response.data;
          }
        }
      }
    } catch (error: unknown) {
      console.error("Error parsing error response:", error);
    }
    // If 0’s message can be a React element, that’s already covered by ReactNode
    const errorMessages: Record<StatusCode, ErrorInfo> = {
      401: {
        title: "Signed out",
        message: "You need to be signed in to generate the image.",
      },
      429: {
        title: "Limit Reached!",
        message:
          "You reached the limit for the free trial, become a PRO to get unlimited generations.",
      },
      413: {
        title: "Failed generating.",
        message:
          "Uploaded images are too large. The preset needs to be less then 64mb",
      },
      500: {
        title: "Failed generating.",
        message: errorMessage,
      },
      0: {
        title: "Server Offline",
        message: (
          <div>
            <p className={"mb-2"}>
              The server is currently unavailable. You can download the card
              <strong> preset</strong> to save your work and try again later.
            </p>
            <p>
              Please contact <strong><EMAIL></strong> if the
              issue persists, try reloading to see if the server is back online.
            </p>
          </div>
        ),
      },
    };

    // Ensure status is the union (or narrow a number to it)
    function getError(status: number): ErrorInfo {
      const s = (
        Object.keys(errorMessages) as unknown as StatusCode[]
      ).includes(status as StatusCode)
        ? (status as StatusCode)
        : 500;

      return errorMessages[s];
    }

    // Usage
    const errorObj: ErrorInfo = getError(status);
    showErrorToast(errorObj.title, { description: errorObj.message });
  };

  const handleDownload = async () => {
    if (user.user === null) {
      await handleErrorResponse({ status: 401 });
      return;
    }

    try {
      // Get the session
      const {
        data: { session },
      } = await supabase.auth.getSession();
      const accessToken = session?.access_token;

      posthog.identify(user?.user?.id, { email: user?.user?.email });
      setGenerating(true);
      setUploadProgress(0);
      setDownloadProgress(0);
      setRequestFullySent(false);
      setQueuePosition(null);
      // setJobId(null);
      setApiVersion(null);
      setHasShownCloudflareToast(false);

      // Create abort controller
      abortControllerRef.current = new AbortController();
      const signal = abortControllerRef.current.signal;

      const state = {
        ...storeState,
        cardKindRoute: cardKind,
        imageFile: null,
      };

      // Check subscription requirements
      if (
        (state.printReady &&
          !subscription.subscriptionName.includes("Creator") &&
          !(subscription.active && subscription.lifetime)) ||
        (subscription.subscriptionName.includes("Creator") &&
          !subscription.active &&
          !subscription.lifetime)
      ) {
        showErrorToast("Failed generating", {
          description: `You need to subscribe to "Creator" plan to use print ready presets.`,
        });
        setGenerating(false);
        return;
      }

      // Check card tokens
      if (subscription.status !== "active" && !subscription.lifetime) {
        if ((cardTokens && cardTokens < 5) || !cardTokens) {
          showErrorToast("Failed generating", {
            description: `You reached the limit for the free trial, subscribe to get unlimited generations.`,
          });
          setGenerating(false);
          return;
        }
        setCardTokens((token) => Number(token) - 1);
      }

      // Queue the job using axios for upload progress tracking
      const response = await axios.post(
        `${process.env.NEXT_PUBLIC_GET_CARD_API_V2_URL}/api/queue`,
        JSON.stringify(state),
        {
          headers: {
            "Content-Type": "application/json",
            ...(accessToken && { Authorization: `Bearer ${accessToken}` }),
          },
          signal: signal,
          onUploadProgress: (progressEvent) => {
            // Check if the upload is complete
            if (progressEvent.loaded === progressEvent.total) {
              setRequestFullySent(true);
              setServerConnectionError(false);
              setIsDismissed(true);
            }
            // Update upload progress based on percentage
            const percentCompleted = Math.round(
              (progressEvent.loaded * 100) / Number(progressEvent.total),
            );
            setUploadProgress(percentCompleted);
          },
        },
      );

      // Expecting { success, jobId, apiVersion, message }
      const { jobId: newJobId, apiVersion: initialApiVersion } = response.data;
      // setJobId(newJobId);
      if (
        initialApiVersion === "backend" ||
        initialApiVersion === "cloudflare"
      ) {
        setApiVersion(initialApiVersion);
      }

      // Start polling for job status
      pollingIntervalRef.current = setInterval(() => {
        pollJobStatus(newJobId);
      }, 1000);

      // Initial poll
      pollJobStatus(newJobId);
    } catch (error: unknown) {
      const err = error as CustomError;

      if (err.name === "AbortError" || err.code === "ERR_CANCELED") {
        console.log("Download aborted");
        setGenerating(false);
        setUploadProgress(0);
        setDownloadProgress(0);
        setRequestFullySent(false);
        setQueuePosition(null);
        // setJobId(null);
        setApiVersion(null);
        setHasShownCloudflareToast(false);
        abortControllerRef.current = null;
      } else {
        console.log("Download error:", err);

        let status = 500;
        if (
          err.message?.includes("Failed to fetch") ||
          err.message?.includes("Network") ||
          err.code === "ERR_NETWORK"
        ) {
          status = 0;
          setServerConnectionError(true);
        }

        if (err.response) {
          await handleErrorResponse(err.response);
          return;
        }

        await handleErrorResponse({
          status,
          data: { error: err.message || "An unexpected error occurred." },
        });
      }
    }
  };

  const handleDownloadClick = (url: string) => {
    if (url) {
      const cardName = storeState.name.trim() || "unnamed";
      const cardType = storeState.cardType.trim() || "card";
      const cardKindName = cardKind;

      const safeCardName = cardName.replace(/[^a-zA-Z0-9-_]/g, "_");
      const safeCardType = cardType.replace(/[^a-zA-Z0-9-_]/g, "_");
      const filename = `${safeCardName}-${safeCardType}-${cardKindName}.png`;

      const link = document.createElement("a");
      link.href = url;
      link.download = filename;
      link.click();

      window.URL.revokeObjectURL(url);
    }
  };

  const getButtonText = () => {
    if (generating) {
      // If there's a queue and we're waiting
      if (queuePosition !== null && queuePosition > 0) {
        return `Waiting in queue (Position: ${queuePosition})`;
      }

      // If we're actively processing (position 0 or completed queue)
      if (queuePosition === 0 || (requestFullySent && downloadProgress > 0)) {
        if (downloadProgress > 0) {
          return `Downloading ${downloadProgress}%`;
        }
        return (
          <p className={"flex items-center gap-2"}>
            <span>Generating image</span>
            <CustomLoader />
          </p>
        );
      }

      // If we're uploading the job to the queue
      if (uploadProgress > 0 && !requestFullySent) {
        return `Uploading ${uploadProgress}%`;
      }

      if (queuePosition !== -1) {
        // Initial queuing state
        return "Queuing job...";
      }
    }
    return "Generate Image";
  };

  return (
    <>
      {!isCropping && !isCroppingBackground && (
        <div className="relative w-full rounded-sm bg-neutral-600">
          {/* Progress bar overlay */}
          {generating && (
            <div
              className="bg-ring absolute inset-0 z-0 transition-all duration-300 ease-in-out"
              style={{
                width: `${
                  downloadProgress > 0
                    ? 50 + downloadProgress / 2 // 50-100% for download
                    : requestFullySent
                      ? 50 // 50% when job is queued and processing
                      : uploadProgress / 2 // 0-50% for upload
                }%`,
                opacity: 1,
                borderRadius: "4px",
              }}
            />
          )}
          <Button
            className="disabled:bg-ring relative z-10 w-full tracking-wide disabled:text-neutral-50 disabled:opacity-100"
            onClick={(e) => {
              e.preventDefault();
              handleDownload();
              posthog.capture("download_button_clicked", {
                cardType: cardKind,
              });
            }}
            disabled={generating}
            style={{
              fontWeight: 500,
              background: generating ? "transparent" : undefined,
            }}
          >
            {getButtonText()}
          </Button>
        </div>
      )}
    </>
  );
}
interface CustomError extends Error {
  code?: string;
  response?: {
    status: number;
    data?: { error: string };
    // add other properties you expect on response if needed
  };
  status?: number;
}
