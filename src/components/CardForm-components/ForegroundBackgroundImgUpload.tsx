"use client";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, <PERSON><PERSON><PERSON>ontent } from "@/components/ui/tabs";
import FormInput from "@/components/CardForm-components/FormInput";
import { ImageCropper } from "@/components/CardForm-components/ImageCropper";
import React from "react";

export default function ForegroundBackgroundImgUpload({
  cardType,
}: {
  cardType: "character" | "leader" | "event" | "stage" | "don";
}) {
  return (
    <Tabs defaultValue="Foreground" className="w-full">
      <TabsList className="grid w-full grid-cols-2">
        <TabsTrigger value="Foreground">Foreground</TabsTrigger>
        <TabsTrigger value="Background">Background</TabsTrigger>
      </TabsList>
      <TabsContent
        value="Foreground"
        className="relative mt-0.5 flex flex-auto flex-col gap-7"
      >
        <FormInput type={"image-file"} />
        <ImageCropper cardType={cardType} />
      </TabsContent>
      <TabsContent
        value="Background"
        className="relative mt-0.5 flex flex-auto flex-col gap-7"
      >
        <FormInput type={"image-file"} imageType={"background"} />
        <ImageCropper cardType={cardType} imageType={"background"} />
      </TabsContent>
    </Tabs>
  );
}
