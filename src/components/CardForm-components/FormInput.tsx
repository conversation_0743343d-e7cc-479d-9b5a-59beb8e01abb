"use client";
import React from "react";

// Import specialized input components
import ColorInput from "./inputs/ColorInput";
import AttributeInput from "./inputs/AttributeInput";
import NameInput from "./inputs/NameInput";
import CardTypeInput from "./inputs/CardTypeInput";
import CostInput from "./inputs/CostInput";
import CostLeaderInput from "./inputs/CostLeaderInput";
import PowerInput from "./inputs/PowerInput";
import TriggerInput from "./inputs/TriggerInput";
import CounterInput from "./inputs/CounterInput";
import SetInput from "./inputs/SetInput";
import RarityInput from "./inputs/RarityInput";
import CardNumInput from "./inputs/CardNumInput";
import PrintWaveInput from "./inputs/PrintWaveInput";
import ArtistInput from "./inputs/ArtistInput";
import BorderTypeInput from "./inputs/BorderTypeInput";
import One<PERSON>ieceCheckboxInput from "./inputs/OnePieceCheckboxInput";
import ImageFileInput from "./inputs/ImageFileInput";

// Import constants
import { CARD_COLORS } from "./cardColors";
import { CARD_ATTRIBUTES } from "./cardAttributes";
import { LEADER_BORDER_TYPES } from "@/components/CardElement-components/card-elements/leaderBorderTypes";
import { CHARACTER_BORDER_TYPES } from "@/components/CardElement-components/card-elements/characterBorderTypes";

// Import types
import { FormInputType } from "@/types";
import DonTextInput from "@/components/CardForm-components/inputs/DonTextInput";
import DonPowerInput from "@/components/CardForm-components/inputs/DonPowerInput";
import { EVENT_BORDER_TYPES } from "@/components/CardElement-components/card-elements/borderTypes";
interface FormInputProps {
  labelText?: string;
  labelFor?: string;
  type: FormInputType;
  placeholder?: string;
  checked?: boolean;
  className?: string;
  maxNum?: number;
  maxCharNum?: null | number;
  disabled?: boolean;
  cardKind?: "character" | "leader" | "event" | "stage" | "don";
  imageType?: "foreground" | "background";
}

/**
 * FormInput is a router component that renders the appropriate specialized input component
 * based on the type prop.
 */
export default function FormInput({
  type,
  imageType = "foreground",
  cardKind,
}: FormInputProps) {
  // Simple router pattern to render the appropriate component based on type
  switch (type) {
    case "color":
      return (
        <ColorInput cardColors={CARD_COLORS} leader={cardKind === "leader"} />
      );

    case "attribute":
      return <AttributeInput cardAttributes={CARD_ATTRIBUTES} />;

    case "name":
      return <NameInput />;
    case "don-text":
      return <DonTextInput />;

    case "card-type":
      return <CardTypeInput />;

    case "cost":
      return <CostInput />;

    case "costLeader":
      return <CostLeaderInput />;

    case "power":
      return <PowerInput />;
    case "don-power":
      return <DonPowerInput />;

    case "trigger":
      return <TriggerInput />;

    case "counter":
      return <CounterInput />;

    case "set":
      return <SetInput />;

    case "rarity":
    case "rarity2":
      return <RarityInput type={type} />;

    case "card-num":
      return <CardNumInput />;

    case "print-wave":
      return <PrintWaveInput />;

    case "artist":
      return <ArtistInput />;

    case "leader-border-type":
      return (
        <BorderTypeInput
          type="leader-border-type"
          borderTypes={LEADER_BORDER_TYPES}
        />
      );
    case "event-border-type":
      return (
        <BorderTypeInput
          type="event-border-type"
          borderTypes={EVENT_BORDER_TYPES}
        />
      );

    case "character-border-type":
      return (
        <BorderTypeInput
          type="character-border-type"
          borderTypes={CHARACTER_BORDER_TYPES}
        />
      );

    case "powerBlack":
    case "leaderBorderEnabled":
    case "aaStar":
    case "foilBorder":
    case "donAbility":
      return <OnePieceCheckboxInput type={type} cardKind={cardKind} />;

    case "image-file":
      return <ImageFileInput imageType={imageType} />;

    default:
      return null;
  }
}

// Test text input with checkbox
/*
if (type === "trigger") {
  const numbers = ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"];
  return (
      <div>
        <label className={"text-sm"}>Trigger</label>
        <p className={" text-xs text-[#868e96]"}>Card trigger</p>
        <TextInput
            className={"mt-1"}
            placeholder={"Enter text..."}
            onChange={(e) => {
              handleNameChange(dispatch, e);
            }}
        />
      </div>
  );
}*/

/*
"use client";
import React, { useState } from "react";
import {
  ComboboxItem,
  Input,
  InputWrapper,
  NumberInput,
  OptionsFilter,
  Select,
  TextInput,
} from "@mantine/core";
import styles from "./Input.module.css";
import { useDispatch } from "react-redux";
import { onColor } from "@/store/formSlice";
import {
  CARD_COLORS,
  CARD_COLORS_LEADER,
} from "@/components/CardForm-components/cardColors";
import {
  handleAttributeChange,
  handleCardCostChange,
  handleCardCounterChange,
  handleCardPowerChange,
  handleCardTriggerChange,
  handleCardTypeChange,
  handleColorChange,
  handleNameChange,
} from "@/app/helpers/storeUpdaterFunctions";
import { CARD_ATTRIBUTES } from "@/components/CardForm-components/cardAttributes";
import AbilityInput from "@/components/AbilityInput";
import NewAbilityInput from "@/components/CardForm-components/NewAbilityInput";

export default function FormInput({
                                    labelText = "",
                                    labelFor = "",
                                    type,
                                    placeholder = "",
                                    checked = false,
                                    className = "",
                                    maxNum = 99999,
                                    maxCharNum = null,
                                    disabled = false,
                                  }: {
  labelText?: string;
  labelFor?: string;
  type: FormInputType;
  placeholder?: string;
  checked?: boolean;
  className?: string;
  maxNum?: number;
  maxCharNum?: null | number;
  disabled?: boolean;
}) {
  const dispatch = useDispatch();
  const [power, setPower] = useState("");
  const [trigger, setTrigger] = useState("");
  const [counter, setCounter] = useState("");
  const optionsFilter: OptionsFilter = ({ options, search }) => {
    const filtered = (options as ComboboxItem[]).filter((option) =>
        option.label.toLowerCase().trim().includes(search.toLowerCase().trim()),
    );

    filtered.sort((a, b) => a.label.localeCompare(b.label));
    return filtered;
  };
  // const [selectedColor, setSelectedColor] = useState<Color>("red");
  let input;

  if (type === "color") {
    //   input = (
    //     <select
    //       value={selectedColor}
    //       name={"colors"}
    //       id={labelFor}
    //       style={{
    //         background: `linear-gradient(to right, ${bgColor[selectedColor]})`,
    //         color: selectedColor === "yellow" ? "#0c0a09" : "#e7e5e4",
    //       }}
    //       onChange={handleColorChange}
    //     >
    //       {Object.keys(bgColor).map((key) => {
    //         return <ColorOption key={key} color={key as ColorKeys} />;
    //       })}
    //     </select>
    //   );
    return (
        <Select
            label={"Color"}
            description={"Color of the card border"}
            data={CARD_COLORS}
            defaultValue={"red"}
            allowDeselect={false}
            onChange={(color) => handleColorChange(dispatch, color as Color)}
            classNames={{ input: styles.wrapper }}
            filter={optionsFilter}
        />
    );
  }
  if (type === "color-leader") {
    return (
        <Select
            label={"Color"}
            description={"Color of the card border"}
            data={CARD_COLORS_LEADER}
            defaultValue={"red"}
            allowDeselect={false}
            onChange={(color) => handleColorChange(dispatch, color as Color)}
            classNames={{ input: styles.wrapper }}
            searchable={true}
            nothingFoundMessage="No color found..."
            filter={optionsFilter}
        />
    );
  }
  if (type === "attribute") {
    return (
        <Select
            label={"Attribute"}
            description={"Card attribute"}
            data={CARD_ATTRIBUTES}
            defaultValue={"ranged"}
            allowDeselect={false}
            onChange={(attribute) =>
                handleAttributeChange(dispatch, attribute as CardAttribute)
            }
            classNames={{ input: styles.wrapper }}
            searchable
            nothingFoundMessage="No attribute found..."
            filter={optionsFilter}
        />
    );
  }

  if (type === "name") {
    return (
        <TextInput
            placeholder={"Monkey D. Luffy"}
            label={"Name"}
            description={"Card name"}
            onChange={(e) => {
              handleNameChange(dispatch, e);
            }}
        />
    );
  }

  if (type === "card-type") {
    return (
        <TextInput
            placeholder={"Straw Hat Pirates"}
            label={"Type"}
            description={"Card type"}
            onChange={(e) => {
              handleCardTypeChange(dispatch, e);
            }}
        />
    );
  }
  if (type === "cost") {
    const numbers = ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"];
    return (
        <Select
            label={"Cost"}
            description={"Card cost"}
            data={numbers}
            defaultValue={"1"}
            allowDeselect={false}
            onChange={(cost) => handleCardCostChange(dispatch, cost as CardCost)}
            classNames={{ input: styles.wrapper }}
        />
    );
  }
  if (type === "power") {
    return (
        <TextInput
            placeholder={"5000"}
            value={power}
            label={"Power"}
            description={"Card power"}
            onChange={(e) => {
              if (
                  e.target.value.length <= 5 &&
                  Number(e.target.value) >= 0 &&
                  !e.target.value.startsWith("0")
              ) {
                setPower(e.target.value);
                handleCardPowerChange(dispatch, e.target.value);
              }
            }}
        />
    );
  }
  if (type === "trigger") {
    return (
        <div>
          <TextInput
              label={"Trigger"}
              value={trigger}
              description={"Card trigger"}
              placeholder={"Enter text..."}
              onChange={(e) => {
                if (e.target.value.length <= 52) {
                  setTrigger(e.target.value);
                  handleCardTriggerChange(dispatch, e);
                }
              }}
          />
        </div>
    );
  }

  if (type === "counter") {
    return (
        <div>
          <TextInput
              label={"Counter"}
              value={counter}
              description={"Card counter"}
              placeholder={"2000"}
              onChange={(e) => {
                if (
                    e.target.value.length <= 4 &&
                    Number(e.target.value) >= 0 &&
                    !e.target.value.startsWith("0")
                ) {
                  setCounter(e.target.value);
                  handleCardCounterChange(dispatch, e);
                }
              }}
          />
        </div>
    );
  }

  return null;
}*/
