// "use client";
// import React, { useState } from "react";
// import { Button, Checkbox, Text } from "@mantine/core";
// import { put } from "@vercel/blob";
//
// import { useDispatch, useSelector } from "react-redux";
// import {
//   onForegroundImageFileUpload,
//   onImageUrl,
//   onIsCropping,
//   state,
// } from "@/store/formSlice";
// import {
//   CARD_COLORS,
//   CARD_COLORS_LEADER,
// } from "@/components/CardForm-components/cardColors";
// import {
//   handleAttributeChange,
//   handleCardArtistChange,
//   handleCardCostChange,
//   handleCardCounterChange,
//   handleCardNumberChange,
//   handleCardPowerChange,
//   handleCardPrintWaveChange,
//   handleCardRarityChange,
//   handleCardSetChange,
//   handleCardTriggerChange,
//   handleCardTypeChange,
//   handleColorChange,
//   handleFoilBorderChange,
//   handleLeaderBorderEnabledChange,
//   handleLeaderBorderTypeChange,
//   handleNameChange,
//   handlePowerBlackChange,
// } from "@/app/helpers/storeUpdaterFunctions";
// import { CARD_ATTRIBUTES } from "@/components/CardForm-components/cardAttributes";
//
// import FormSelectInput from "@/components/CardForm-components/FormSelectInput";
// import FormTextInput from "@/components/CardForm-components/FormTextInput";
// import { LEADER_BORDER_TYPES } from "@/components/CardElement-components/card-elements/leaderBorderTypes";
// import { useGetStoreState } from "@/helpers/useGetStoreState";
// import { Dropzone } from "@mantine/dropzone";
// import { storeState } from "@/store/store";
// import { Color, FormInputType } from "@/types";
// import { showErrorToast } from "@/lib/toast";
// import Link from "next/link";
// import { upload } from "@vercel/blob/client";
// export default function FormInput({
//   type,
//
//   cardKind,
// }: {
//   labelText?: string;
//   labelFor?: string;
//   type: FormInputType;
//   placeholder?: string;
//   checked?: boolean;
//   className?: string;
//   maxNum?: number;
//   maxCharNum?: null | number;
//   disabled?: boolean;
//   cardKind?: "character" | "leader" | "event" | "stage" | "don";
// }) {
//   const [downloadUrl, setDownloadUrl] = useState<null | string>(null);
//   const [generating, setGenerating] = useState(false);
//   const color = useGetStoreState("color") as Color;
//   const leaderBorder = useGetStoreState("leaderBorder");
//   const attribute = useGetStoreState("attribute") as string;
//   const name = useGetStoreState("name") as string;
//   const cardType = useGetStoreState("cardType") as string;
//   const cost = useGetStoreState("cost") as string;
//   const power = useGetStoreState("power") as string;
//   const triggerText = useGetStoreState("triggerText") as string;
//   const set = useGetStoreState("set") as string;
//   const rarity = useGetStoreState("rarity") as string;
//   const cardNum = useGetStoreState("cardNum") as string;
//   const printWave = useGetStoreState("printWave") as string;
//   const counterText = useGetStoreState("counterText") as string;
//   const artist = useGetStoreState("artist") as string;
//   const powerBlack = useGetStoreState("powerBlack") as boolean;
//   const leaderBorderEnabled = useGetStoreState(
//     "leaderBorderEnabled",
//   ) as boolean;
//   const foilBorder = useGetStoreState("foilBorder") as boolean;
//   const isCropping = useGetStoreState("isCropping") as boolean;
//   const storeState = useSelector<storeState>(
//     (state) => state.mainFormSlice,
//   ) as state;
//
//   const dispatch = useDispatch();
//   if (type === "color") {
//     return (
//       <FormSelectInput
//         label={"Color"}
//         description={"Color of the card border"}
//         data={CARD_COLORS}
//         defaultValue={color}
//         storeUpdaterFunction={handleColorChange}
//         filter={true}
//       />
//     );
//   }
//   if (type === "color-leader") {
//     return (
//       <FormSelectInput
//         label={"Color"}
//         description={"Color of the card border"}
//         data={CARD_COLORS_LEADER}
//         defaultValue={color}
//         storeUpdaterFunction={handleColorChange}
//         searchable={true}
//         nothingFoundMessage="No color found..."
//         filter={true}
//       />
//     );
//   }
//   if (type === "attribute") {
//     return (
//       <FormSelectInput
//         label={"Attribute"}
//         description={"Card attribute"}
//         data={CARD_ATTRIBUTES}
//         defaultValue={attribute}
//         storeUpdaterFunction={handleAttributeChange}
//         nothingFoundMessage="No attribute found..."
//         filter={true}
//       />
//     );
//   }
//
//   if (type === "name") {
//     return (
//       <FormTextInput
//         placeholder={"Monkey D. Luffy"}
//         label={"Name"}
//         maxCharLength={22}
//         description={"Card name"}
//         storeUpdaterFunction={handleNameChange}
//         value={name}
//       />
//     );
//   }
//
//   if (type === "card-type") {
//     return (
//       <FormTextInput
//         placeholder={"Straw Hat Pirates"}
//         maxCharLength={30}
//         label={"Type"}
//         description={"Card type"}
//         storeUpdaterFunction={handleCardTypeChange}
//         value={cardType}
//       />
//     );
//   }
//   if (type === "cost") {
//     const numbers = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10"];
//     return (
//       <FormSelectInput
//         label={"Cost"}
//         description={"Card cost"}
//         data={numbers}
//         defaultValue={cost}
//         storeUpdaterFunction={handleCardCostChange}
//         className={""}
//       />
//     );
//   }
//   if (type === "power") {
//     return (
//       <FormTextInput
//         placeholder={"5000"}
//         maxCharLength={5}
//         label={"Power"}
//         description={"Card power"}
//         storeUpdaterFunction={handleCardPowerChange}
//         typeNumber={true}
//         value={power}
//       />
//     );
//   }
//   if (type === "trigger") {
//     return (
//       <FormTextInput
//         placeholder={"Enter text..."}
//         maxCharLength={9999}
//         label={"Trigger"}
//         description={"Card trigger"}
//         storeUpdaterFunction={handleCardTriggerChange}
//         value={triggerText}
//       />
//     );
//   }
//
//   if (type === "counter") {
//     return (
//       <FormTextInput
//         placeholder={"2000"}
//         maxCharLength={4}
//         label={"Counter"}
//         description={"Card counter"}
//         storeUpdaterFunction={handleCardCounterChange}
//         typeNumber={true}
//         value={counterText}
//       />
//     );
//   }
//   if (type === "set") {
//     return (
//       <FormTextInput
//         placeholder={"OP03"}
//         maxCharLength={4}
//         label={"Set"}
//         description={"Card set"}
//         storeUpdaterFunction={handleCardSetChange}
//         value={set}
//       />
//     );
//   }
//   if (type === "rarity") {
//     return (
//       <FormTextInput
//         placeholder={"SEC"}
//         maxCharLength={3}
//         label={"Rarity"}
//         description={"Card rarity"}
//         storeUpdaterFunction={handleCardRarityChange}
//         value={rarity}
//       />
//     );
//   }
//   if (type === "card-num") {
//     return (
//       <FormTextInput
//         placeholder={"120"}
//         maxCharLength={3}
//         label={"Card Number"}
//         storeUpdaterFunction={handleCardNumberChange}
//         typeNumber={true}
//         doesntStartWith0={true}
//         value={cardNum}
//       />
//     );
//   }
//
//   if (type === "print-wave") {
//     return (
//       <FormTextInput
//         placeholder={"1"}
//         maxCharLength={2}
//         label={"Print Wave"}
//         storeUpdaterFunction={handleCardPrintWaveChange}
//         typeNumber={true}
//         value={printWave}
//       />
//     );
//   }
//   if (type === "artist") {
//     return (
//       <FormTextInput
//         placeholder={"Artist name"}
//         maxCharLength={60}
//         label={"Artist"}
//         storeUpdaterFunction={handleCardArtistChange}
//         value={artist}
//       />
//     );
//   }
//   if (type === "leader-border-type") {
//     return (
//       <FormSelectInput
//         label={"Border Type"}
//         description={"Card border type"}
//         data={LEADER_BORDER_TYPES}
//         defaultValue={leaderBorder}
//         storeUpdaterFunction={handleLeaderBorderTypeChange}
//         nothingFoundMessage="No attribute found..."
//       />
//     );
//   }
//   if (type === "powerBlack") {
//     return (
//       <Checkbox
//         label={"Power white outline"}
//         checked={powerBlack}
//         onChange={(e) => handlePowerBlackChange(dispatch, e)}
//       />
//     );
//   }
//   if (type === "leaderBorderEnabled") {
//     return (
//       <Checkbox
//         label={"Card Border"}
//         checked={leaderBorderEnabled}
//         onChange={(e) => handleLeaderBorderEnabledChange(dispatch, e)}
//       />
//     );
//   }
//   if (type === "foilBorder") {
//     return (
//       <Checkbox
//         label={"Foil border"}
//         checked={foilBorder}
//         onChange={(e) => handleFoilBorderChange(dispatch, e)}
//       />
//     );
//   }
//   if (type === "image-file") {
//     return (
//       <>
//         {/*<FileInput
//           value={imageFile}
//           label={"Upload image"}
//           placeholder={"Click here to upload"}
//           onChange={(file) => {
//             dispatch(onForegroundImageFileUpload(file));
//             const reader = new FileReader();
//             console.log(file, reader);
//             reader.onload = (e) => {
//               if (e?.target?.result) {
//                 dispatch(onImageUrl(e.target.result as string));
//               }
//             };
//             if (file) {
//               reader.readAsDataURL(file);
//             }
//           }}
//           accept={"imageFile"}
//         />*/}
//         <Dropzone
//           onDrop={(file) => {
//             dispatch(onIsCropping(false));
//
//             dispatch(onForegroundImageFileUpload(file[0]));
//             const reader = new FileReader();
//
//             reader.onload = (e) => {
//               if (e?.target?.result) {
//                 dispatch(onImageUrl(e.target.result as string));
//               }
//             };
//             if (file[0]) {
//               reader.readAsDataURL(file[0]);
//             }
//           }}
//           className={
//             "relative  min-h-[120px] min-w-[207px] border-2 border-solid border-neutral-200 bg-white text-neutral-400 hover:cursor-pointer dark:border-[rgb(55,58,64)] dark:bg-[#25262b]"
//           }
//           accept={["image/png", "image/jpeg", "image/webp"]}
//           maxFiles={1}
//         >
//           <Text ta="center" className={"relative mx-auto my-[26%]"}>
//             Click here or drop images
//           </Text>
//         </Dropzone>
//       </>
//     );
//   }
//   if (type === "download") {
//     const handleDownload = async () => {
//       setDownloadUrl(null);
//       setGenerating(true);
//       let fileToUpload = new FormData();
//       fileToUpload.append("state", storeState.imageFile!);
//       const state = { ...storeState, cardKindRoute: cardKind, imageFile: null };
//
//       const res = await fetch("/api/get-card", {
//         cache: "no-store",
//         method: "POST",
//         headers: {
//           "Content-Type": "application/json", // Set the Content-Type header to application/json
//         },
//         body: JSON.stringify(state),
//       });
//
//       if (res.status === 200) {
//         // Successful response, assume it's the image data
//         const blob = await res.blob();
//
//         // Create a downloadable URL for the blob
//         const url = window.URL.createObjectURL(blob);
//         setDownloadUrl(url);
//         setGenerating(false);
//       } else if (res.status === 401) {
//         setGenerating(false);
//         showErrorToast("Signed out", {
//           description: "You need to be signed in to generate the image."
//         });
//
//         // Handle error responses here
//       } else if (res.status === 429) {
//         setGenerating(false);
//         showErrorToast("Limit Reached!", {
//           description: (
//             <Link href={"/account"}>
//               You reached the limit for this week, become a PRO to get unlimited
//               generations.
//             </Link>
//           )
//         });
//       } else {
//         setGenerating(false);
//         showErrorToast("Failed generating.", {
//           description: "Something went wrong please try again."
//         });
//       }
//     };
//
//     const handleDownloadClick = () => {
//       if (downloadUrl) {
//         setDownloadUrl(null);
//         // Create an invisible link element
//         const link = document.createElement("a");
//         link.href = downloadUrl;
//         link.download = "downloaded-image.png"; // You can set the desired filename
//         link.click();
//
//         // Revoke the URL to free up resources
//         window.URL.revokeObjectURL(downloadUrl);
//       }
//     };
//
//     return (
//       <>
//         {!isCropping && (
//           <>
//             <Button
//               variant={"filled"}
//               className={"tracking-wide"}
//               fullWidth
//               onClick={handleDownload}
//               loading={generating}
//               style={{ fontWeight: 500 }}
//             >
//               {generating ? "Generating..." : "Generate Image"}
//             </Button>
//           </>
//         )}
//         {downloadUrl && (
//           <Button
//             variant={"filled"}
//             className={" font-normal tracking-wide "}
//             fullWidth
//             onClick={handleDownloadClick}
//             color={"green"}
//           >
//             Download
//           </Button>
//         )}
//       </>
//     );
//   }
//
//   return null;
// }
//
// // Test text input with checkbox
// /*
// if (type === "trigger") {
//   const numbers = ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"];
//   return (
//       <div>
//         <label className={"text-sm"}>Trigger</label>
//         <p className={" text-xs text-[#868e96]"}>Card trigger</p>
//         <TextInput
//             className={"mt-1"}
//             placeholder={"Enter text..."}
//             onChange={(e) => {
//               handleNameChange(dispatch, e);
//             }}
//         />
//       </div>
//   );
// }*/
//
// /*
// "use client";
// import React, { useState } from "react";
// import {
//   ComboboxItem,
//   Input,
//   InputWrapper,
//   NumberInput,
//   OptionsFilter,
//   Select,
//   TextInput,
// } from "@mantine/core";
// import styles from "./Input.module.css";
// import { useDispatch } from "react-redux";
// import { onColor } from "@/store/formSlice";
// import {
//   CARD_COLORS,
//   CARD_COLORS_LEADER,
// } from "@/components/CardForm-components/cardColors";
// import {
//   handleAttributeChange,
//   handleCardCostChange,
//   handleCardCounterChange,
//   handleCardPowerChange,
//   handleCardTriggerChange,
//   handleCardTypeChange,
//   handleColorChange,
//   handleNameChange,
// } from "@/app/helpers/storeUpdaterFunctions";
// import { CARD_ATTRIBUTES } from "@/components/CardForm-components/cardAttributes";
// import AbilityInput from "@/components/AbilityInput";
// import NewAbilityInput from "@/components/CardForm-components/NewAbilityInput";
//
// export default function FormInput({
//                                     labelText = "",
//                                     labelFor = "",
//                                     type,
//                                     placeholder = "",
//                                     checked = false,
//                                     className = "",
//                                     maxNum = 99999,
//                                     maxCharNum = null,
//                                     disabled = false,
//                                   }: {
//   labelText?: string;
//   labelFor?: string;
//   type: FormInputType;
//   placeholder?: string;
//   checked?: boolean;
//   className?: string;
//   maxNum?: number;
//   maxCharNum?: null | number;
//   disabled?: boolean;
// }) {
//   const dispatch = useDispatch();
//   const [power, setPower] = useState("");
//   const [trigger, setTrigger] = useState("");
//   const [counter, setCounter] = useState("");
//   const optionsFilter: OptionsFilter = ({ options, search }) => {
//     const filtered = (options as ComboboxItem[]).filter((option) =>
//         option.label.toLowerCase().trim().includes(search.toLowerCase().trim()),
//     );
//
//     filtered.sort((a, b) => a.label.localeCompare(b.label));
//     return filtered;
//   };
//   // const [selectedColor, setSelectedColor] = useState<Color>("red");
//   let input;
//
//   if (type === "color") {
//     //   input = (
//     //     <select
//     //       value={selectedColor}
//     //       name={"colors"}
//     //       id={labelFor}
//     //       style={{
//     //         background: `linear-gradient(to right, ${bgColor[selectedColor]})`,
//     //         color: selectedColor === "yellow" ? "#0c0a09" : "#e7e5e4",
//     //       }}
//     //       onChange={handleColorChange}
//     //     >
//     //       {Object.keys(bgColor).map((key) => {
//     //         return <ColorOption key={key} color={key as ColorKeys} />;
//     //       })}
//     //     </select>
//     //   );
//     return (
//         <Select
//             label={"Color"}
//             description={"Color of the card border"}
//             data={CARD_COLORS}
//             defaultValue={"red"}
//             allowDeselect={false}
//             onChange={(color) => handleColorChange(dispatch, color as Color)}
//             classNames={{ input: styles.wrapper }}
//             filter={optionsFilter}
//         />
//     );
//   }
//   if (type === "color-leader") {
//     return (
//         <Select
//             label={"Color"}
//             description={"Color of the card border"}
//             data={CARD_COLORS_LEADER}
//             defaultValue={"red"}
//             allowDeselect={false}
//             onChange={(color) => handleColorChange(dispatch, color as Color)}
//             classNames={{ input: styles.wrapper }}
//             searchable={true}
//             nothingFoundMessage="No color found..."
//             filter={optionsFilter}
//         />
//     );
//   }
//   if (type === "attribute") {
//     return (
//         <Select
//             label={"Attribute"}
//             description={"Card attribute"}
//             data={CARD_ATTRIBUTES}
//             defaultValue={"ranged"}
//             allowDeselect={false}
//             onChange={(attribute) =>
//                 handleAttributeChange(dispatch, attribute as CardAttribute)
//             }
//             classNames={{ input: styles.wrapper }}
//             searchable
//             nothingFoundMessage="No attribute found..."
//             filter={optionsFilter}
//         />
//     );
//   }
//
//   if (type === "name") {
//     return (
//         <TextInput
//             placeholder={"Monkey D. Luffy"}
//             label={"Name"}
//             description={"Card name"}
//             onChange={(e) => {
//               handleNameChange(dispatch, e);
//             }}
//         />
//     );
//   }
//
//   if (type === "card-type") {
//     return (
//         <TextInput
//             placeholder={"Straw Hat Pirates"}
//             label={"Type"}
//             description={"Card type"}
//             onChange={(e) => {
//               handleCardTypeChange(dispatch, e);
//             }}
//         />
//     );
//   }
//   if (type === "cost") {
//     const numbers = ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"];
//     return (
//         <Select
//             label={"Cost"}
//             description={"Card cost"}
//             data={numbers}
//             defaultValue={"1"}
//             allowDeselect={false}
//             onChange={(cost) => handleCardCostChange(dispatch, cost as CardCost)}
//             classNames={{ input: styles.wrapper }}
//         />
//     );
//   }
//   if (type === "power") {
//     return (
//         <TextInput
//             placeholder={"5000"}
//             value={power}
//             label={"Power"}
//             description={"Card power"}
//             onChange={(e) => {
//               if (
//                   e.target.value.length <= 5 &&
//                   Number(e.target.value) >= 0 &&
//                   !e.target.value.startsWith("0")
//               ) {
//                 setPower(e.target.value);
//                 handleCardPowerChange(dispatch, e.target.value);
//               }
//             }}
//         />
//     );
//   }
//   if (type === "trigger") {
//     return (
//         <div>
//           <TextInput
//               label={"Trigger"}
//               value={trigger}
//               description={"Card trigger"}
//               placeholder={"Enter text..."}
//               onChange={(e) => {
//                 if (e.target.value.length <= 52) {
//                   setTrigger(e.target.value);
//                   handleCardTriggerChange(dispatch, e);
//                 }
//               }}
//           />
//         </div>
//     );
//   }
//
//   if (type === "counter") {
//     return (
//         <div>
//           <TextInput
//               label={"Counter"}
//               value={counter}
//               description={"Card counter"}
//               placeholder={"2000"}
//               onChange={(e) => {
//                 if (
//                     e.target.value.length <= 4 &&
//                     Number(e.target.value) >= 0 &&
//                     !e.target.value.startsWith("0")
//                 ) {
//                   setCounter(e.target.value);
//                   handleCardCounterChange(dispatch, e);
//                 }
//               }}
//           />
//         </div>
//     );
//   }
//
//   return null;
// }*/
