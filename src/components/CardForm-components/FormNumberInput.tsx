"use client";
import { NumberInput } from "@mantine/core";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import { useDispatch } from "react-redux";
import { onNameFontSize, onTypeFontSize } from "@/store/formSlice";

export default function FormNumberInput({
  inputFor,
}: {
  inputFor: "typeFontSize" | "nameFontSize";
}) {
  const typeFontSize = useGetStoreState(inputFor) as number;
  const dispatch = useDispatch();
  return (
    <NumberInput
      value={typeFontSize}
      onChange={function (e) {
        if (inputFor === "typeFontSize") dispatch(onTypeFontSize(e));
        if (inputFor === "nameFontSize") dispatch(onNameFontSize(e));
      }}
      className={"max-w-[5rem] self-end"}
    />
  );
}
