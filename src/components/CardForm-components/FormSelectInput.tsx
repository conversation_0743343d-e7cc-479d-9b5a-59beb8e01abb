// "use client";
//
// import { ComboboxItem, OptionsFilter, Select } from "@mantine/core";
// import { useDispatch } from "react-redux";
//
// import styles from "@/components/CardForm-components/Input.module.css";
//
// export default function FormSelectInput({
//   description = "",
//   label = "",
//   storeUpdaterFunction,
//   searchable = false,
//   nothingFoundMessage = "",
//   filter = false,
//   data,
//   allowDeselect = false,
//   defaultValue = "",
//   className = "",
// }: {
//   label?: string;
//   description?: string;
//   storeUpdaterFunction: any;
//   searchable?: boolean;
//   nothingFoundMessage?: string;
//   filter?: boolean;
//   data: { value: string; label: string }[] | string[];
//   allowDeselect?: boolean;
//   defaultValue?: string;
//   className?: string;
// }) {
//   const dispatch = useDispatch();
//   const optionsFilter: OptionsFilter = ({ options, search }) => {
//     const filtered = (options as ComboboxItem[]).filter((option) =>
//       option.label.toLowerCase().trim().includes(search.toLowerCase().trim()),
//     );
//
//     filtered.sort((a, b) => a.label.localeCompare(b.label));
//     return filtered;
//   };
//   if (filter) {
//     return (
//       <Select
//         label={label}
//         description={description}
//         data={data}
//         defaultValue={defaultValue}
//         value={defaultValue}
//         allowDeselect={allowDeselect}
//         onChange={(state) => storeUpdaterFunction(dispatch, state)}
//         classNames={{
//           input: `${styles.wrapper}`,
//           root: `${className}`,
//         }}
//         searchable={searchable}
//         nothingFoundMessage={nothingFoundMessage}
//         filter={optionsFilter}
//       />
//     );
//   } else {
//     return (
//       <Select
//         label={label}
//         description={description}
//         data={data}
//         defaultValue={defaultValue}
//         value={defaultValue}
//         allowDeselect={allowDeselect}
//         onChange={(state) => storeUpdaterFunction(dispatch, state)}
//         classNames={{
//           input: `${styles.wrapper}`,
//           root: `${className}`,
//         }}
//         searchable={searchable}
//         nothingFoundMessage={nothingFoundMessage}
//       />
//     );
//   }
// }
