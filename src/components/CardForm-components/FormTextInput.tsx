// "use client";
//
// import { TextInput } from "@mantine/core";
// import React from "react";
// import { useDispatch } from "react-redux";
//
// export default function FormTextInput({
//   description = "",
//   label = "",
//   maxCharLength = 0,
//   placeholder = "",
//   storeUpdaterFunction,
//   typeNumber = false,
//   doesntStartWith0 = false,
//   className = "",
//   value,
// }: {
//   maxCharLength?: number;
//   label?: string;
//   description?: string;
//   placeholder?: string;
//   storeUpdaterFunction: any;
//   typeNumber?: boolean;
//   doesntStartWith0?: boolean;
//   className?: string;
//   value: any;
// }) {
//   const dispatch = useDispatch();
//
//   if (typeNumber) {
//     return (
//       <>
//         <TextInput
//           label={label}
//           value={value}
//           description={description}
//           placeholder={placeholder}
//           className={className}
//           onChange={(e) => {
//             const maxLength = maxCharLength ? maxCharLength : 999999;
//             if (!doesntStartWith0) {
//               if (
//                 !e.target.value.includes(" ") &&
//                 e.target.value.length <= maxLength &&
//                 Number(e.target.value) >= 0 &&
//                 !e.target.value.startsWith("0")
//               ) {
//                 storeUpdaterFunction(dispatch, e);
//               }
//             } else if (e.target.value.length <= maxLength) {
//               storeUpdaterFunction(dispatch, e);
//             }
//           }}
//         />
//       </>
//     );
//   }
//   if (!maxCharLength) {
//     return (
//       <TextInput
//         label={label}
//         value={value}
//         description={description}
//         placeholder={placeholder}
//         className={className}
//         onChange={(e) => {
//           storeUpdaterFunction(dispatch, e);
//         }}
//       />
//     );
//   }
//   if (maxCharLength) {
//     return (
//       <TextInput
//         label={label}
//         value={value}
//         description={description}
//         placeholder={placeholder}
//         className={className}
//         onChange={(e) => {
//           if (e.target.value.length <= maxCharLength) {
//             storeUpdaterFunction(dispatch, e);
//           }
//         }}
//       />
//     );
//   }
// }
