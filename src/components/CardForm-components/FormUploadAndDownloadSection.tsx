import ButtonDownloadCurrentStateAsJson from "@/components/CardForm-components/ButtonDownloadCurrentStateAsJSON";
import ButtonUploadState from "@/components/CardForm-components/ButtonUploadState";
import React, { Suspense } from "react";

import { Subscription } from "@/types";
import { Skeleton } from "@/components/ui/skeleton";

export default function FormUploadAndDownloadSection({
  uploadFor,
  subscription,
}: {
  uploadFor: "character" | "leader" | "event" | "stage" | "don";
  subscription: Subscription;
}) {
  return (
    <div
      className={
        "flex min-h-[93px] flex-wrap items-center gap-4 sm:min-h-[41px]"
      }
    >
      <ButtonDownloadCurrentStateAsJson downloadFor={uploadFor} />

      <Suspense
        fallback={
          <Skeleton
            className={`mt-0! h-9 min-w-[189.23px]! sm:max-w-[189.23px]!`}
          />
        }
      >
        <AsyncButtonUploadState
          uploadFor={uploadFor}
          subscription={subscription}
        />
      </Suspense>
    </div>
  );
}

async function AsyncButtonUploadState({
  uploadFor,
  subscription,
}: {
  uploadFor: "character" | "leader" | "event" | "stage" | "don";
  subscription: Subscription;
}) {
  // const subscription = await getSubscriptionNameAndStatus();
  return (
    <ButtonUploadState uploadFor={uploadFor} subscription={subscription} />
  );
}
