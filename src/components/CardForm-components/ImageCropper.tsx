"use client";
import React, { useState } from "react";
import {
  useGetCropperState,
  useGetStoreState,
} from "@/helpers/useGetStoreState";
import { useDispatch } from "react-redux";
import { onIsCropping, onIsCroppingBackground } from "@/store/formSlice";
import CardBorder from "@/components/CardElement-components/card-elements/CardBorder";
import CardCost from "@/components/CardElement-components/card-elements/CardCost";
import CardColorWheel from "@/components/CardElement-components/card-elements/CardColorWheel";
import { Button } from "@/components/ui/button";
import OnePieceReactSvgWithGradient from "@/components/OnePieceReactSvgWithGradient";
import TcgCropper from "@/components/CardForm-components/TCGCropper";
import { onFileUrl } from "@/store/cropperSlice";
const SPL2 = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Character-SP-V2-L2.svg`;
const printReadySPL2 = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Print-Ready-Character-SP-V2-L2.svg`;
const innerBorderBottom = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Inner-Border-Bottom.svg`;
const printReadyInnerBorderBottom = `${process.env.NEXT_PUBLIC_ASSETS_URL}/svg/Border-Assets/Print-Ready-Inner-Border-Bottom.svg`;
export function ImageCropper({
  cardType,
  imageType = "foreground",
}: {
  cardType: "character" | "leader" | "event" | "stage" | "don";
  imageType?: "foreground" | "background";
}) {
  const printReady = useGetStoreState("printReady");
  const fileUrl = useGetCropperState("fileUrl");
  const characterBorder = useGetStoreState("characterBorder");
  const dispatch = useDispatch();
  const imageFileStateKey =
    imageType === "foreground" ? "imageFile" : "backgroundImageFile";
  const isCroppingStateKey =
    imageType === "foreground" ? "isCropping" : "isCroppingBackground";

  const [fileType, setFileType] = useState<string | null>(null); // State to hold the Data URL

  const isCropping = useGetStoreState(isCroppingStateKey);
  const imageFile = useGetStoreState(imageFileStateKey) as File | null;

  const startCrop = () => {
    loadImage(imageFile!);
    if (isCroppingStateKey === "isCropping") {
      dispatch(onIsCropping(true));
    }
    if (isCroppingStateKey === "isCroppingBackground") {
      dispatch(onIsCroppingBackground(true));
    }
  };

  const loadImage = (file: File) => {
    setFileType(file.type);
    const reader = new FileReader();
    reader.onload = (e) => {
      if (e?.target?.result !== fileUrl) {
        dispatch(onFileUrl(e?.target?.result as string));
      }
    };
    reader.readAsDataURL(file);
  };
  return (
    <>
      {fileUrl && (
        <>
          <TcgCropper
            fileUrl={fileUrl}
            imageType={imageType}
            fileType={fileType}
            isCroppingStateKey={isCroppingStateKey}
            isCropping={isCropping}
          >
            <CardBorder
              cardType={cardType}
              quality={1}
              className={` ${(characterBorder === "none" || characterBorder === "sp-v2") && cardType === "character" ? "opacity-0" : "opacity-50"}`}
            />
            {cardType === "leader" && (
              <OnePieceReactSvgWithGradient
                printReady={printReady}
                printReadySrc={printReadySPL2}
                standardSrc={SPL2}
                className={`absolute top-0 z-4 opacity-0`}
              />
            )}
            <CardCost
              character={cardType !== "leader" && cardType !== "don"}
              leader={cardType === "leader" || cardType === "don"}
              cropper={true}
              event={cardType === "event"}
            />
            <CardColorWheel cardKind={cardType} />
            <OnePieceReactSvgWithGradient
              printReady={printReady}
              printReadySrc={printReadyInnerBorderBottom}
              standardSrc={innerBorderBottom}
              className={`relative top-0 z-4 opacity-0`}
            />
          </TcgCropper>
        </>
      )}
      {imageFile && !isCropping && (
        <>
          <Button
            onClick={startCrop}
            className={
              "bg-full-button hover:bg-full-button-hover dark:bg-full-button-dark dark:hover:bg-full-button-hover-dark font-medium! tracking-wide active:translate-y-0.5"
            }
          >
            Crop the image
          </Button>
        </>
      )}
    </>
  );
}
