import React, { Suspense } from "react";

import PrintReadyCheckbox from "@/components/CardForm-components/PrintReadyCheckbox";
import { Subscription } from "@/types";
import { Skeleton } from "@/components/ui/skeleton";

export default function PrintReadyInput({
  subscription,
}: {
  subscription: Subscription;
}) {
  return (
    <>
      <Suspense
        fallback={
          <Skeleton
            className={`mt-0! h-5 min-w-[107.52px]! sm:max-w-[107.52px]!`}
          />
        }
      >
        <AsyncPrintReadyCheckbox subscription={subscription} />
      </Suspense>
    </>
  );
}

async function AsyncPrintReadyCheckbox({
  subscription,
}: {
  subscription: Subscription;
}) {
  // const subscription = await getSubscriptionNameAndStatus();

  if (
    subscription &&
    ((subscription.active &&
      subscription.subscriptionName.includes("Creator")) ||
      subscription.lifetime)
  ) {
    return <PrintReadyCheckbox />;
  }
  return null;
}
