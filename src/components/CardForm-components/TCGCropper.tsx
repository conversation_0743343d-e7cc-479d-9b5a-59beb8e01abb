"use client";
import <PERSON><PERSON><PERSON>, { Area } from "react-easy-crop";
import React, { useEffect, useRef, useState } from "react";
import { Button } from "@/components/ui/button";
import {
  onBackgroundImageUrl,
  onImageUrl,
  onIsCropping,
  onIsCroppingBackground,
} from "@/store/formSlice";
import { useDispatch } from "react-redux";
import {
  useGetCropperState,
  useGetStoreState,
} from "@/helpers/useGetStoreState";
import { onCrop, onZoom } from "@/store/cropperSlice";

export default function TcgCropper({
  fileUrl,
  fileType,
  imageType,
  isCroppingStateKey,
  children,
  isCropping,
}: {
  fileUrl: string;
  fileType: string | null;
  isCropping: boolean;
  imageType: "foreground" | "background";
  isCroppingStateKey: "isCropping" | "isCroppingBackground";
  children?: React.ReactNode;
}) {
  const crop = useGetCropperState("crop");
  const zoom = useGetCropperState("zoom");

  const [croppedArea, setCroppedArea] = useState<Area | null>(null); // State to hold cropped File
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<Area | null>(null); // State to hold cropped File
  const canvas = useRef<HTMLCanvasElement | null>(null);
  const printReady = useGetStoreState("printReady");
  const dispatch = useDispatch();

  const onCropComplete = () => {
    if (croppedArea && croppedAreaPixels && fileUrl) {
      // Perform cropping and set cropped data in the state

      const image = new Image();
      image.src = fileUrl;
      image.onload = () => {
        if (canvas.current) {
          canvas.current.width = croppedAreaPixels.width;
          canvas.current.height = croppedAreaPixels.height;
          const ctx = canvas.current.getContext("2d");
          ctx?.drawImage(
            image,
            croppedAreaPixels.x,
            croppedAreaPixels.y,
            croppedAreaPixels.width,
            croppedAreaPixels.height,
            0,
            0,
            croppedAreaPixels.width,
            croppedAreaPixels.height,
          );
          const croppedDataURL = canvas.current.toDataURL(
            fileType ? fileType : "image/png",
          );

          if (imageType === "foreground") {
            dispatch(onImageUrl(croppedDataURL));
          }
          if (imageType === "background") {
            dispatch(onBackgroundImageUrl(croppedDataURL));
          }
        }
      };
      if (isCroppingStateKey === "isCropping") {
        dispatch(onIsCropping(false));
      }
      if (isCroppingStateKey === "isCroppingBackground") {
        dispatch(onIsCroppingBackground(false));
      }
    }
  };
  function handleCropComplete(croppedArea: Area, croppedAreaPixels: Area) {
    setCroppedArea(croppedArea);
    setCroppedAreaPixels(croppedAreaPixels);
  }

  useEffect(() => {
    canvas.current = document.createElement("canvas");
  }, []);

  if (!isCropping) {
    return null;
  }

  return (
    <>
      <Button
        onClick={onCropComplete}
        className={
          "bg-full-button hover:bg-full-button-hover dark:bg-full-button-dark dark:hover:bg-full-button-hover-dark font-medium! tracking-wide active:translate-y-0.5"
        }
        color={"green"}
      >
        Complete Cropping
      </Button>
      <div className={"relative overflow-clip rounded-xl sm:max-w-[19rem]"}>
        <Cropper
          image={fileUrl}
          crop={crop}
          zoom={zoom}
          minZoom={0.1}
          maxZoom={10}
          aspect={printReady ? 3677 / 5011 : 3357 / 4692}
          onCropChange={(point) => {
            dispatch(onCrop(point));
          }}
          onCropComplete={handleCropComplete}
          restrictPosition={false}
          zoomSpeed={0.1}
          onZoomChange={(zoom) => {
            dispatch(onZoom(zoom));
          }}
          objectFit={"cover"}
          style={{
            cropAreaStyle: {
              zIndex: "10",
            },
            containerStyle: {
              background: "rgba(0,0,0,0.1)",
            },
          }}
        />
        {children}
      </div>
    </>
  );
}
