"use client";

import React from "react";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import { handleCardArtistChange } from "@/app/helpers/storeUpdaterFunctions";
import TextInput from "./TextInput";

const ArtistInput = () => {
  const artist = useGetStoreState("artist") as string;

  return (
    <TextInput
      label="Artist"
      placeholder="Artist name"
      value={artist}
      storeUpdaterFunction={handleCardArtistChange}
      maxCharLength={60}
      className="w-full"
    />
  );
};

export default ArtistInput;
