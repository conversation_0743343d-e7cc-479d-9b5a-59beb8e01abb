"use client";

import React from "react";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import { handleAttributeChange } from "@/app/helpers/storeUpdaterFunctions";
import SelectInput from "./SelectInput";
import { CardAttribute } from "@/types";

interface AttributeInputProps {
  cardAttributes: { value: CardAttribute; label: string }[];
}

const AttributeInput = ({ cardAttributes }: AttributeInputProps) => {
  const attribute = useGetStoreState("attribute") as CardAttribute;

  return (
    <SelectInput<CardAttribute>
      label="Attribute"
      description="Card attribute"
      data={cardAttributes}
      value={attribute}
      storeUpdaterFunction={handleAttributeChange}
      className="w-full"
    />
  );
};

export default AttributeInput;
