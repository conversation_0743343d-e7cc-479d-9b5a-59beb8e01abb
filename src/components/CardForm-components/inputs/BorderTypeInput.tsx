"use client";

import React from "react";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import {
  handleLeaderBorderTypeChange,
  handleCharacterBorderTypeChange,
  handleEventBorderTypeChange,
} from "@/app/helpers/storeUpdaterFunctions";
import SelectInput from "./SelectInput";
import { CharacterBorder, LeaderBorder, EventBorder } from "@/types";

interface BorderTypeInputProps {
  type: "leader-border-type" | "character-border-type" | "event-border-type";
  borderTypes:
    | { value: LeaderBorder; label: string }[]
    | { value: CharacterBorder; label: string }[]
    | { value: EventBorder; label: string }[];
}

const BorderTypeInput = ({ type, borderTypes }: BorderTypeInputProps) => {
  const leaderBorder = useGetStoreState("leaderBorder");
  const characterBorder = useGetStoreState("characterBorder");
  const eventBorder = useGetStoreState("eventBorder");

  if (type === "leader-border-type") {
    // Use type assertion to tell TypeScript this is a LeaderBorder array
    const leaderBorderTypes = borderTypes as {
      value: LeaderBorder;
      label: string;
    }[];
    return (
      <SelectInput<LeaderBorder>
        label="Border Type"
        description="Card border type"
        data={leaderBorderTypes}
        value={leaderBorder}
        storeUpdaterFunction={handleLeaderBorderTypeChange}
        className="w-full"
      />
    );
  }

  if (type === "character-border-type") {
    // Use type assertion to tell TypeScript this is a CharacterBorder array
    const characterBorderTypes = borderTypes as {
      value: CharacterBorder;
      label: string;
    }[];
    return (
      <SelectInput<CharacterBorder>
        label="Border Type"
        description="Card border type"
        data={characterBorderTypes}
        value={characterBorder}
        storeUpdaterFunction={handleCharacterBorderTypeChange}
        className="w-full"
      />
    );
  }
  if (type === "event-border-type") {
    // Use type assertion to tell TypeScript this is a EventBorder array
    const eventBorderTypes = borderTypes as {
      value: EventBorder;
      label: string;
    }[];

    return (
      <SelectInput<EventBorder>
        label="Border Type"
        description="Card border type"
        data={eventBorderTypes}
        value={eventBorder}
        storeUpdaterFunction={handleEventBorderTypeChange}
        className="w-full"
      />
    );
  }
  return null;
};

export default BorderTypeInput;
