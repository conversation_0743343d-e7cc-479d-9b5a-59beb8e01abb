"use client";

import React from "react";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import { handleCardNumberChange } from "@/app/helpers/storeUpdaterFunctions";
import TextInput from "./TextInput";

const CardNumInput = () => {
  const cardNum = useGetStoreState("cardNum") as string;

  return (
    <TextInput
      label="Card Number"
      placeholder="120"
      value={cardNum}
      storeUpdaterFunction={handleCardNumberChange}
      maxCharLength={3}
      typeNumber={true}
      className="w-full"
    />
  );
};

export default CardNumInput;
