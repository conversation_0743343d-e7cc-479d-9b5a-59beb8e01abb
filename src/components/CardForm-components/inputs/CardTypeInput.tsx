"use client";

import React from "react";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import { handleCardTypeChange } from "@/app/helpers/storeUpdaterFunctions";
import TextInput from "./TextInput";

const CardTypeInput = () => {
  const cardType = useGetStoreState("cardType") as string;

  return (
    <div className="flex">
      <TextInput
        label="Type"
        description="Card type"
        placeholder="Straw Hat Pirates"
        value={cardType}
        storeUpdaterFunction={handleCardTypeChange}
        className="grow"
      />
    </div>
  );
};

export default CardTypeInput;
