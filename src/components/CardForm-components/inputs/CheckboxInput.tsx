"use client";

import React, { ChangeEvent } from "react";
import { useDispatch } from "react-redux";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { AnyAction, Dispatch } from "@reduxjs/toolkit";

interface CheckboxInputProps {
  label: string;
  checked: boolean;
  storeUpdaterFunction: (
    dispatch: Dispatch<AnyAction>,
    event: ChangeEvent<HTMLInputElement>,
  ) => void;
  className?: string;
}

const CheckboxInput = ({
  label,
  checked,
  storeUpdaterFunction,
  className = "",
}: CheckboxInputProps) => {
  const dispatch = useDispatch();

  const handleCheckedChange = (checked: boolean) => {
    // Create a synthetic event to match the expected signature
    const syntheticEvent = {
      target: {
        checked,
      },
    } as unknown as ChangeEvent<HTMLInputElement>;

    storeUpdaterFunction(dispatch, syntheticEvent);
  };

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <Checkbox
        id={label}
        checked={checked}
        onCheckedChange={handleCheckedChange}
      />
      <Label htmlFor={label}>{label}</Label>
    </div>
  );
};

export default CheckboxInput;
