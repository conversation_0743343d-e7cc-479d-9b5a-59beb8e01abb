"use client";

import React from "react";
import { Color } from "@/types";
import ColorMultiSelectInput from "./ColorMultiSelectInput";

interface ColorInputProps {
  cardColors: { value: Color; label: string }[];
  leader?: boolean;
}

const ColorInput = ({ cardColors, leader = false }: ColorInputProps) => {
  return (
    <ColorMultiSelectInput
      label="Color"
      description="Color of the card border"
      data={cardColors}
      className="w-full duration-300"
      placeholder="Select colors"
      leader={leader}
    />
  );
};

export default ColorInput;
