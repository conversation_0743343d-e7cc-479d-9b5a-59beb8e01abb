"use client";

import React, { useState } from "react";
import { useDispatch } from "react-redux";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandSeparator,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Drawer,
  DrawerContent,
  DrawerTrigger,
  DrawerTitle,
} from "@/components/ui/drawer";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { CheckIcon, ChevronsUpDownIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { Color } from "@/types";
import { useIsMobile } from "@/hooks/use-mobile";
import { Switch } from "@/components/ui/switch";
import {
  handleMultiColorChange,
  handleMultiColorModeChange,
} from "@/app/helpers/storeUpdaterFunctions";
import { useGetStoreState } from "@/helpers/useGetStoreState";

interface MultiSelectInputProps {
  label: string;
  description?: string;
  data: { value: Color; label: string }[];
  className?: string;
  maxSelections?: number;
  placeholder?: string;
  closeOnSelect?: boolean;
  leader?: boolean;
}

export default function ColorMultiSelectInput({
  label,
  description,
  data,
  className = "",
  maxSelections = 1,
  placeholder = "Select options",
  closeOnSelect = true,
  leader = false,
}: MultiSelectInputProps) {
  const dispatch = useDispatch();
  const [open, setOpen] = useState(false);
  const multiColorMode = useGetStoreState("multiColorMode");
  const maxSelectionsState = multiColorMode ? 6 : leader ? 2 : maxSelections;
  const selectedValues = useGetStoreState("colorArray");

  // Transform data if it's an array of strings
  const selectData = data.map((item) => {
    return item;
  });
  if (selectedValues.length > maxSelectionsState) {
    handleMultiColorChange(
      dispatch,
      selectedValues.slice(0, maxSelectionsState),
    );
  }

  const handleSelect = (value: Color) => {
    let newSelectedValues = [...selectedValues];

    // If already selected, remove it
    if (newSelectedValues.includes(value)) {
      // Don't allow removing if it would result in empty selection
      if (newSelectedValues.length > 1) {
        newSelectedValues = newSelectedValues.filter((v) => v !== value);
      }
    } else {
      // Add the new value
      if (newSelectedValues.length < maxSelectionsState) {
        newSelectedValues.push(value);
      } else {
        // If max selections reached, remove the first one and add the new one
        newSelectedValues.shift();
        newSelectedValues.push(value);
      }
    }

    handleMultiColorChange(dispatch, newSelectedValues);
  };
  // Function to render the selection list (used in both Popover and Drawer)
  const SelectionList = ({
    closeOnSelect = true,
  }: {
    closeOnSelect?: boolean;
  }) => (
    <Command>
      <CommandInput placeholder={`Search ${label.toLowerCase()}...`} />
      <CommandEmpty>No {label.toLowerCase()} found.</CommandEmpty>
      <div className="my-2 ml-1 flex items-center space-x-2">
        <Switch
          id="airplane-mode"
          checked={multiColorMode}
          onCheckedChange={(e) => {
            handleMultiColorModeChange(dispatch, e);
            handleMultiColorChange(
              dispatch,
              selectedValues.slice(0, e ? 6 : leader ? 2 : maxSelections),
            );
          }}
        />
        <Label htmlFor="airplane-mode">Multi-color mode</Label>
      </div>
      <CommandSeparator />
      <CommandGroup className="max-h-[240px] overflow-auto">
        {selectData.map((item) => {
          const isSelected = selectedValues.includes(item.value);
          return (
            <CommandItem
              key={item.value}
              value={item.value}
              onSelect={() => {
                handleSelect(item.value);
                if (!leader) {
                  if (!multiColorMode && closeOnSelect) {
                    setOpen(false);
                  }
                }
              }}
            >
              <div
                className={cn(
                  "border-primary mr-2 flex h-4 w-4 items-center justify-center rounded-sm border",
                  isSelected
                    ? "bg-primary text-primary-foreground"
                    : "opacity-50 [&_svg]:invisible",
                )}
              >
                <CheckIcon className="h-4 w-4" />
              </div>
              <span>{item.label}</span>
            </CommandItem>
          );
        })}
      </CommandGroup>
    </Command>
  );

  // Function to render the trigger button
  const TriggerButton = React.forwardRef<
    HTMLButtonElement,
    React.ButtonHTMLAttributes<HTMLButtonElement>
  >(({ className, ...props }, ref) => (
    <Button
      ref={ref}
      variant="outline"
      role="combobox"
      aria-expanded={open}
      className={cn(
        "dark:bg-input-dark! dark:hover:bg-input-lite-dark! h-auto! w-full justify-between hover:bg-white! md:min-w-[15rem]",
        className,
      )}
      {...props}
    >
      {selectedValues.length >= 0 ? (
        <div className="flex flex-wrap items-center gap-1">
          {selectedValues.map((value) => {
            const item = selectData.find((item) => item.value === value);
            return (
              <Badge key={value} variant="secondary">
                {item?.label}
              </Badge>
            );
          })}
        </div>
      ) : (
        <span className="text-muted-foreground">{placeholder}</span>
      )}
      <ChevronsUpDownIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
    </Button>
  ));
  TriggerButton.displayName = "TriggerButton";

  const isMobile = useIsMobile();

  return (
    <div className={`space-y-2 ${className} transition-all! duration-1000!`}>
      <div className="space-y-1">
        <Label htmlFor={label}>{label}</Label>
        {description && (
          <p className="text-muted-foreground text-xs">{description}</p>
        )}
      </div>

      {isMobile ? (
        <Drawer open={open} onOpenChange={setOpen}>
          <DrawerTrigger asChild>
            <TriggerButton />
          </DrawerTrigger>
          <DrawerContent>
            <DrawerTitle className="sr-only">
              Select {label.toLowerCase()}
            </DrawerTitle>
            <div className="mt-4 border-t p-4">
              <SelectionList closeOnSelect={closeOnSelect} />
            </div>
          </DrawerContent>
        </Drawer>
      ) : (
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <TriggerButton />
          </PopoverTrigger>
          <PopoverContent className="w-full p-0">
            <SelectionList closeOnSelect={closeOnSelect} />
          </PopoverContent>
        </Popover>
      )}
    </div>
  );
}
