"use client";

import React from "react";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import { handleCardCostChange } from "@/app/helpers/storeUpdaterFunctions";
import SelectInput from "./SelectInput";
import { CardCost } from "@/types";

const CostInput = () => {
  const cost = useGetStoreState("cost") as CardCost;
  const numbers: CardCost[] = [
    "0",
    "1",
    "2",
    "3",
    "4",
    "5",
    "6",
    "7",
    "8",
    "9",
    "10",
    "11",
    "12",
    "13",
    "14",
    "15",
    "16",
    "17",
    "18",
    "19",
  ];

  // Convert the array of strings to the format expected by SelectInput
  const costData = numbers.map((value) => ({ value, label: value }));

  return (
    <SelectInput<CardCost>
      label="Cost"
      description="Card cost"
      data={costData}
      value={cost}
      storeUpdaterFunction={handleCardCostChange}
      className="w-full"
    />
  );
};

export default CostInput;
