"use client";

import React from "react";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import { handleCardLifeChange } from "@/app/helpers/storeUpdaterFunctions";
import SelectInput from "./SelectInput";
import { CardLife } from "@/types";

const CostLeaderInput = () => {
  const life = useGetStoreState("life") as CardLife;
  const numbers: CardLife[] = [
    "0",
    "1",
    "2",
    "3",
    "4",
    "5",
    "6",
    "7",
    "8",
    "9",
    "10",
  ];

  // Convert the array of strings to the format expected by SelectInput
  const lifeData = numbers.map((value) => ({ value, label: value }));

  return (
    <SelectInput<CardLife>
      label="Life"
      description="Card life"
      data={lifeData}
      value={life}
      storeUpdaterFunction={handleCardLifeChange}
      className="w-full"
    />
  );
};

export default CostLeaderInput;
