"use client";

import React from "react";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import { handleCardCounterChange } from "@/app/helpers/storeUpdaterFunctions";
import TextInput from "./TextInput";

const CounterInput = () => {
  const counterText = useGetStoreState("counterText") as string;

  return (
    <TextInput
      label="Counter"
      description="Card counter"
      placeholder="2000"
      value={counterText}
      storeUpdaterFunction={handleCardCounterChange}
      maxCharLength={4}
      typeNumber={true}
      className="w-full"
    />
  );
};

export default CounterInput;
