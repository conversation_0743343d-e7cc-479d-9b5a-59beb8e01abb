"use client";

import React from "react";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import { handleDonPowerChange } from "@/app/helpers/storeUpdaterFunctions";
import SelectInput from "./SelectInput";

const PowerInput = () => {
  const power = useGetStoreState("donPower") as string;

  // Generate power array from 0 to 19000 in increments of 1000
  const powerArray = [
    "0",
    "1000",
    "2000",
    "3000",
    "4000",
    "5000",
    "6000",
    "7000",
    "8000",
    "9000",
    "10000",
  ];

  // Convert the array of strings to the format expected by SelectInput
  const powerData = powerArray.map((value) => ({ value, label: value }));

  return (
    <SelectInput<string>
      label="Power"
      description="Don power"
      data={powerData}
      value={power}
      storeUpdaterFunction={handleDonPowerChange}
      className="w-full"
    />
  );
};

export default PowerInput;
