"use client";

import React from "react";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import { handleDonTextChange } from "@/app/helpers/storeUpdaterFunctions";
import TextInput from "./TextInput";
import { onDonFontSize } from "@/store/formSlice";
import NumberInput from "./NumberInput";

const DonTextInput = () => {
  const name = useGetStoreState("donText") as string;

  return (
    <div className="flex">
      <TextInput
        label="Text"
        description="DON Text"
        placeholder="Your Turn"
        value={name}
        storeUpdaterFunction={handleDonTextChange}
        className="grow"
        classNameForInput={"rounded-r-none"}
      />
      <div className="flex items-end">
        <NumberInput
          inputFor="donFontSize"
          onChangeAction={onDonFontSize}
          min={1}
          max={100}
          step={1}
        />
      </div>
    </div>
  );
};

export default DonTextInput;
