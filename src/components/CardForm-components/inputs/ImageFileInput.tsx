"use client";

import React from "react";
import {
  onBackgroundImageFileUpload,
  onBackgroundImageUrl,
  onForegroundImageFileUpload,
  onImageUrl,
  onIsCropping,
} from "@/store/formSlice";
import ImageInput from "./ImageInput";

interface ImageFileInputProps {
  imageType: "foreground" | "background";
}

const ImageFileInput = ({ imageType }: ImageFileInputProps) => {
  return (
    <ImageInput
      imageType={imageType}
      onFileUpload={
        imageType === "foreground"
          ? onForegroundImageFileUpload
          : onBackgroundImageFileUpload
      }
      onImageUrl={
        imageType === "foreground" ? onImageUrl : onBackgroundImageUrl
      }
      onIsCropping={onIsCropping}
    />
  );
};

export default ImageFileInput;
