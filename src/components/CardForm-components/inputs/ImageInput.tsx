"use client";

import React, { useCallback } from "react";
import { useDispatch } from "react-redux";
import { onCrop, onFileUrl, onZoom } from "@/store/cropperSlice";

interface ImageInputProps {
  imageType: "foreground" | "background";
  onFileUpload: (file: File) => { type: string; payload: File };
  onImageUrl: (url: string) => { type: string; payload: string };
  onIsCropping: (value: boolean) => { type: string; payload: boolean };
}

const ImageInput = ({
  onFileUpload,
  onImageUrl,
  onIsCropping,
}: ImageInputProps) => {
  const dispatch = useDispatch();

  const handleDrop = useCallback(
    (
      e: React.DragEvent<HTMLDivElement> | React.ChangeEvent<HTMLInputElement>,
    ) => {
      dispatch(onIsCropping(false));

      let file: File | null = null;

      if ("dataTransfer" in e) {
        // Drag event
        e.preventDefault();
        if (e.dataTransfer.files && e.dataTransfer.files[0]) {
          file = e.dataTransfer.files[0];
        }
      } else if (e.target.files && e.target.files[0]) {
        // Input change event
        file = e.target.files[0];
      }

      if (file) {
        dispatch(onFileUpload(file));
        dispatch(onCrop({ x: 0, y: 0 }));
        dispatch(onZoom(1));

        const reader = new FileReader();
        reader.onload = (e) => {
          if (e?.target?.result) {
            dispatch(onImageUrl(e.target.result as string));
            dispatch(onFileUrl(e.target.result as string));
          }
        };
        reader.readAsDataURL(file);
      }
    },
    [dispatch, onFileUpload, onImageUrl, onIsCropping],
  );

  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
  }, []);

  return (
    <div className="space-y-2">
      <div
        className="relative flex min-h-[170px] min-w-[207px] items-center justify-center rounded-md border-2 border-dashed border-neutral-200 bg-white text-neutral-400 hover:cursor-pointer dark:border-neutral-700 dark:bg-neutral-900/10"
        onDrop={handleDrop}
        onDragOver={handleDragOver}
      >
        <input
          type="file"
          className="absolute inset-0 h-full w-full cursor-pointer opacity-0"
          onChange={handleDrop}
          accept="image/png,image/jpeg,image/webp"
        />
        <div className="p-4 text-center">
          <p>Click here or drop images</p>
          <p className="mt-2 text-xs">Accepts PNG, JPEG, WEBP</p>
        </div>
      </div>
    </div>
  );
};

export default ImageInput;
