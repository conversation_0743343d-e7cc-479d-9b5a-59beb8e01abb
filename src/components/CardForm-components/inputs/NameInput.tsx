"use client";

import React from "react";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import { handleNameChange } from "@/app/helpers/storeUpdaterFunctions";
import TextInput from "./TextInput";
import { onNameFontSize } from "@/store/formSlice";
import NumberInput from "./NumberInput";

const NameInput = () => {
  const name = useGetStoreState("name") as string;

  return (
    <div className="flex">
      <TextInput
        label="Name"
        description="Card name"
        placeholder="Monkey D. Luffy"
        value={name}
        storeUpdaterFunction={handleNameChange}
        className="grow"
        classNameForInput={"rounded-r-none"}
      />
      <div className="flex items-end">
        <NumberInput
          inputFor="nameFontSize"
          onChangeAction={onNameFontSize}
          min={1}
          max={100}
          step={1}
        />
      </div>
    </div>
  );
};

export default NameInput;
