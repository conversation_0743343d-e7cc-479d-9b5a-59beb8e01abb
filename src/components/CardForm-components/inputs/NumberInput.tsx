"use client";

import React from "react";
import { useDispatch } from "react-redux";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import { ChevronUpIcon, ChevronDownIcon } from "lucide-react";
import { cn } from "@/lib/utils";

interface NumberInputProps {
  inputFor: "typeFontSize" | "nameFontSize" | "donFontSize";
  onChangeAction: (value: number) => { type: string; payload: number };
  min?: number;
  max?: number;
  step?: number;
  className?: string;
}

const NumberInput = ({
  inputFor,
  onChangeAction,
  min = 0,
  max = 100,
  step = 1,
  className,
}: NumberInputProps) => {
  const value = useGetStoreState(inputFor) as number;
  const dispatch = useDispatch();

  const handleChange = (newValue: number) => {
    // Ensure the value is within bounds
    const boundedValue = Math.min(Math.max(newValue, min), max);
    dispatch(onChangeAction(boundedValue));
  };

  const increment = () => {
    handleChange(value + step);
  };

  const decrement = () => {
    handleChange(value - step);
  };

  return (
    <div className={cn("relative flex items-center", className)}>
      <Input
        type="number"
        value={value}
        min={min}
        max={max}
        step={step}
        onChange={(e) => handleChange(Number(e.target.value))}
        className="h-9 w-[5rem] [appearance:textfield] rounded-l-none pr-7 pl-0 text-center [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none"
      />
      <div className="absolute right-0 flex h-full flex-col">
        <Button
          type="button"
          variant="ghost"
          size="icon"
          className="h-[18px] w-7 rounded-none rounded-tr-md border-l"
          onClick={increment}
          disabled={value >= max}
        >
          <ChevronUpIcon className="h-3 w-3" />
        </Button>
        <Button
          type="button"
          variant="ghost"
          size="icon"
          className="h-[18px] w-7 rounded-none rounded-br-md border-t border-l"
          onClick={decrement}
          disabled={value <= min}
        >
          <ChevronDownIcon className="h-3 w-3" />
        </Button>
      </div>
    </div>
  );
};

export default NumberInput;
