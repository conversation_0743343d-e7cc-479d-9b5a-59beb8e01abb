"use client";

import React, { ChangeEvent } from "react";
import { useDispatch } from "react-redux";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import {
  handlePowerBlackChange,
  handleLeaderBorderEnabledChange,
  handleAAStarChange,
  handleFoilBorderChange,
  handlePrintReadyChange,
  handleDonAbilityChange,
} from "@/app/helpers/storeUpdaterFunctions";
import { AnyAction, Dispatch } from "@reduxjs/toolkit";

// Define the checkbox types and their configurations
type CheckboxType =
  | "powerBlack"
  | "leaderBorderEnabled"
  | "donAbility"
  | "aaStar"
  | "foilBorder"
  | "printReady";

interface OnePieceCheckboxInputProps {
  type: CheckboxType;
  cardKind?: "character" | "leader" | "event" | "stage" | "don";
}

// Map of checkbox types to their labels
const CHECKBOX_LABELS: Record<CheckboxType, string> = {
  powerBlack: "Power outline",
  leaderBorderEnabled: "Foreground behind border",
  aaStar: "AA Star",
  foilBorder: "Foil border",
  printReady: "Print ready",
  donAbility: "Don Ability",
};

// Map of checkbox types to their updater functions
const CHECKBOX_UPDATERS: Record<
  CheckboxType,
  (dispatch: Dispatch<AnyAction>, event: ChangeEvent<HTMLInputElement>) => void
> = {
  powerBlack: handlePowerBlackChange,
  leaderBorderEnabled: handleLeaderBorderEnabledChange,
  aaStar: handleAAStarChange,
  foilBorder: handleFoilBorderChange,
  printReady: handlePrintReadyChange,
  donAbility: handleDonAbilityChange,
};

/**
 * Optimized checkbox input component for One Piece that only re-renders when its specific state changes.
 * Each checkbox uses its own state from the store directly, preventing unnecessary re-renders.
 */
const OnePieceCheckboxInput = ({
  type,
  cardKind,
}: OnePieceCheckboxInputProps) => {
  const dispatch = useDispatch();

  // Only get the specific state needed for this checkbox
  const checked = useGetStoreState(type) as boolean;

  // For powerBlack with leader card, we need to check the leaderBorder state
  const leaderBorder = useGetStoreState("leaderBorder");
  const eventBorder = useGetStoreState("eventBorder");

  // Special case for powerBlack with leader card
  if (
    type === "powerBlack" &&
    cardKind === "leader" &&
    leaderBorder !== "standard"
  ) {
    return null;
  }
  if (
    type === "foilBorder" &&
    cardKind === "event" &&
    eventBorder !== "standard"
  ) {
    return null;
  }
  const handleCheckedChange = (isChecked: boolean) => {
    // Create a synthetic event to match the expected signature
    const syntheticEvent = {
      target: {
        checked: isChecked,
      },
    } as unknown as ChangeEvent<HTMLInputElement>;

    // Get the appropriate updater function for this checkbox type
    const updaterFunction = CHECKBOX_UPDATERS[type];
    updaterFunction(dispatch, syntheticEvent);
  };

  return (
    <div className="flex items-center space-x-2">
      <Checkbox
        id={CHECKBOX_LABELS[type]}
        checked={checked}
        onCheckedChange={handleCheckedChange}
      />
      <Label htmlFor={CHECKBOX_LABELS[type]}>{CHECKBOX_LABELS[type]}</Label>
    </div>
  );
};

export default OnePieceCheckboxInput;
