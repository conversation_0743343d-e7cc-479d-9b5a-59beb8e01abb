"use client";

import React from "react";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import { handleCardPowerChange } from "@/app/helpers/storeUpdaterFunctions";
import SelectInput from "./SelectInput";

const PowerInput = () => {
  const power = useGetStoreState("power") as string;

  // Generate power array from 0 to 19000 in increments of 1000
  const powerArray = [
    "0",
    "1000",
    "2000",
    "3000",
    "4000",
    "5000",
    "6000",
    "7000",
    "8000",
    "9000",
    "10000",
    "11000",
    "12000",
    "13000",
    "14000",
    "15000",
    "16000",
    "17000",
    "18000",
    "19000",
  ];

  // Convert the array of strings to the format expected by SelectInput
  const powerData = powerArray.map((value) => ({ value, label: value }));

  return (
    <SelectInput<string>
      label="Power"
      description="Card power"
      data={powerData}
      value={power}
      storeUpdaterFunction={handleCardPowerChange}
      className="w-full"
    />
  );
};

export default PowerInput;
