"use client";

import React from "react";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import { handleCardPrintWaveChange } from "@/app/helpers/storeUpdaterFunctions";
import TextInput from "./TextInput";

const PrintWaveInput = () => {
  const printWave = useGetStoreState("printWave") as string;

  return (
    <TextInput
      label="Block Number"
      placeholder="1"
      value={printWave}
      storeUpdaterFunction={handleCardPrintWaveChange}
      maxCharLength={2}
      typeNumber={true}
      className="w-full"
    />
  );
};

export default PrintWaveInput;
