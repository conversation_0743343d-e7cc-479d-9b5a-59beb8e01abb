"use client";

import React from "react";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import { handleCardRarityChange, handleCardRarity2Change } from "@/app/helpers/storeUpdaterFunctions";
import TextInput from "./TextInput";

interface RarityInputProps {
  type: "rarity" | "rarity2";
}

const RarityInput = ({ type }: RarityInputProps) => {
  const rarity = useGetStoreState("rarity") as string;
  const rarity2 = useGetStoreState("rarity2") as string;

  const value = type === "rarity" ? rarity : rarity2;
  const storeUpdaterFunction = type === "rarity" ? handleCardRarityChange : handleCardRarity2Change;
  const label = type === "rarity" ? "Rarity" : "Rarity 2";
  const placeholder = type === "rarity" ? "SEC" : "SP";
  const maxCharLength = type === "rarity" ? 3 : 2;

  return (
    <TextInput
      label={label}
      description="Card rarity"
      placeholder={placeholder}
      value={value}
      storeUpdaterFunction={storeUpdaterFunction}
      maxCharLength={maxCharLength}
      className="w-full"
    />
  );
};

export default RarityInput;
