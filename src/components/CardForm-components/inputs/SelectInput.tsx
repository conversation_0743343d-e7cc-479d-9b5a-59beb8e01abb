"use client";

import * as React from "react";
import { useState } from "react";
import { useDispatch } from "react-redux";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Drawer,
  DrawerContent,
  DrawerTrigger,
  DrawerTitle,
} from "@/components/ui/drawer";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { CheckIcon, ChevronsUpDownIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { useIsMobile } from "@/hooks/use-mobile";
import { AnyAction, Dispatch } from "@reduxjs/toolkit";

type StoreUpdaterFunction<T extends React.Key = string> = (
  dispatch: Dispatch<AnyAction>,
  value: T,
) => void;

interface SelectInputProps<T extends React.Key = string> {
  label: string;
  description?: string;
  // data can be array of strings or array of objects with value and label of type T
  data: Array<{ value: T; label: string }>;
  value: T;
  storeUpdaterFunction: StoreUpdaterFunction<T>;
  className?: string;
  searchable?: boolean;
  placeholder?: string;
}

export default function SelectInput<T extends React.Key = string>({
  label,
  description,
  data,
  value,
  storeUpdaterFunction,
  className = "",
  placeholder,
}: SelectInputProps<T>) {
  const dispatch = useDispatch();
  const [open, setOpen] = useState(false);
  // Normalize data to array of { value, label }
  const selectData = data;

  const handleSelect = (currentValue: T) => {
    storeUpdaterFunction(dispatch, currentValue);
  };

  // Selection list component
  const SelectionList = () => (
    <Command>
      <CommandInput placeholder={`Search ${label.toLowerCase()}...`} />
      <CommandEmpty>No {label.toLowerCase()} found.</CommandEmpty>
      <CommandGroup className="max-h-[240px] overflow-auto">
        {selectData.map((item) => {
          const isSelected =
            JSON.stringify(value) === JSON.stringify(item.value);
          return (
            <CommandItem
              key={String(item.value)}
              value={String(item.value)}
              onSelect={() => {
                handleSelect(item.value as T);
                setOpen(false);
              }}
            >
              <div
                className={cn(
                  "border-primary mr-2 flex h-4 w-4 items-center justify-center rounded-sm border",
                  isSelected
                    ? "bg-primary text-primary-foreground"
                    : "opacity-50 [&_svg]:invisible",
                )}
              >
                <CheckIcon className="h-4 w-4" />
              </div>
              <span>{item.label}</span>
            </CommandItem>
          );
        })}
      </CommandGroup>
    </Command>
  );

  // ForwardRef for the trigger button
  const TriggerButton = React.forwardRef<
    HTMLButtonElement,
    React.ButtonHTMLAttributes<HTMLButtonElement>
  >(({ className, ...props }, ref) => {
    const selectedItem = selectData.find(
      (item) => JSON.stringify(item.value) === JSON.stringify(value),
    );

    return (
      <Button
        ref={ref}
        variant="outline"
        role="combobox"
        aria-expanded={open}
        className={cn(
          "dark:bg-input-dark! dark:hover:bg-input-lite-dark! w-full justify-between bg-white hover:bg-white",
          className,
        )}
        {...props}
      >
        <span className={selectedItem ? "" : "text-muted-foreground"}>
          {selectedItem ? selectedItem.label : placeholder || `Select ${label}`}
        </span>
        <ChevronsUpDownIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
      </Button>
    );
  });
  TriggerButton.displayName = "TriggerButton";

  const isMobile = useIsMobile();

  return (
    <div className={`space-y-2 ${className} transition-all! duration-1000!`}>
      <div className="space-y-1">
        <Label htmlFor={label}>{label}</Label>
        {description && (
          <p className="text-muted-foreground text-xs">{description}</p>
        )}
      </div>

      {isMobile ? (
        <Drawer open={open} onOpenChange={setOpen}>
          <DrawerTrigger asChild>
            <TriggerButton />
          </DrawerTrigger>
          <DrawerContent>
            <DrawerTitle className="sr-only">
              Select {label.toLowerCase()}
            </DrawerTitle>
            <div className="mt-4 border-t p-4">
              <SelectionList />
            </div>
          </DrawerContent>
        </Drawer>
      ) : (
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <TriggerButton />
          </PopoverTrigger>
          <PopoverContent className="w-full p-0">
            <SelectionList />
          </PopoverContent>
        </Popover>
      )}
    </div>
  );
}
