"use client";

import React from "react";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import { handleCardSetChange } from "@/app/helpers/storeUpdaterFunctions";
import TextInput from "./TextInput";

const SetInput = () => {
  const set = useGetStoreState("set") as string;

  return (
    <TextInput
      label="Set"
      description="Card set"
      placeholder="OP03"
      value={set}
      storeUpdaterFunction={handleCardSetChange}
      maxCharLength={4}
      className="w-full"
    />
  );
};

export default SetInput;
