"use client";

import React, { ChangeEvent } from "react";
import { useDispatch } from "react-redux";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { AnyAction, Dispatch } from "@reduxjs/toolkit";

interface TextInputProps {
  label: string;
  description?: string;
  placeholder?: string;
  value: string;
  storeUpdaterFunction: (
    dispatch: Dispatch<AnyAction>,
    event: ChangeEvent<HTMLInputElement>,
  ) => void;
  maxCharLength?: number;
  typeNumber?: boolean;
  doesntStartWith0?: boolean;
  className?: string;
  classNameForInput?: string;
}

const TextInput = ({
  label,
  description,
  placeholder = "",
  value,
  storeUpdaterFunction,
  maxCharLength = 0,
  typeNumber = false,
  doesntStartWith0 = false,
  className = "",
  classNameForInput = "",
}: TextInputProps) => {
  const dispatch = useDispatch();

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    const maxLength = maxCharLength || 999999;

    if (typeNumber) {
      if (
        !e.target.value.includes(" ") &&
        e.target.value.length <= maxLength &&
        Number(e.target.value) >= 0 &&
        (!doesntStartWith0 || !e.target.value.startsWith("0"))
      ) {
        storeUpdaterFunction(dispatch, e);
      }
    } else if (!maxCharLength || e.target.value.length <= maxCharLength) {
      storeUpdaterFunction(dispatch, e);
    }
  };

  return (
    <div className={`space-y-2 ${className}`}>
      <div className="space-y-1">
        <Label htmlFor={label}>{label}</Label>
        {description && (
          <p className="text-muted-foreground text-xs">{description}</p>
        )}
      </div>
      <Input
        id={label}
        placeholder={placeholder}
        value={value}
        onChange={handleChange}
        className={classNameForInput}
        type={typeNumber ? "text" : "text"}
      />
    </div>
  );
};

export default TextInput;
