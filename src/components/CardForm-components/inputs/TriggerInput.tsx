"use client";

import React from "react";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import { handleCardTriggerChange } from "@/app/helpers/storeUpdaterFunctions";
import TextInput from "./TextInput";

const TriggerInput = () => {
  const triggerText = useGetStoreState("triggerText") as string;

  return (
    <TextInput
      label="Trigger"
      description="Card trigger"
      placeholder="Enter text..."
      value={triggerText}
      storeUpdaterFunction={handleCardTriggerChange}
      maxCharLength={9999}
      className="w-full"
    />
  );
};

export default TriggerInput;
