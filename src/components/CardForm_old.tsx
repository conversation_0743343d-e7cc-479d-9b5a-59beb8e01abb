// "use client";
// import { useParams } from "next/navigation";
// import { useState } from "react";
// import { useSelector } from "react-redux";
// import { storeState } from "@/store/store";
// import { FormField } from "@/components/FormField";
// import { ColorButton } from "@/components/ColorButton";
// import { FieldLabel } from "@/components/FieldLabel";
// import { FieldInput } from "@/components/FieldInput";
// import { AttributeButton } from "@/components/AttributeButton";
// import { handleScreenShot } from "@/helpers/handleScreenShot";
// import AbilityInput from "@/components/AbilityInput";
//
// function CardForm_old() {
//   const route = useParams();
//   const currentRoute = route.Characters;
//   const [content, setConetent] = useState("");
//   const blackBorder = useSelector(
//     (state: storeState) => state.mainFormSlice.blackBorder,
//   );
//   const imageError = useSelector(
//     (state: storeState) => state.mainFormSlice.imageError,
//   );
//   const op3 = useSelector((state: storeState) => state.mainFormSlice["op3 AA"]);
//   const op1 = useSelector(
//     (state: storeState) => state.mainFormSlice["op1/2 AA"],
//   );
//   const rainbow = useSelector(
//     (state: storeState) => state.mainFormSlice.rainbow,
//   );
//   const notDonRoute = route.Characters !== "Don";
//   const notCharacterRoute = route.Characters !== "Character";
//   return (
//     <div>
//       <div className={"relative flex flex-col gap-8 pl-8 pt-7"}>
//         {notDonRoute && (
//           <FormField className={"flex items-center gap-7"}>
//             <FieldLabel>Color:</FieldLabel>
//             <div className={"flex max-w-2xl flex-wrap gap-x-2.5 gap-y-2.5"}>
//               <ColorButton color={"red"} />
//               <ColorButton color={"green"} />
//               <ColorButton color={"blue"} />
//               <ColorButton color={"purple"} />
//               <ColorButton color={"black"} />
//               <ColorButton color={"yellow"} />
//
//               {currentRoute === "Leader" && (
//                 <>
//                   <ColorButton color={"purpleBlack"} />
//                   <ColorButton color={"purpleYellow"} />
//                   <ColorButton color={"blackYellow"} />
//                   <ColorButton color={"blueBlack"} />
//                   <ColorButton color={"bluePurple"} />
//                   <ColorButton color={"blueYellow"} />
//                   <ColorButton color={"greenBlack"} />
//                   <ColorButton color={"greenBlue"} />
//                   <ColorButton color={"greenPurple"} />
//                   <ColorButton color={"greenYellow"} />
//                   <ColorButton color={"redBlack"} />
//                   <ColorButton color={"redBlue"} />
//                   <ColorButton color={"redGreen"} />
//                   <ColorButton color={"redPurple"} />
//                   <ColorButton color={"redYellow"} />
//                 </>
//               )}
//             </div>
//           </FormField>
//         )}
//         {(currentRoute === "Character" || currentRoute === "Leader") && (
//           <FormField className={"flex items-center gap-7"}>
//             <FieldLabel>Attribute:</FieldLabel>
//             <AttributeButton type={"slash"} />
//             <AttributeButton type={"wisdom"} />
//             <AttributeButton type={"ranged"} />
//             <AttributeButton type={"special"} />
//             <AttributeButton type={"strike"} />
//           </FormField>
//         )}
//         {notDonRoute && (
//           <FormField className={"flex gap-[3.3rem]"}>
//             <FieldLabel>Name:</FieldLabel>
//             <FieldInput
//               maxCharNum={22}
//               placeholder={"Monkey D. Luffy"}
//               inputFor={"name"}
//             />
//           </FormField>
//         )}
//         {notDonRoute && (
//           <FormField className={"flex gap-16"}>
//             <FieldLabel>Type:</FieldLabel>
//             <FieldInput
//               maxCharNum={30}
//               placeholder={"Straw Hat Pirates"}
//               inputFor={"cardType"}
//             />
//           </FormField>
//         )}
//         {notDonRoute && (
//           <div className={"flex justify-between gap-8"}>
//             <FormField className={"flex gap-16"}>
//               <FieldLabel>Cost:</FieldLabel>
//               <FieldInput
//                 placeholder={"1-10"}
//                 inputFor={"cost"}
//                 type={"number"}
//                 maxNum={10}
//               />
//             </FormField>
//             {(currentRoute === "Character" || currentRoute === "Leader") && (
//               <FormField className={"flex gap-16"}>
//                 <div className={"flex flex-row items-center gap-x-2"}>
//                   <FieldLabel>Power:</FieldLabel>
//                   {notCharacterRoute && (
//                     <FieldInput type={"checkbox"} inputFor={"powerBlack"} />
//                   )}
//                 </div>
//                 <FieldInput
//                   placeholder={"5000"}
//                   inputFor={"power"}
//                   type={"number"}
//                   maxNum={99999}
//                 />
//               </FormField>
//             )}
//           </div>
//         )}
//         {notDonRoute && (
//           <FormField className={"flex justify-between gap-8 "}>
//             <div className={"flex flex-col justify-between"}>
//               <FieldLabel>Ability:</FieldLabel>
//             </div>
//             <AbilityInput />
//           </FormField>
//         )}
//         {currentRoute === "Leader" && (
//           <FormField className={"flex items-center gap-3"}>
//             <FieldLabel>Borders:</FieldLabel>
//             <div className={"flex gap-x-7"}>
//               <div className={"flex items-center gap-x-2"}>
//                 <FieldLabel>Black:</FieldLabel>
//                 <FieldInput
//                   type={"checkbox"}
//                   inputFor={"blackBorder"}
//                   disabled={blackBorder}
//                 />
//               </div>
//               <div className={"flex items-center gap-x-2"}>
//                 <FieldLabel>OP3 AA:</FieldLabel>
//                 <FieldInput
//                   type={"checkbox"}
//                   inputFor={"op3 AA"}
//                   disabled={op3}
//                 />
//               </div>
//               <div className={"flex items-center gap-x-2"}>
//                 <FieldLabel>OP1/2 AA:</FieldLabel>
//                 <FieldInput
//                   type={"checkbox"}
//                   inputFor={"op1/2 AA"}
//                   disabled={op1}
//                 />
//               </div>
//               <div className={"flex items-center gap-x-2"}>
//                 <FieldLabel>Rainbow:</FieldLabel>
//                 <FieldInput
//                   type={"checkbox"}
//                   inputFor={"rainbow"}
//                   disabled={rainbow}
//                 />
//               </div>
//             </div>
//           </FormField>
//         )}
//         {(currentRoute === "Event" || currentRoute === "Stage") && (
//           <FormField className={"flex items-center gap-3"}>
//             <FieldLabel>Foil Border:</FieldLabel>
//             <FieldInput type={"checkbox"} inputFor={"foilBorder"} />
//           </FormField>
//         )}
//         {currentRoute !== "Leader" && currentRoute !== "Don" && (
//           <FormField className={"flex items-center gap-3"}>
//             <FieldInput type={"checkbox"} inputFor={"trigger"} />
//             <FieldLabel>Trigger:</FieldLabel>
//             <FieldInput
//               maxCharNum={52}
//               placeholder={"Enter text..."}
//               className={" "}
//               inputFor={"triggerText"}
//             />
//           </FormField>
//         )}
//         {currentRoute === "Character" && (
//           <FormField className={"flex items-center gap-3"}>
//             <FieldInput type={"checkbox"} inputFor={"counter"} />
//             <FieldLabel>Counter:</FieldLabel>
//             <FieldInput
//               placeholder={"2000"}
//               className={" max-w-[8rem] "}
//               inputFor={"counterText"}
//               type={"number"}
//               maxCharNum={4}
//             />
//           </FormField>
//         )}
//         {notDonRoute && (
//           <div className={"testSC flex justify-between"}>
//             <FormField className={"flex items-center gap-3"}>
//               <FieldLabel>Set:</FieldLabel>
//               <FieldInput
//                 placeholder={"OP03"}
//                 className={" max-w-[5rem] "}
//                 inputFor={"set"}
//                 maxCharNum={4}
//               />
//             </FormField>
//             <FormField className={"flex items-center gap-3"}>
//               <FieldLabel>Rarity:</FieldLabel>
//               <FieldInput
//                 placeholder={"SEC"}
//                 className={" max-w-[5rem] "}
//                 inputFor={"rarity"}
//                 maxCharNum={3}
//               />
//             </FormField>
//             <FormField className={"flex items-center gap-3"}>
//               <FieldLabel>Card Num:</FieldLabel>
//               <FieldInput
//                 placeholder={"120"}
//                 className={" max-w-[5rem] "}
//                 inputFor={"cardNum"}
//                 type={"number"}
//                 maxCharNum={3}
//               />
//             </FormField>
//             <FormField className={"flex items-center gap-3"}>
//               <FieldLabel>Print Wave:</FieldLabel>
//               <FieldInput
//                 placeholder={"1"}
//                 className={" max-w-[5rem] "}
//                 inputFor={"printWave"}
//                 type={"number"}
//                 maxCharNum={2}
//               />
//             </FormField>
//           </div>
//         )}
//         <div className={"flex flex-col"}>
//           <FormField
//             className={`flex items-center gap-3 ${
//               !notDonRoute ? "w-[50rem]" : ""
//             }`}
//           >
//             <div className={"flex flex-col items-end gap-3"}>
//               <FieldLabel>Image:</FieldLabel>
//               <div className={"flex flex-row items-center gap-2"}>
//                 <FieldInput type={"checkbox"} inputFor={"imageFull"} />
//                 <FieldLabel>Image full height</FieldLabel>
//               </div>
//             </div>
//
//             <FieldInput
//               placeholder={"Enter link..."}
//               className={" "}
//               inputFor={"imageUrl"}
//               maxNum={99}
//               type={"image"}
//             />
//           </FormField>
//           {imageError && (
//             <p className={"mt-2 inline-block self-end rounded-sm bg-red-900 px-2"}>
//               {imageError}
//             </p>
//           )}
//         </div>
//
//         <FormField className={"flex items-center gap-3"}>
//           <FieldLabel>Artist:</FieldLabel>
//           <FieldInput
//             placeholder={"Artist name"}
//             className={" max-w-[16rem] overflow-clip"}
//             inputFor={"artist"}
//             maxCharNum={60}
//           />
//           <button
//             className={
//               "ml-auto rounded-sm bg-white px-2 text-xl font-bold text-stone-900 hover:bg-stone-200"
//             }
//             onClick={handleScreenShot}
//           >
//             Download
//           </button>
//         </FormField>
//       </div>
//     </div>
//   );
// }
//
// export default CardForm_old;
