/*"use client";
import { useDispatch, useSelector } from "react-redux";
import { sliceFromFirstUppercase } from "@/helpers/sliceFromFirstUppercase";
import { storeState } from "@/store/store";
import { onColor } from "@/store/formSlice";*/

/*export function ColorButton({ color }: { color: Color }) {
  const bgColor = {
    red: "#8A2A2BFF, #8A2A2BFF",
    green: "#208E6A, #208E6A",
    blue: "#3A81BEFF, #3A81BEFF",
    yellow: "#F6E649, #F6E649",
    purple: "#6A3F71FF, #6A3F71FF",
    black: "#202020, #202020",
    blackYellow: "#202020  25%, #F6E649",
    blueBlack: "#2679B1  25%, #202020",
    bluePurple: "#2679B1  25%, #913D79",
    blueYellow: "#2679B1  25%, #F6E649",
    greenBlack: "#208E6A  25%, #202020",
    greenBlue: "#208E6A  25%, #2679B1",
    greenPurple: "#208E6A  25%, #6A3F71FF",
    greenYellow: "#208E6A  25%, #F6E649",
    purpleBlack: "#6A3F71FF  25%, #202020",
    redBlack: "#B70D21  25%, #202020",
    redBlue: "#B70D21  25%, #2679B1",
    redGreen: "#B70D21  25%, #208E6A",
    redPurple: "#B70D21  25%, #6A3F71FF",
    redYellow: "#B70D21  25%, #F6E649",
    purpleYellow: "#6A3F71FF  25%, #F6E649",
  };
  const active =
    useSelector((state: storeState) => state.mainFormSlice.color) === color;
  const dispatch = useDispatch();

  const buttonName = sliceFromFirstUppercase(color);

  return (
    <button
      style={{
        background: `linear-gradient(to right, ${bgColor[color]})`,
      }}
      onClick={() => {
        dispatch(onColor(color));
      }}
      className={` ${
        (color === "yellow" ||
          color === "purpleYellow" ||
          color === "greenYellow") &&
        "font-medium text-stone-900"
      } max-h-[3rem] min-h-[3rem] min-w-[3.2rem] max-w-[3.2rem] break-words px-0.5  leading-4 ${
        active ? "drop-shadow-[0_0px_3px_rgba(255,255,255,1)]" : ""
      }`}
    >
      {buttonName}
    </button>
  );
}*/
/*

type Color =
  | "red"
  | "green"
  | "blue"
  | "yellow"
  | "purple"
  | "black"
  | "blackYellow"
  | "blueBlack"
  | "bluePurple"
  | "blueYellow"
  | "greenBlack"
  | "greenBlue"
  | "greenPurple"
  | "greenYellow"
  | "purpleBlack"
  | "redBlack"
  | "redBlue"
  | "redGreen"
  | "redPurple"
  | "redYellow"
  | "purpleYellow";
*/
