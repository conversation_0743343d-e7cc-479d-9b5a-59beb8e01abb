// "use client";
//
// import Link from "next/link";
// import { useDispatch } from "react-redux";
//
// export default function CreateCardsButton() {
//   const dispatch = useDispatch();
//
//   return (
//     <Link
//       className={
//         "tap rounded-[5rem] bg-neutral-950 px-8! py-4! text-xl font-bold text-neutral-100 transition-all duration-200 target:bg-neutral-800 focus-within:bg-neutral-800 hover:bg-neutral-800 focus:bg-neutral-800 focus-visible:bg-neutral-800 active:bg-neutral-800 dark:bg-white dark:text-stone-800 dark:target:bg-stone-200 dark:focus-within:bg-stone-300 dark:hover:bg-stone-300 dark:focus:bg-stone-300 dark:focus-visible:bg-stone-300 dark:active:bg-stone-300"
//       }
//       onClick={() => {}}
//       href={"/one-piece/character"}
//     >
//       Create Cards
//     </Link>
//   );
// }
