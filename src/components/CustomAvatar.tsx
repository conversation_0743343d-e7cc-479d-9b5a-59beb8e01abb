/*
"use client";
import {
  createClientComponentClient,
  createServerComponentClient,
} from "@supabase/auth-helpers-nextjs";
import { Database } from "@/types/supabase";
import { cookies } from "next/headers";

export default async function CustomAvatar() {
  const supabase = createClientComponentClient();

  const {
    data: { session },
  } = await supabase.auth.getSession();
  return <div>test</div>;
}
*/
