"use client";

import React, { useState } from "react";
import { useIsMobile } from "@/hooks/use-mobile";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import Image from "next/image";
import { Button } from "@/components/ui/button";

interface CardButtonProps {
  children: React.ReactNode;
}
const cardIcon = `${process.env.NEXT_PUBLIC_ASSETS_URL}/card-icon.svg`;
export default function ExpandableCard({ children }: CardButtonProps) {
  const isMobile = useIsMobile();
  const [loading, setLoading] = useState(false);

  function triggerLoading() {
    setTimeout(() => {
      setLoading(false);
    }, 500);
    setLoading(true);
  }

  if (!isMobile) {
    return <>{children}</>;
  }

  return (
    <Dialog>
      <DialogTitle />
      <DialogTrigger asChild>
        <Button
          className="bg-primary text-primary-foreground fixed right-4 bottom-4 z-50 flex h-12 w-12 items-center justify-center rounded-full p-0! shadow-lg"
          onClick={triggerLoading}
          loading={loading}
        >
          <Image src={cardIcon} alt="Card Icon" width={32} height={32} />
          {/*<span className="sr-only">Open Card</span>*/}
        </Button>
      </DialogTrigger>
      <DialogContent className="flex max-h-[90vh] w-[95vw] max-w-full justify-center overflow-auto bg-transparent p-0 md:hidden">
        {children}
      </DialogContent>
    </Dialog>
  );
}
