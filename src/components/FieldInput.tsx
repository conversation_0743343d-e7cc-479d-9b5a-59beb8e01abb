// "use client";
// import { useDispatch, useSelector } from "react-redux";
// import React, { ChangeEvent, MutableRefObject, useRef } from "react";
// import { storeState } from "@/store/store";
// import { onInputField, state } from "@/store/formSlice";
//
// export function FieldInput({
//   className,
//   inputFor = "" as keyof state,
//   placeholder,
//   type = "",
//   maxNum = 99999,
//   maxCharNum = null,
//   disabled,
// }: {
//   className?: string;
//   inputFor: keyof state;
//   placeholder?: string;
//   type?: string;
//   maxNum?: number;
//   maxCharNum?: number | null;
//   disabled?: boolean;
// }) {
//   const stateValue = useSelector(
//     (state: storeState) => state.mainFormSlice[inputFor],
//   );
//   const image = useSelector((state: storeState) => state.mainFormSlice.image);
//   const imageFile = useSelector(
//     (state: storeState) => state.mainFormSlice.imageFile,
//   );
//   const dispatch = useDispatch();
//   const imageFileInput: React.MutableRefObject<HTMLInputElement | null> =
//     useRef(null);
//   const imageUrl = useSelector(
//     (state: storeState) => state.mainFormSlice.imageUrl,
//   );
//
//   if (type === "image")
//     return (
//       <div className={"flex grow flex-col gap-3"}>
//         <input
//           onChange={(e) => {
//             if (
//               imageFileInput?.current !== null &&
//               imageFileInput?.current?.value !== null &&
//               imageFileInput?.current?.value !== ""
//             )
//               imageFileInput.current.value = "";
//             if (imageFile !== null) dispatch(onInputField("imageFile", null));
//             fetch(e.target.value)
//               .then((response) => response.blob())
//               .then((blob) => {
//                 const file = new File([blob], "image.jpg", {
//                   type: "image/jpeg",
//                 });
//
//                 dispatch(onInputField("image", URL.createObjectURL(file)));
//                 dispatch(onInputField("imageError", null));
//               })
//               .catch(() => {
//                 dispatch(onInputField("image", ""));
//                 dispatch(
//                   onInputField(
//                     "imageError",
//                     "Unable to get the image, please try uploading it.",
//                   ),
//                 );
//               });
//
//             dispatch(onInputField("imageUrl", e.target.value));
//           }}
//           className={
//             className +
//             " grow rounded-sm pl-4 text-[1.2rem] font-bold text-stone-900 placeholder:italic focus:outline-hidden focus:ring-3 focus:ring-gray-500"
//           }
//           value={imageUrl}
//           placeholder={placeholder}
//         />
//         <input
//           ref={imageFileInput}
//           type={"file"}
//           accept={".jpg, .png, .webp"}
//           onChange={(e: ChangeEvent<HTMLInputElement>) => {
//             if (e?.target && e.target.files && e.target.files.length > 0) {
//               // Continue processing the file...
//
//               const file = e?.target?.files[0];
//               if (file) {
//                 const reader = new FileReader();
//                 reader.onload = function (e) {
//                   // Set the src attribute of the image to the uploaded file data
//                   if (e?.target?.result) {
//                     console.log();
//                     dispatch(onInputField("imageError", null));
//                     dispatch(onInputField("imageFile", e.target.result));
//                     dispatch(onInputField("image", e.target.result));
//                   }
//                 };
//                 reader.readAsDataURL(file);
//               }
//
//               console.log(e.target.files[0]);
//             }
//           }}
//         />
//       </div>
//     );
//
//   if (type !== "checkbox")
//     return (
//       <input
//         onChange={(e) => {
//           const value = e.target.value;
//           if (
//             (type === "number" && typeof Number(value) !== "number") ||
//             (inputFor !== "cardNum" &&
//               inputFor !== "set" &&
//               value.startsWith("0"))
//           )
//             return;
//           if (maxNum && Number(value) > maxNum) return;
//           if (maxCharNum && value.length > maxCharNum) return;
//           dispatch(onInputField(inputFor, value));
//         }}
//         className={
//           className +
//           " grow rounded-sm pl-4 text-[1.2rem] font-bold text-stone-900 placeholder:italic focus:outline-hidden focus:ring-3 focus:ring-gray-500"
//         }
//         value={stateValue as string | number | readonly string[] | undefined}
//         placeholder={placeholder}
//         type={type}
//       />
//     );
//   if (type === "checkbox")
//     return (
//       <input
//         type="checkbox"
//         checked={stateValue as boolean | undefined}
//         className={"h-5 w-5"}
//         onChange={() => {
//           dispatch(onInputField(inputFor, !stateValue));
//         }}
//         disabled={disabled}
//       />
//     );
// }
//
// type a = keyof state;
