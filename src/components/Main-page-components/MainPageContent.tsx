import Image from "next/image";
import MainPagePricing from "@/components/Main-page-components/MainPagePricing";
import MainPageOverview from "@/components/Main-page-components/MainPageOverview";
const cardBackground = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/cards-summer.webp`;

export default function MainPageContent() {
  return (
    <div>
      <div
        className={
          "relative mt-40 grid w-screen gap-32 sm:mt-0 sm:gap-40 md:gap-48 lg:gap-64"
        }
      >
        <div className="text-content relative z-500 flex flex-col items-center">
          <h2
            className={
              "mb-8 max-w-5xl px-4! text-center text-[32px] leading-tight font-semibold md:text-[3.75rem]"
            }
          >
            Dive into the adventure-filled universe of One Piece
          </h2>
          <p
            className={
              "mb-8 max-w-5xl px-8! text-center text-base leading-normal font-light sm:text-2xl dark:text-neutral-400"
            }
          >
            Bring your favorite characters, events, and strategies to life with
            our intuitive card creation tool.
          </p>
          <Image
            className={"h-auto max-w-[80%] lg:max-w-[817.594px]"}
            src={cardBackground}
            width={10092}
            height={5610}
            alt={"Image containing 3 cards of 3 One Piece Characters"}
            sizes={"(max-width: 1400px) 100vw, 1400px"}
            priority={true}
          />
        </div>
        <MainPagePricing />
        <MainPageOverview />
        <div className="bg-img absolute"></div>
      </div>
    </div>
  );
}
