import Link from "next/link";

export default function MainPageFooter() {
  return (
    <footer className={"mb-32"}>
      <div
        className={
          "flex items-center gap-4 px-8! text-center sm:gap-8 dark:text-neutral-400"
        }
      >
        <Link
          href={"/refund-policy"}
          className={"transition-all duration-100 dark:hover:text-neutral-100"}
        >
          Refund Policy
        </Link>
        <Link
          href={"/terms-and-conditions"}
          className={"transition-all duration-100 dark:hover:text-neutral-100"}
        >
          Terms And Conditions
        </Link>
        <Link
          href={"/privacy-policy"}
          className={"transition-all duration-100 dark:hover:text-neutral-100"}
        >
          Privacy Policy
        </Link>
      </div>
    </footer>
  );
}
