import Link from "next/link";

export default function MainPageHeroSection() {
  return (
    <div className={"relative w-screen overflow-hidden py-[10rem]!"}>
      <div className="text-content relative z-500 flex flex-col items-center">
        <h1
          className={
            "mb-10 max-w-[18ch] px-4! text-center text-4xl/[1.1] leading-tight font-semibold md:text-6xl/[1.1] lg:text-7xl/[1.1]"
          }
        >
          Ultimate TCG Card Maker
        </h1>

        <p
          className={
            "text-astro-gray-100 mb-8 max-w-4xl px-2! text-center text-lg leading-normal md:px-8! md:text-2xl"
          }
        >
          Ultimate TCG Card Maker is your one-stop solution for crafting your
          very own custom cards for the thrilling world of One Piece TCG!
        </p>
        <Link
          className={
            "tap rounded-[5rem] bg-neutral-950 px-8! py-4! text-xl font-bold text-neutral-100 transition-all duration-200 target:bg-neutral-800 focus-within:bg-neutral-800 hover:bg-neutral-800 focus:bg-neutral-800 focus-visible:bg-neutral-800 active:bg-neutral-800 dark:bg-white dark:text-stone-800 dark:target:bg-stone-200 dark:focus-within:bg-stone-300 dark:hover:bg-stone-300 dark:focus:bg-stone-300 dark:focus-visible:bg-stone-300 dark:active:bg-stone-300"
          }
          href={"/one-piece/character"}
        >
          Create Cards
        </Link>
        <Link
          href={"https://spadepiratesdesigns.com"}
          className={
            "mt-8 max-w-4xl px-2! text-center text-xl leading-normal text-neutral-950 underline underline-offset-4 transition-all duration-200 target:text-neutral-500 focus-within:text-neutral-500 hover:text-neutral-500 focus:text-neutral-500 focus-visible:text-neutral-500 active:text-neutral-500 md:px-8! dark:text-stone-200 dark:target:text-stone-200 dark:focus-within:text-stone-300 dark:hover:text-stone-300 dark:focus:text-stone-300 dark:focus-visible:text-stone-300 dark:active:text-stone-300"
          }
        >
          Check out some amazing playmats from SpadePiratesDesigns!
        </Link>
      </div>
      <div className="bg-head absolute h-full"></div>
    </div>
  );
}
