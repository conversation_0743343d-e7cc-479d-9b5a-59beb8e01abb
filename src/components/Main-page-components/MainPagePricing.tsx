import PricingCard from "@/components/PricingCard";

export default function MainPagePricing() {
  return (
    <div className="text-content relative z-500 flex flex-col items-center">
      <h2
        className={
          "mb-8 max-w-5xl px-4! text-center text-[32px] leading-tight font-semibold md:text-[3.75rem]"
        }
      >
        Pricing
      </h2>
      <div
        className={
          "flex max-w-3xl flex-col justify-center gap-4 px-4! sm:flex-row sm:flex-wrap md:min-w-[37em]"
        }
      >
        <PricingCard
          title={"Free trial"}
          featureList={["1 generated image"]}
          buttonLink={"/one-piece/character"}
          buttonText={"Create cards"}
          className={"basis-1/3 sm:basis-1/3"}
        />
        <PricingCard
          title={"Pro"}
          featureList={["Unlimited image generations", "Removed watermark"]}
          buttonLink={"/account"}
          buttonText={"Subscribe"}
          price={"5$ / month"}
          className={"basis-1/3 overflow-visible sm:basis-1/3"}
        />
        <PricingCard
          title={"Creator"}
          featureList={[
            "Unlimited image generations",
            "Removed watermark",
            "Option to generate print ready images",
          ]}
          buttonLink={"/account"}
          buttonText={"Subscribe"}
          price={"15$ / month"}
          className={"basis-3/3 overflow-visible sm:basis-full"}
        />
      </div>
    </div>
  );
}
