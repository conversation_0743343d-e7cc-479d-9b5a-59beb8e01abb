"use client";
// import Link from "next/link";
//
//
// <button className={"rounded-sm bg-white px-2 py-1 font-bold"}>
//     <Link
//         className={"text-xl text-stone-800"}
//         href={"/one-piece/character"}
//     >
//         Create a One Piece Card
//     </Link>
// </button>

import { Button } from "@mantine/core";
import { ReactNode } from "react";

export default function MyButton({ children }: { children: ReactNode }) {
  return (
    <Button color={"white"} variant={"filled"}>
      {children}
    </Button>
  );
}
