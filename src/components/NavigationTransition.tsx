"use client";

import { useEffect, useState } from "react";
import { usePathname } from "next/navigation";

export default function NavigationTransition({
  children,
  fallback = null
}: {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}) {
  const [isReady, setIsReady] = useState(false);
  const pathname = usePathname();

  useEffect(() => {
    setIsReady(false);
    const timer = requestAnimationFrame(() => {
      requestAnimationFrame(() => {
        setIsReady(true);
      });
    });

    return () => cancelAnimationFrame(timer);
  }, [pathname]);

  if (!isReady) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}