"use client";
import { ReactSVG } from "react-svg";
import React from "react";
import { cn } from "@/lib/utils";
import { LeaderBorder, ColorObj, BlendMode } from "@/types";
import { HexColor } from "@/components/CardElement-components/card-elements/characterBorderTypes";

export default function OnePieceLeaderBorderReactSVGWithGradientOP({
  printReady,
  printReadySrc,
  standardSrc,
  colorArray = [],
  className = "",
  leaderBorder,
  style,
  blendMode,
}: {
  printReady: boolean;
  printReadySrc: string;
  standardSrc: string;
  colorArray?: ColorObj[][] | HexColor[];
  className?: string;
  leaderBorder: LeaderBorder;
  style?: React.CSSProperties;
  blendMode?: BlendMode;
}) {
  if (
    colorArray.length >= 3 ||
    (colorArray.length === 2 && colorArray[0] === colorArray[1]) ||
    (colorArray.length === 1 && typeof colorArray[0] === "string")
  ) {
    blendMode = "";
  }

  const gradientId = React.useMemo(() => crypto.randomUUID(), []);

  // Check if colorArray is ColorObj[][] or HexColor[]
  const isColorObjArray = colorArray.length > 0 && Array.isArray(colorArray[0]);

  // For HexColor[], apply blend mode to entire SVG. For ColorObj[][], apply to layers only
  const combinedStyle = React.useMemo(
    () => ({
      ...style,
      ...(!isColorObjArray && blendMode && { mixBlendMode: blendMode }),
    }),
    [style, blendMode, isColorObjArray],
  );

  return (
    <ReactSVG
      className={cn("h-full w-full", className)}
      style={combinedStyle}
      src={printReady ? printReadySrc : standardSrc}
      beforeInjection={(svg) => {
        if (colorArray.length === 0) return;

        if (isColorObjArray) {
          // Handle ColorObj[][] - create layered gradients
          const colorObjArrays = colorArray as ColorObj[][];

          // Create SVG defs
          const defs = document.createElementNS(
            "http://www.w3.org/2000/svg",
            "defs",
          );

          // Create gradient definitions for each layer
          colorObjArrays.forEach((colorObjs, arrayIndex) => {
            if (!colorObjs || colorObjs.length === 0) return;

            const gradientElement = document.createElementNS(
              "http://www.w3.org/2000/svg",
              "linearGradient",
            );
            gradientElement.setAttribute("id", `${gradientId}-${arrayIndex}`);

            // Set gradient direction - horizontal (to match CSS "to right")
            if (leaderBorder === "rainbow") {
              // 135-degree angle (top-left to bottom-right)
              gradientElement.setAttribute("x1", "0%");
              gradientElement.setAttribute("y1", "0%");
              gradientElement.setAttribute("x2", "139.5%");
              gradientElement.setAttribute("y2", "0%");
              gradientElement.setAttribute("gradientTransform", "rotate(54.5)");
            } else if (colorObjArrays.length > 3) {
              // 135-degree angle (top-left to bottom-right)
              gradientElement.setAttribute("x1", "0%");
              gradientElement.setAttribute("y1", "0%");
              gradientElement.setAttribute("x2", "170.5%");
              gradientElement.setAttribute("y2", "0%");
              gradientElement.setAttribute("gradientTransform", "rotate(45.5)");
            } else {
              gradientElement.setAttribute("x1", "0%");
              gradientElement.setAttribute("y1", "0%");
              gradientElement.setAttribute("x2", "100%");
              gradientElement.setAttribute("y2", "0%");
              gradientElement.setAttribute("gradientUnits", "userSpaceOnUse");
            }

            // Add stops for this gradient
            colorObjs.forEach((colorObj) => {
              if (colorObj.stops) {
                colorObj.stops.forEach((stopPercentage) => {
                  const stop = document.createElementNS(
                    "http://www.w3.org/2000/svg",
                    "stop",
                  );
                  stop.setAttribute("offset", stopPercentage);
                  stop.setAttribute("stop-color", colorObj.color);
                  gradientElement.appendChild(stop);
                });
              }
            });

            defs.appendChild(gradientElement);
          });

          svg.prepend(defs);

          // Get all paths/shapes that should be filled
          const fillableElements = svg.querySelectorAll(
            '[fill]:not([fill="none"])',
          );

          // For each gradient layer, clone the elements and apply the gradient
          colorObjArrays.forEach((colorObjs, arrayIndex) => {
            if (!colorObjs || colorObjs.length === 0) return;

            fillableElements.forEach((element) => {
              if (arrayIndex === 0) {
                // First layer: modify original elements
                element.setAttribute(
                  "fill",
                  `url(#${gradientId}-${arrayIndex})`,
                );
              } else {
                // Additional layers: clone and stack with blend mode
                const clone = element.cloneNode(true) as Element;
                clone.setAttribute("fill", `url(#${gradientId}-${arrayIndex})`);

                // Apply blend mode to the cloned layer, not the entire SVG
                if (blendMode) {
                  clone.setAttribute("style", `mix-blend-mode: ${blendMode};`);
                }

                element.parentNode?.appendChild(clone);
              }
            });
          });
        } else {
          // Handle HexColor[] - original single gradient logic
          const processedColors = colorArray as HexColor[];
          const svgColorArray =
            processedColors.length === 1
              ? [processedColors[0], processedColors[0]]
              : processedColors;

          const stops = [];

          // Use automatic stop calculation (original logic)
          if (svgColorArray.length === 2) {
            stops.push(`${svgColorArray[0]} 0%`);
            stops.push(`${svgColorArray[0]} 50%`);
            stops.push(`${svgColorArray[1]} 50%`);
            stops.push(`${svgColorArray[1]} 100%`);
          } else if (svgColorArray.length === 3) {
            stops.push(`${svgColorArray[0]} 0%`);
            stops.push(`${svgColorArray[1]} 50%`);
            stops.push(`${svgColorArray[2]} 100%`);
          } else {
            for (let i = 0; i < svgColorArray.length; i++) {
              const percentage = (i / (svgColorArray.length - 1)) * 100;
              stops.push(`${svgColorArray[i]} ${percentage}%`);
            }
          }

          // Create SVG defs and gradient
          const defs = document.createElementNS(
            "http://www.w3.org/2000/svg",
            "defs",
          );

          const gradientElement = document.createElementNS(
            "http://www.w3.org/2000/svg",
            "linearGradient",
          );
          gradientElement.setAttribute("id", gradientId);

          if (leaderBorder === "rainbow") {
            // 135-degree angle (top-left to bottom-right)
            gradientElement.setAttribute("x1", "0%");
            gradientElement.setAttribute("y1", "0%");
            gradientElement.setAttribute("x2", "139.5%");
            gradientElement.setAttribute("y2", "0%");
            gradientElement.setAttribute("gradientTransform", "rotate(54.5)");
          } else if (svgColorArray.length > 3) {
            // 135-degree angle (top-left to bottom-right)
            gradientElement.setAttribute("x1", "0%");
            gradientElement.setAttribute("y1", "0%");
            gradientElement.setAttribute("x2", "170.5%");
            gradientElement.setAttribute("y2", "0%");
            gradientElement.setAttribute("gradientTransform", "rotate(45.5)");
          } else {
            // Horizontal gradient for 1-2 colors
            gradientElement.setAttribute("x1", "0%");
            gradientElement.setAttribute("y1", "0%");
            gradientElement.setAttribute("x2", "100%");
            gradientElement.setAttribute("y2", "0%");
          }

          gradientElement.setAttribute("gradientUnits", "userSpaceOnUse");

          // Add all stop elements
          stops.forEach((stopStr) => {
            const [color, offset] = stopStr.split(" ");
            const stop = document.createElementNS(
              "http://www.w3.org/2000/svg",
              "stop",
            );
            if (offset) {
              stop.setAttribute("offset", offset);
            }
            stop.setAttribute("stop-color", color);
            gradientElement.appendChild(stop);
          });

          defs.appendChild(gradientElement);
          svg.prepend(defs);

          const className = `gradient-fill-${gradientId}`;
          svg.classList.add(className);

          const style = document.createElementNS(
            "http://www.w3.org/2000/svg",
            "style",
          );
          style.textContent = `
            .${className} * {
              fill: url(#${gradientId}) !important;
            }
            .${className} [fill="none"] {
              fill: none !important;
            }
          `;
          svg.appendChild(style);
        }
      }}
    />
  );
}
