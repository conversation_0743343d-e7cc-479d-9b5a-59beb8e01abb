"use client";
import { ReactSVG } from "react-svg";
import React from "react";
import { cn } from "@/lib/utils";
import { HexColor } from "@/components/CardElement-components/card-elements/characterBorderTypes";
import { BlendMode, ColorObj } from "@/types";

export default function OnePieceReactSvgWithGradient({
  printReady,
  printReadySrc,
  standardSrc,
  colorArray = [],
  className = "",
  style,
  blendMode,
  gradientDirection,
  flatBlendFor2Colors = true,
  id,
}: {
  printReady: boolean;
  printReadySrc: string;
  standardSrc: string;
  colorArray?: HexColor[] | ColorObj[][];
  className?: string;
  style?: React.CSSProperties;
  blendMode?: BlendMode;
  gradientDirection?: "horizontal" | "vertical";
  flatBlendFor2Colors?: boolean;
  id?: string;
}) {
  // Reset blend mode for 3+ colors
  if (colorArray.length >= 3) {
    blendMode = "";
  }

  const gradientId = React.useMemo(() => crypto.randomUUID(), []);

  // Apply blend mode to entire SVG for HexColor[] (after conversion from ColorObj[][])
  const combinedStyle = React.useMemo(
    () => ({
      ...style,
      ...(blendMode && { mixBlendMode: blendMode }),
    }),
    [style, blendMode],
  );

  return (
    <ReactSVG
      className={cn("h-full w-full", className)}
      src={printReady ? printReadySrc : standardSrc}
      style={combinedStyle}
      id={id}
      beforeInjection={(svg) => {
        if (colorArray.length === 0) return;
        let svgColorArray: HexColor[];

        if (isHexColorArray(colorArray)) {
          svgColorArray = colorArray;
        } else {
          svgColorArray = colorArray.map((colorObj) => colorObj[0].color);
        }

        // Create gradient stops based on color count (like generateMultiColorGradient)
        const stops = [];

        if (svgColorArray.length === 2 && flatBlendFor2Colors) {
          // For 2 colors, split at 50%
          stops.push(`${svgColorArray[0]} 0%`);
          stops.push(`${svgColorArray[0]} 50%`);
          stops.push(`${svgColorArray[1]} 50%`);
          stops.push(`${svgColorArray[1]} 100%`);
        } else if (svgColorArray.length === 3) {
          // For 3 colors, create 33.33% segments
          stops.push(`${svgColorArray[0]} 0%`);
          stops.push(`${svgColorArray[1]} 50%`);
          stops.push(`${svgColorArray[2]} 100%`);
        } else {
          // For 3+ colors, evenly distribute
          for (let i = 0; i < svgColorArray.length; i++) {
            const percentage = (i / (svgColorArray.length - 1)) * 100;
            stops.push(
              `${svgColorArray[i]} ${percentage ? `${percentage}%` : ""}`,
            );
          }
        }

        // Create SVG defs and style
        const defs = document.createElementNS(
          "http://www.w3.org/2000/svg",
          "defs",
        );

        const gradientElement = document.createElementNS(
          "http://www.w3.org/2000/svg",
          "linearGradient",
        );
        if (gradientDirection === "vertical") {
          gradientElement.setAttribute("gradientTransform", "rotate(90)");
        }

        gradientElement.setAttribute("id", gradientId);

        // Set gradient direction based on color count
        if (svgColorArray.length > 3) {
          // 135-degree angle (top-left to bottom-right)
          gradientElement.setAttribute("x1", "0%");
          gradientElement.setAttribute("y1", "0%");
          gradientElement.setAttribute("x2", "170.5%");
          gradientElement.setAttribute("y2", "0%");
          gradientElement.setAttribute("gradientTransform", "rotate(45.5)");
        } else if (gradientDirection === "vertical") {
          // Horizontal gradient for 1-2 colors
          gradientElement.setAttribute("x1", "0%");
          gradientElement.setAttribute("y1", "0%");
          gradientElement.setAttribute("x2", "150%");
          gradientElement.setAttribute("y2", "-20%");
        } else {
          // Horizontal gradient for 1-2 colors
          gradientElement.setAttribute("x1", "0%");
          gradientElement.setAttribute("y1", "0%");
          gradientElement.setAttribute("x2", "100%");
          gradientElement.setAttribute("y2", "0%");
        }

        gradientElement.setAttribute("gradientUnits", "userSpaceOnUse");

        // Add all stop elements
        stops.forEach((stopStr) => {
          const [color, offset] = stopStr.split(" ");
          const stop = document.createElementNS(
            "http://www.w3.org/2000/svg",
            "stop",
          );
          if (offset) {
            stop.setAttribute("offset", offset);
          }
          stop.setAttribute("stop-color", color);
          gradientElement.appendChild(stop);
        });

        defs.appendChild(gradientElement);
        svg.prepend(defs);

        const className = `gradient-fill-${gradientId}`;
        svg.classList.add(className);

        const style = document.createElementNS(
          "http://www.w3.org/2000/svg",
          "style",
        );
        style.textContent = `
          .${className} * {
            fill: url(#${gradientId}) !important;
          }
          .${className} [fill="none"] {
            fill: none !important;
          }
        `;
        svg.appendChild(style);
      }}
    />
  );
}

function isHexColorArray(arr: HexColor[] | ColorObj[][]): arr is HexColor[] {
  return typeof arr[0] === "string";
}
