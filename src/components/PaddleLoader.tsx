// "use client";
// import { useEffect, useState } from "react";
// import { initializePaddle, Paddle } from "@paddle/paddle-js";
//
// export default function PaddleLoader() {
//   const [paddle, setPaddle] = useState<Paddle>();
//   const paddleEnv = process.env.NEXT_PUBLIC_PADDLE_ENVIRONMENT as
//     | "sandbox"
//     | "production";
//   useEffect(() => {
//     initializePaddle({
//       environment: paddleEnv,
//       token: `${process.env.NEXT_PUBLIC_PADDLE_CLIENT_TOKEN}`,
//     }).then((paddleInstance: Paddle | undefined) => {
//       if (paddleInstance) {
//         setPaddle(paddleInstance);
//       }
//     });
//   }, [paddleEnv]);
//   const openCheckout = () => {
//     paddle?.Checkout.open({
//       items: [{ priceId: "PRICE_ID_GOES_HERE", quantity: 1 }],
//     });
//   };
//   return;
// }
