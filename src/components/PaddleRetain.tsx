"use client";

import { useEffect } from "react";
import { initializePaddle } from "@paddle/paddle-js";
import { createClient } from "@/utils/supabase/client";

import { getTransaction } from "@/helpers/getTransaction";

export default function PaddleRetain() {
  useEffect(() => {
    // Only proceed if we're in a browser environment
    if (typeof window === "undefined") return;

    const initializePaddleWithCustomer = async () => {
      try {
        let customerId: null | string = null;

        const supabase = createClient();

        const {
          data: { user },
        } = await supabase.auth.getUser();

        if (user) {
          const { data: formData, error } = await supabase
            .from("profiles")
            .select("paddle_transaction_ID")
            .eq("id", user.id)
            .single();
          if (formData?.paddle_transaction_ID) {
            const transaction = await getTransaction(
              formData?.paddle_transaction_ID,
            );

            customerId = transaction?.data?.customer_id;
          }
          if (error) {
            console.error(
              "Error fetching transaction ID or no transaction ID found:",
              error,
            );
            return;
          }
        }

        const paddleEnv = process.env.NEXT_PUBLIC_PADDLE_ENVIRONMENT as
          | "production"
          | "sandbox";

        await initializePaddle({
          environment: paddleEnv,
          token: process.env.NEXT_PUBLIC_PADDLE_CLIENT_TOKEN || "",

          pwCustomer: customerId
            ? {
                id: customerId,
              }
            : {},
        });

        // Retain demo
        /*        if (paddleInstance) {
          // Only try to use Retain in production environment
          if (paddleEnv === "production" && paddleInstance.Retain) {
            try {
              // Wrap in try/catch to handle potential errors
              paddleInstance.Retain.demo({ feature: "paymentRecovery" });
            } catch (error) {
              console.error("Error initializing Retain demo:", error);
            }
          } else {
            console.log("Retain is only available in production environment");
          }
        }*/
      } catch (error) {
        console.error("Error in Paddle initialization process:", error);
      }
    };

    initializePaddleWithCustomer();
  }, []);

  return null;
}
