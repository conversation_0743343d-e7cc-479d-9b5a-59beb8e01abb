"use client";

import posthog from "posthog-js";
import { PostHog<PERSON>rovider as P<PERSON>rovider } from "posthog-js/react";
import { useEffect } from "react";

import SuspendedPostHogPageView from "@/components/posthog/PostHogPageView";

const isDevelopment = process.env.NEXT_PUBLIC_NODE_ENV === "development";
// ||

// window.location.hostname.startsWith("dev.") ||
// window.location.hostname === "localhost" ||
// window.location.hostname === "127.0.0.1";

export function PostHogProvider({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    if (!isDevelopment) {
      posthog.init(process.env.NEXT_PUBLIC_POSTHOG_KEY!, {
        // POSTHOG direct
        api_host: `${process.env.NEXT_PUBLIC_POSTHOG_HOST}`,
        // POSTHOG /ingest ( our api route )
        // api_host: "/ingest",
        // POSTOGH behind proxy
        // ui_host: `${process.env.NEXT_PUBLIC_POSTHOG_HOST!.split(".i.").join(".")}`,
        capture_pageview: false, // We capture pageviews manually
        capture_pageleave: true, // Enable pageleave capture
      });
    }
  }, []);

  if (isDevelopment) {
    return <>{children}</>;
  }

  return (
    <PHProvider client={posthog}>
      <SuspendedPostHogPageView />
      {children}
    </PHProvider>
  );
}
