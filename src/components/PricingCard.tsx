"use client";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardHeader,
  CardTitle,
} from "@/components/ui/Card";
import { cn } from "@/lib/utils";

export default function PricingCard({
  title,
  featureList,
  buttonText,
  buttonLink,
  price = "",
  className = "",
  buttonClassName = "",
  remaining,
  recurring,
  onClick,
  loading = false,
}: {
  title: string;
  featureList: string[];
  buttonText: string;
  buttonLink: string;
  price?: string;
  className?: string;
  buttonClassName?: string;
  remaining?: { remaining: string; limit: string } | null;
  recurring?: string;
  onClick?: () => void;
  loading?: boolean;
}) {
  return (
    <Card className={`flex-1 gap-2 border shadow-sm ${className}`}>
      <CardHeader className="pb-0">
        <CardTitle className="text-xl font-medium">{title}</CardTitle>
        <p className="text-sm font-medium">
          {`${price} ${recurring ? `/ ${recurring}` : ""}`}
        </p>
        {remaining && (
          <p className="text-sm">
            {remaining.remaining} / {remaining.limit} remaining
          </p>
        )}
      </CardHeader>

      <CardContent>
        <ul className="mb-4 list-disc pl-5 text-sm">
          {featureList.map((feature, i) => (
            <li key={`${feature}-${i}`} className="mb-1">
              {feature}
            </li>
          ))}
        </ul>
      </CardContent>

      <CardFooter className="mt-auto">
        <Link href={buttonLink} className="w-full">
          <Button
            onClick={onClick}
            className={cn(
              "w-full bg-neutral-950 text-neutral-50 dark:bg-neutral-50 dark:text-neutral-950 dark:hover:bg-neutral-200",
              buttonClassName,
            )}
            disabled={loading}
            loading={loading}
          >
            {buttonText}
          </Button>
        </Link>
      </CardFooter>
    </Card>
  );
}
