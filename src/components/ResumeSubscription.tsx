"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { Subscription } from "@/types";
import { LoadingButton } from "@/components/ui/loading-button";

export default function ResumeSubscription({
  subDate,
  subStatus,
  subId,
  updatePaymentMethod,
  subscription,
}: {
  subDate: string | null;
  subStatus: string | null;
  subId: string | null;
  updatePaymentMethod: string | null;
  subscription: Subscription;
}) {
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  if (
    subscription &&
    subscription.status === "active" &&
    subscription.id?.startsWith("sub_") &&
    subscription.scheduledChange?.effective_at
  ) {
    return (
      <LoadingButton
        className="bg-green-600!"
        disabled={loading}
        loading={loading}
        onClick={async () => {
          setLoading(true);
          await fetch(`/api/paddle/resume-subscription`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Accept: "application/json",
            },
            body: JSON.stringify({
              subscriptionId: subId,
              subDate: subDate,
            }),
          });
          setLoading(false);
          router.refresh();
        }}
      >
        Resume subscription
      </LoadingButton>
    );
  }

  if (subStatus === "PAST_DUE" && updatePaymentMethod) {
    return (
      <LoadingButton
        className="bg-green-600!"
        disabled={loading}
        loading={loading}
        onClick={() => {
          router.push(updatePaymentMethod);
        }}
      >
        Resume subscription
      </LoadingButton>
    );
  }
  if (subDate && subStatus === "PAUSED") {
    return (
      <LoadingButton
        className="bg-green-600!"
        disabled={loading}
        loading={loading}
        onClick={async () => {
          setLoading(true);
          await fetch(`/api/paddle/resume-subscription`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Accept: "application/json",
            },
            body: JSON.stringify({
              subscriptionId: subId,
              subDate: subDate,
            }),
          });
          setLoading(false);
          router.refresh();
        }}
      >
        Resume subscription
      </LoadingButton>
    );
  }
  if (
    subDate &&
    subStatus === "CANCELLED" &&
    subId?.startsWith("sub_") &&
    subscription.status !== "canceled"
  ) {
    if (new Date(subDate) > new Date()) {
      return (
        <LoadingButton
          className="bg-green-600!"
          onClick={async () => {
            setLoading(true);
            await fetch(`/api/paddle/resume-subscription`, {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                Accept: "application/json",
              },
              body: JSON.stringify({
                subscriptionId: subId,
                subDate: subDate,
              }),
            });
            setLoading(false);
            router.refresh();
          }}
          disabled={loading}
          loading={loading}
        >
          Resume subscription
        </LoadingButton>
      );
    }
  }
}
