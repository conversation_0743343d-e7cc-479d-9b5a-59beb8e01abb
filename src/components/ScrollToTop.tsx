"use client";
import { IconArrowUp } from "@tabler/icons-react";
import { Button } from "@/components/ui/button";
import { useEffect, useState, useRef } from "react";

export default function ScrollToTop() {
  const [visible, setVisible] = useState(false);
  const visibleRef = useRef(visible);

  useEffect(() => {
    visibleRef.current = visible;
  }, [visible]);

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 0 && !visibleRef.current) {
        setVisible(true);
      } else if (window.scrollY === 0 && visibleRef.current) {
        setVisible(false);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  return (
    <div
      className={
        "fixed right-5 bottom-5 z-1000 flex items-center transition-opacity duration-300 " +
        (visible ? "opacity-100" : "opacity-0")
      }
    >
      <Button
        size="icon"
        variant="default"
        className={
          "scroll-to-top rounded-full bg-neutral-950 p-2 dark:bg-white"
        }
        onClick={scrollToTop}
      >
        <IconArrowUp
          className={"h-5 w-5 text-neutral-50 dark:text-neutral-950"}
        />
      </Button>
    </div>
  );
}
