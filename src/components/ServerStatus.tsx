"use client";

import React from "react";
import { useServerStatus } from "@/context/ServerStatusContext";

export default function ServerStatus() {
  const { serverConnectionError, isDismissed, setIsDismissed } =
    useServerStatus();

  // Return null if server is online or notification was dismissed
  if (!serverConnectionError || isDismissed) {
    return null;
  }

  // Only render the message if server is confirmed to be offline
  return (
    <div className="pointer-events-none fixed top-[4rem] right-0 left-0 z-999 flex justify-center">
      <div className="pointer-events-auto relative flex w-auto max-w-[90%] flex-col gap-2 rounded-sm border border-orange-400 bg-orange-100/90 px-4! py-3! text-orange-700 shadow-md backdrop-blur-md dark:border-orange-800 dark:bg-orange-900/80 dark:text-orange-100">
        <div className={"flex justify-between"}>
          <strong className="font-bold sm:min-w-fit">Server Offline: </strong>
          <button
            onClick={() => setIsDismissed(true)}
            className="top-1 right-1 flex h-6 w-6 items-center justify-center rounded-full transition-colors hover:bg-orange-200 dark:hover:bg-orange-800"
            aria-label="Close notification"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>
        <p className={"flex flex-col gap-1"}>
          <span className="block sm:inline">
            The card generation server is currently unavailable. You can
            download the card <strong>preset</strong> to save your work and try
            again later.
          </span>
          <span>
            Please contact <strong><EMAIL></strong> if the issue
            persists, try reloading to see if the server is back online.
          </span>
        </p>
      </div>
    </div>
  );
}
