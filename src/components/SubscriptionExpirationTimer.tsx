"use client";
import React from "react";
import { Subscription } from "@/types";

export default function SubscriptionExpirationTimer({
  subscription,
}: {
  subscription: Subscription;
}) {
  if (
    subscription &&
    subscription.status === "active" &&
    subscription.id?.startsWith("sub_") &&
    subscription.scheduledChange?.effective_at
  ) {
    return (
      <p>
        Your plan will be active until:{" "}
        {`${
          new Date(subscription.scheduledChange?.effective_at).getDate() < 10
            ? `0${new Date(subscription.scheduledChange?.effective_at).getDate()}`
            : new Date(subscription.scheduledChange?.effective_at).getDate()
        }-${
          new Date(subscription.scheduledChange?.effective_at).getMonth() + 1 <
          10
            ? `0${new Date(subscription.scheduledChange?.effective_at).getMonth() + 1}`
            : new Date(subscription.scheduledChange?.effective_at).getMonth() +
              1
        }-${new Date(subscription.scheduledChange?.effective_at).getFullYear()}`}
      </p>
    );
  }

  return null;
}
