"use client";

import { useEffect, useState, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { showCustomToast, dismissToast } from "@/lib/toast";
import { RefreshCw } from "lucide-react";

export default function UpdateNotification() {
  const [updateAvailable, setUpdateAvailable] = useState(false);
  const [registration, setRegistration] =
    useState<ServiceWorkerRegistration | null>(null);
  const [toastId, setToastId] = useState<string | number | null>(null);

  const handleRefresh = useCallback(() => {
    try {
      // Dismiss the toast
      if (toastId) {
        dismissToast(toastId);
        setToastId(null);
      }

      // Tell service worker to skip waiting and activate
      if (registration?.waiting) {
        try {
          registration.waiting.postMessage({ type: "SKIP_WAITING" });

          // Listen for the service worker to take control, then reload
          const handleControllerChange = () => {
            try {
              window.location.reload();
            } catch {
              // Fallback: force reload
              // window.location.href = window.location.href;
            }
          };

          navigator.serviceWorker.addEventListener(
            "controllerchange",
            handleControllerChange,
            { once: true },
          );
        } catch {
          // If messaging fails, just reload
          window.location.reload();
        }
      } else {
        window.location.reload();
      }
    } catch {
      // Ultimate fallback: force page refresh
      try {
        window.location.reload();
      } catch {
        // window.location.href = window.location.href;
      }
    }
  }, [toastId, registration]);

  const showUpdateNotification = useCallback(() => {
    try {
      if (toastId) {
        return;
      }

      const id = showCustomToast(
        <div className="flex w-full items-center justify-between">
          <div className="flex flex-col">
            <span className="font-semibold">New version available!</span>
            <span className="text-muted-foreground text-sm">
              Save your work by downloading your card preset.
            </span>
          </div>
          <Button
            size="sm"
            onClick={handleRefresh}
            className="ml-4 flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Refresh
          </Button>
        </div>,
        {
          duration: Infinity,
          closeButton: true,
          onDismiss: () => {
            setUpdateAvailable(false);
            setToastId(null);
          },
        },
      );

      setToastId(id);
    } catch {
      // Silently fail if toast can't be shown
      console.log("Failed to show update notification");
    }
  }, [toastId, handleRefresh]);

  const registerServiceWorker = useCallback(async (): Promise<
    (() => void) | null
  > => {
    try {
      // Double-check service worker support
      if (!navigator.serviceWorker) {
        return null;
      }

      const reg = await navigator.serviceWorker.register("/sw.js", {
        scope: "/",
      });

      if (!reg) {
        return null;
      }

      setRegistration(reg);

      // AUTO-ACTIVATE: If there's a waiting worker on page load, activate it immediately
      try {
        if (reg.waiting) {
          reg.waiting.postMessage({ type: "SKIP_WAITING" });
        }
      } catch {
        // Ignore messaging errors
      }

      const handleUpdateFound = () => {
        try {
          const newWorker = reg.installing;

          if (newWorker) {
            const handleStateChange = () => {
              try {
                if (newWorker.state === "installed") {
                  if (navigator.serviceWorker.controller) {
                    setUpdateAvailable(true);
                  }
                }
              } catch {
                // Ignore state check errors
              }
            };

            try {
              newWorker.addEventListener("statechange", handleStateChange);
            } catch {
              // Ignore event listener errors
            }
          }
        } catch {
          // Ignore update handling errors
        }
      };

      // Set up update listener for NEW updates that arrive while page is open
      try {
        reg.addEventListener("updatefound", handleUpdateFound);
      } catch {
        // Ignore event listener errors
      }

      // Set up periodic update checking with error handling
      const updateInterval = setInterval(() => {
        try {
          if (reg && typeof reg.update === "function") {
            reg.update().catch(() => {
              // Silently ignore update check failures
            });
          }
        } catch {
          // Ignore update check errors
        }
      }, 60000);

      // Return cleanup function
      return () => {
        try {
          clearInterval(updateInterval);
          if (reg && typeof reg.removeEventListener === "function") {
            reg.removeEventListener("updatefound", handleUpdateFound);
          }
        } catch {
          // Ignore cleanup errors
        }
      };
    } catch {
      // Silently fail service worker registration
      return null;
    }
  }, []);

  useEffect(() => {
    let cleanup: (() => void) | null = null;

    // Check for service worker support before attempting registration
    if (
      typeof window !== "undefined" &&
      "serviceWorker" in navigator &&
      navigator.serviceWorker
    ) {
      registerServiceWorker()
        .then((cleanupFn) => {
          cleanup = cleanupFn;
        })
        .catch(() => {
          // Silently handle registration promise rejection
        });
    }

    // Development test functionality
    if (process.env.NODE_ENV === "development") {
      const handleTestUpdate = () => {
        try {
          setUpdateAvailable(true);
        } catch {
          // Ignore test update errors
        }
      };

      try {
        window.addEventListener("manual-update-test", handleTestUpdate);
      } catch {
        // Ignore event listener errors in development
      }

      return () => {
        try {
          if (cleanup) cleanup();
          window.removeEventListener("manual-update-test", handleTestUpdate);
        } catch {
          // Ignore cleanup errors
        }
      };
    }

    return () => {
      try {
        if (cleanup) cleanup();
      } catch {
        // Ignore cleanup errors
      }
    };
  }, [registerServiceWorker]);

  useEffect(() => {
    if (updateAvailable && !toastId) {
      // Add a small delay to ensure DOM is ready
      const timeoutId = setTimeout(() => {
        showUpdateNotification();
      }, 50);

      return () => {
        try {
          clearTimeout(timeoutId);
        } catch {
          // Ignore timeout cleanup errors
        }
      };
    }
  }, [updateAvailable, toastId, showUpdateNotification]);

  useEffect(() => {
    return () => {
      try {
        if (toastId) {
          dismissToast(toastId);
        }
      } catch {
        // Ignore toast dismissal errors
      }
    };
  }, [toastId]);

  return null;
}
