"use client"

import * as React from "react"
import { cn } from "@/lib/utils"

const List = React.forwardRef<
  HTMLUListElement,
  React.HTMLAttributes<HTMLUListElement> & {
    listStyleType?: "disc" | "circle" | "square" | "decimal" | "none" | string
    withPadding?: boolean
  }
>(({ className, listStyleType = "none", withPadding = false, ...props }, ref) => {
  return (
    <ul
      ref={ref}
      className={cn(
        "text-foreground",
        withPadding && "pl-6",
        listStyleType !== "none" && `list-${listStyleType}`,
        className
      )}
      style={
        listStyleType !== "disc" && 
        listStyleType !== "circle" && 
        listStyleType !== "square" && 
        listStyleType !== "decimal" && 
        listStyleType !== "none" 
          ? { listStyleType } 
          : undefined
      }
      {...props}
    />
  )
})
List.displayName = "List"

const ListItem = React.forwardRef<
  HTMLLIElement,
  React.HTMLAttributes<HTMLLIElement>
>(({ className, ...props }, ref) => {
  return (
    <li
      ref={ref}
      className={cn("my-1", className)}
      {...props}
    />
  )
})
ListItem.displayName = "ListItem"

export { List, ListItem }
