"use client"

import * as React from "react"
import { cn } from "@/lib/utils"

interface TitleProps extends React.HTMLAttributes<HTMLHeadingElement> {
  order?: 1 | 2 | 3 | 4 | 5 | 6
}

const Title = React.forwardRef<HTMLHeadingElement, TitleProps>(
  ({ className, order = 1, ...props }, ref) => {
    const Component = `h${order}` as const
    
    const styles = {
      1: "scroll-m-20 text-4xl font-extrabold tracking-tight lg:text-5xl",
      2: "scroll-m-20 text-3xl font-semibold tracking-tight",
      3: "scroll-m-20 text-2xl font-semibold tracking-tight",
      4: "scroll-m-20 text-xl font-semibold tracking-tight",
      5: "scroll-m-20 text-lg font-semibold tracking-tight",
      6: "scroll-m-20 text-base font-semibold tracking-tight",
    }
    
    return (
      <Component
        ref={ref}
        className={cn(styles[order], className)}
        {...props}
      />
    )
  }
)

Title.displayName = "Title"

export { Title }
