"use client";

import React, { createContext, useContext, useState, ReactNode } from "react";

interface ServerStatusContextType {
  serverConnectionError: boolean;
  setServerConnectionError: (value: boolean) => void;
  isDismissed: boolean;
  setIsDismissed: (value: boolean) => void;
}

const ServerStatusContext = createContext<ServerStatusContextType | undefined>(
  undefined,
);

export function ServerStatusProvider({
  children,
  initialServerStatus = true,
}: {
  children: ReactNode;
  initialServerStatus?: boolean;
}) {
  const [serverConnectionError, setServerConnectionError] =
    useState(!initialServerStatus);
  const [isDismissed, setIsDismissed] = useState(false);

  return (
    <ServerStatusContext.Provider
      value={{
        serverConnectionError,
        setServerConnectionError,
        isDismissed,
        setIsDismissed,
      }}
    >
      {children}
    </ServerStatusContext.Provider>
  );
}

export function useServerStatus() {
  const context = useContext(ServerStatusContext);
  if (context === undefined) {
    throw new Error(
      "useServerStatus must be used within a ServerStatusProvider",
    );
  }
  return context;
}
