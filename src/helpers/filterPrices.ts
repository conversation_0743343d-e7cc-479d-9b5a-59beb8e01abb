import { MyPaddlePriceArray } from "@/types/paddleTypes";

export function filterPrices(productPrices: MyPaddlePriceArray) {
  const proPrices = productPrices.filter((price) =>
    price.productName.includes("Pro"),
  );
  const creatorPrices = productPrices.filter(
    (price) =>
      price.productName.includes("Creator") || price.name.includes("Lifetime"),
  );
  return { proPrices, creatorPrices };
}
