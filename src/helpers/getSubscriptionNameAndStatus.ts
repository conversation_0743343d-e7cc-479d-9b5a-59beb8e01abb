import { PaddleSubscription, PaddleTransaction } from "@/types";
import { SUBSCRIPTION_PLAN_NAMES } from "@/app/constants";
import { cache } from "react";
import { createClient } from "@/utils/supabase/server";

export default async function getSubscriptionNameAndStatus(): Promise<{
  active: boolean;
  subscriptionName: string;
  updatePaymentMethod: string | null;
  lifetime?: boolean | undefined;
  status:
    | "active"
    | "canceled"
    | "paused"
    | "past_due"
    | "trialing"
    | undefined;
  id: string | undefined;
  subscriptionPlanName: string;
  scheduledChange: null | {
    action: string;
    effective_at: string;
    resume_at: string | null;
  };
  cardTokens: number | null | undefined;
}> {
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (user) {
    const {
      data: formData,
      // error,
      // status,
    } = await supabase
      .from("profiles")
      .select(
        `subscription_status,subscription_ID,paddle_transaction_ID,subscription_last_payment_date,card_tokens`,
      )
      .eq("id", user?.id ?? "")
      .single();
    const subscriptionID = formData?.subscription_ID;
    const transactionID = formData?.paddle_transaction_ID;
    const lastPaymentDate = formData?.subscription_last_payment_date;
    const subscriptionStatus = formData?.subscription_status;
    // Check for PayPal subs

    if (user && subscriptionStatus === "LIFETIME") {
      return {
        id: undefined,
        active: true,
        subscriptionName: "LIFETIME",
        subscriptionPlanName: "",
        lifetime: true,
        updatePaymentMethod: null,
        status: "active",
        scheduledChange: null,
        cardTokens: formData?.card_tokens,
      };
    }
    if (
      user &&
      formData?.subscription_ID &&
      formData.subscription_ID.startsWith("I-")
    ) {
      if (subscriptionStatus === "ACTIVE") {
        return {
          id: formData.subscription_ID,
          active: true,
          subscriptionName: "",
          subscriptionPlanName: "Pro 1-month",
          scheduledChange: null,
          updatePaymentMethod: null,
          status: "active",
          cardTokens: formData?.card_tokens,
        };
      }
      if (lastPaymentDate) {
        const subscriptionExpirationDate = new Date(lastPaymentDate);
        subscriptionExpirationDate.setMonth(
          new Date(subscriptionExpirationDate).getMonth() + 1,
        );

        if (user && subscriptionStatus === "CANCELLED") {
          return {
            id: formData.subscription_ID,
            active: false,
            subscriptionName: "",
            subscriptionPlanName: "",
            updatePaymentMethod: null,
            status: "canceled",
            scheduledChange: null,
            cardTokens: formData?.card_tokens,
          };
        }
        if (
          user &&
          subscriptionStatus === "PAUSED" &&
          new Date() < subscriptionExpirationDate
        ) {
          return {
            id: formData.subscription_ID,
            active: true,
            subscriptionName: "",
            subscriptionPlanName: "",
            updatePaymentMethod: null,
            status: "paused",
            scheduledChange: null,
            cardTokens: formData?.card_tokens,
          };
        }
      }
    }

    // Check for Paddle subs
    if (user && subscriptionID && subscriptionID.startsWith("sub_")) {
      const sub = await fetchSubscription(subscriptionID);
      if (sub?.data?.status === "active") {
        return {
          active: true,
          subscriptionName: sub.data.items[0].product.name,
          subscriptionPlanName: returnValidSubscriptionPlanName(
            sub.data.items[0].price.name,
          ),
          status: sub.data.status,
          updatePaymentMethod: `${sub.data.management_urls.update_payment_method}`,
          id: sub.data.id,
          scheduledChange: sub.data.scheduled_change,
          cardTokens: formData?.card_tokens,
        };
      }
      if (sub?.data?.status === "past_due") {
        return {
          id: sub.data.id,
          active: false,
          subscriptionName: sub.data.items[0].product.name,
          subscriptionPlanName: returnValidSubscriptionPlanName(
            sub.data.items[0].price.name,
          ),
          status: sub.data.status,
          updatePaymentMethod: `${sub.data.management_urls.update_payment_method}`,
          scheduledChange: sub.data.scheduled_change,
          cardTokens: formData?.card_tokens,
        };
      }
      if (sub?.data?.id) {
        return {
          id: sub.data.id,
          active: false,
          subscriptionName: sub.data.items[0].product.name,
          subscriptionPlanName: returnValidSubscriptionPlanName(
            sub.data.items[0].price.name,
          ),
          lifetime: false,
          status: sub.data.status,
          updatePaymentMethod: `${sub.data.management_urls.update_payment_method}`,
          scheduledChange: null,
          cardTokens: formData?.card_tokens,
        };
      }
    } else if (user && transactionID) {
      const txn = await fetchTransaction(transactionID);
      await supabase
        .from("profiles")
        .update({
          subscription_ID: txn.data.subscription_id,
        })
        .eq("paddle_transaction_ID", transactionID)
        .select();
      const sub = await fetchSubscription(txn.data.subscription_id);
      if (sub.data?.status === "active") {
        return {
          active: true,
          subscriptionName: sub.data.items[0].product.name,
          subscriptionPlanName: returnValidSubscriptionPlanName(
            sub.data.items[0].price.name,
          ),
          status: sub.data.status,
          updatePaymentMethod: `${sub.data.management_urls.update_payment_method}`,
          id: sub.data.id,
          scheduledChange: sub.data.scheduled_change,
          cardTokens: formData?.card_tokens,
        };
      }
      if (sub.data?.status === "past_due") {
        return {
          id: sub.data.id,
          active: true,
          subscriptionName: sub.data.items[0].product.name,
          subscriptionPlanName: returnValidSubscriptionPlanName(
            sub.data.items[0].price.name,
          ),
          status: sub.data.status,
          updatePaymentMethod: `${sub.data.management_urls.update_payment_method}`,
          scheduledChange: sub.data.scheduled_change,
          cardTokens: formData?.card_tokens,
        };
      }
    }

    // if (formData?.subscription_last_payment_date) {
    //   const subscriptionExpirationDate = new Date(
    //     formData?.subscription_last_payment_date,
    //   );
    //   const subId = formData.subscription_ID;
    //
    //   if (subId && subId.startsWith("sub_")) {
    //     subscriptionExpirationDate.setMonth(
    //       new Date(subscriptionExpirationDate).getMonth(),
    //     );
    //   } else {
    //     subscriptionExpirationDate.setMonth(
    //       new Date(subscriptionExpirationDate).getMonth() + 1,
    //     );
    //   }
    //
    //   if (
    //     user &&
    //     formData?.subscription_status === "CANCELLED" &&
    //     new Date() < subscriptionExpirationDate
    //   ) {
    //     return true;
    //   }
    //   if (
    //     user &&
    //     formData?.subscription_status === "PAUSED" &&
    //     new Date() < subscriptionExpirationDate
    //   ) {
    //     return true;
    //   }
    // }
    return {
      active: false,
      subscriptionName: "",
      subscriptionPlanName: "",
      lifetime: false,
      status: undefined,
      updatePaymentMethod: null,
      scheduledChange: null,
      id: undefined,
      cardTokens: formData?.card_tokens,
    };
  }

  return {
    active: false,
    subscriptionName: "",
    subscriptionPlanName: "",
    lifetime: false,
    status: undefined,
    updatePaymentMethod: null,
    scheduledChange: null,
    id: undefined,
    cardTokens: null,
  };
}

export async function fetchSubscription(subscriptionID: string) {
  try {
    const res = await fetch(
      `${process.env.NEXT_PUBLIC_PADDLE_API_LINK}/subscriptions/${subscriptionID}`,
      {
        headers: {
          Authorization: `Bearer ${process.env.PADDLE_BEARER_TOKEN}`,
        },
      },
    );
    if (res.status === 404) {
      console.log("Subscription not found");
    }
    if (!res.ok) {
      console.log(`Error fetching subscription: ${res.statusText}`);
    }
    return (await res.json()) as PaddleSubscription;
  } catch (error) {
    console.error(error);
    throw error;
  }
}

export async function fetchTransaction(transactionID: string) {
  try {
    const res = await fetch(
      `${process.env.NEXT_PUBLIC_PADDLE_API_LINK}/transactions/${transactionID}`,
      {
        headers: {
          Authorization: `Bearer ${process.env.PADDLE_BEARER_TOKEN}`,
        },
      },
    );
    if (res.status === 404) {
      console.log("Subscription not found");
    }
    if (!res.ok) {
      console.log(`Failed to fetch transaction: ${res.statusText}`);
    }

    return (await res.json()) as PaddleTransaction;
  } catch (error) {
    console.error("Error fetching transaction:", error);
    throw error;
  }
}

function returnValidSubscriptionPlanName(subscriptionPlanName: string) {
  if (
    SUBSCRIPTION_PLAN_NAMES.some((name) => subscriptionPlanName.includes(name))
  )
    return subscriptionPlanName;
  return "Pro " + subscriptionPlanName;
}

export const getCachedSubscriptionNameAndStatus = cache(
  async () => await getSubscriptionNameAndStatus(),
);
