"use server";
import { PaddleTransaction } from "@/types";

export async function getTransaction(transactionID: string) {
  try {
    const res = await fetch(
      `${process.env.NEXT_PUBLIC_PADDLE_API_LINK}/transactions/${transactionID}`,
      {
        headers: {
          Authorization: `Bearer ${process.env.PADDLE_BEARER_TOKEN}`,
        },
      },
    );

    if (!res.ok) {
      console.log(`Failed to fetch transaction: ${res.status}`);
    }

    return (await res.json()) as PaddleTransaction;
  } catch (error) {
    console.error("Error fetching transaction:", error);
    throw error;
  }
}
