import { SupabaseClient } from "@supabase/supabase-js";
import { EventName } from "@paddle/paddle-node-sdk";

export async function getUserData({
  supabase,
  subscriptionID,
  transactionID,
  eventType,
}: {
  supabase: SupabaseClient;
  subscriptionID?: string | undefined;
  transactionID?: string | undefined;
  eventType: "subscription" | "transaction" | EventName.SubscriptionCreated;
}) {
  if (eventType === "subscription") {
    if (!subscriptionID) {
      return null;
    }
    const { data } = await supabase
      .from("profiles")
      .select("subscription_status,subscription_last_payment_date")
      .eq("subscription_ID", subscriptionID);
    if (data) {
      const dbSubscriptionLastPaymentDate: string | null =
        data[0]?.subscription_last_payment_date;
      const dbSubscriptionStatus: string | null = data[0]?.subscription_status;
      return {
        subscriptionStatus: dbSubscriptionStatus,
        subscriptionLastPaymentDate: dbSubscriptionLastPaymentDate,
        eventType,
      };
    }
  }
  if (eventType === EventName.SubscriptionCreated) {
    const { data } = await supabase
      .from("profiles")
      .select("subscription_ID")
      .eq("paddle_transaction_ID", transactionID);
    if (data) {
      return {
        subscriptionID: data[0]?.subscription_ID as string | null,
        eventType,
      };
    }
  }
  if (eventType === "transaction") {
    if (!transactionID) {
      return null;
    }
    const { data } = await supabase
      .from("profiles")
      .select("subscription_ID")
      .eq("paddle_transaction_ID", transactionID);
    if (data) {
      return {
        subscriptionID: data[0]?.subscription_ID as string | null,
        eventType,
      };
    }
  }

  return null;
}
