// "use client";
// import React from "react";
//
// export default function setCardWatermark(
//   setterFunction: (value: React.SetStateAction<boolean>) => void,
//   formData: {
//     subscription_status: any;
//     subscription_last_payment_date: any;
//   } | null,
//   user: any,
//   subscriptionExpirationDate: Date,
// ) {
//   if (user && formData?.subscription_status === null) {
//     setterFunction(true);
//   }
//   if (user && formData?.subscription_status === "") {
//     setterFunction(true);
//   }
//   if (
//     user &&
//     formData?.subscription_status === "CANCELLED" &&
//     new Date() > subscriptionExpirationDate
//   ) {
//     setterFunction(true);
//   }
//   if (
//     user &&
//     formData?.subscription_status === "PAUSED" &&
//     new Date() > subscriptionExpirationDate
//   ) {
//     setterFunction(true);
//   }
//   if (user && formData?.subscription_status === "PAST_DUE") {
//     setterFunction(true);
//   }
// }
