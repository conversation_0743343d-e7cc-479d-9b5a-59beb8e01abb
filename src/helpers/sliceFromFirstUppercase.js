export function sliceFromFirstUppercase(inputString) {
  const firstColor = inputString[0].toLocaleUpperCase() + inputString.slice(1);
  const match = inputString.match(/[A-Z]/); // Find the first uppercase letter
  if (match) {
    const index = match.index; // Get the index of the first uppercase letter
    return firstColor.slice(0, index) + " " + inputString.substring(index); // Slice from that index to the end
  }
  // If there are no uppercase letters, return the original string
  return firstColor;
}
