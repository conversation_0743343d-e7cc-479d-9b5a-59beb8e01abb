"use client";
import { useSelector } from "react-redux";
import { storeState } from "@/store/store";

export type StateKeys = keyof storeState["mainFormSlice"];
export type CropperKeys = keyof storeState["cropperSlice"];

export function useGetStoreState<K extends StateKeys>(
  stateKey: K,
): storeState["mainFormSlice"][K] {
  return useSelector((state: storeState) => state.mainFormSlice[stateKey]);
}

export function useGetCropperState<K extends CropperKeys>(
  stateKey: K,
): storeState["cropperSlice"][K] {
  return useSelector((state: storeState) => state.cropperSlice[stateKey]);
}
