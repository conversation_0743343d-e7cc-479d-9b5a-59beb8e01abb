"use client";

import { useState, useEffect } from "react";

/**
 * Optimized hook to detect dark mode changes with memoization
 * Prevents unnecessary re-renders by only updating state when the theme actually changes
 */
export const useDarkMode = () => {
  const [isDark, setIsDark] = useState(() =>
    typeof window !== "undefined" 
      ? document.documentElement.classList.contains("dark")
      : false
  );

  useEffect(() => {
    if (typeof window === "undefined") return;

    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (
          mutation.type === "attributes" &&
          mutation.attributeName === "class"
        ) {
          const newIsDark = document.documentElement.classList.contains("dark");
          // Only update state if the value actually changed
          setIsDark(prev => prev !== newIsDark ? newIsDark : prev);
        }
      });
    });

    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ["class"],
    });

    return () => observer.disconnect();
  }, []);

  return isDark;
};
