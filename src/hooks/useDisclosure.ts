import { useState, useCallback, useRef } from 'react';

export interface UseDisclosureOptions {
  onOpen?: () => void;
  onClose?: () => void;
}

export interface UseDisclosureHandlers {
  open: () => void;
  close: () => void;
  toggle: () => void;
}

export type UseDisclosureReturnValue = readonly [boolean, UseDisclosureHandlers];

export function useDisclosure(
  initialState = false,
  options?: UseDisclosureOptions
): UseDisclosureReturnValue {
  const [state, setState] = useState(initialState);
  const optionsRef = useRef(options);
  optionsRef.current = options;

  const open = useCallback(() => {
    setState((current) => {
      if (!current) {
        optionsRef.current?.onOpen?.();
        return true;
      }
      return current;
    });
  }, []);

  const close = useCallback(() => {
    setState((current) => {
      if (current) {
        optionsRef.current?.onClose?.();
        return false;
      }
      return current;
    });
  }, []);

  const toggle = useCallback(() => {
    setState((current) => {
      const newState = !current;
      if (newState) {
        optionsRef.current?.onOpen?.();
      } else {
        optionsRef.current?.onClose?.();
      }
      return newState;
    });
  }, []);

  return [state, { open, close, toggle }] as const;
}
