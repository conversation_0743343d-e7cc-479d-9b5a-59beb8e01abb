import { useLayoutEffect, useRef, useState } from "react";
import { useGetStoreState } from "@/helpers/useGetStoreState";

export function useGetResponsiveFontsizeInPx({
  useTextSizeState = false,
  useTriggerTextSizeState = false,
  desiredTextSizeInPx = 16,
  baseSizeOfElement = 640,
}: {
  useTextSizeState?: boolean | undefined;
  useTriggerTextSizeState?: boolean | undefined;
  desiredTextSizeInPx?: number | undefined;
  baseSizeOfElement?: number;
} = {}) {
  const abilityTextSize = useGetStoreState("abilityTextSize") as number;
  const triggerTextFontSize = useGetStoreState("triggerTextFontSize") as number;

  const [size, setSize] = useState<DOMRect | undefined>(undefined);
  const card = useRef<null | HTMLElement>(null);
  let fontSizePx;
  useLayoutEffect(() => {
    if (card.current === null) {
      card.current = document?.querySelector("#card-element");
    }

    const observer = new ResizeObserver(function (e) {
      setSize(e[0].contentRect);
    });

    setSize(card?.current?.getBoundingClientRect());

    if (card.current) observer?.observe(card.current);
    return function () {
      if (card.current) observer.unobserve(card.current);
    };
  }, [card]);
  /* useResizeObserver(card.current, (e) => {
    setSize(e.contentRect);
  });*/

  if (size) {
    fontSizePx = Math.min(
      (size?.width / baseSizeOfElement) *
        (useTextSizeState
          ? abilityTextSize
          : useTriggerTextSizeState
            ? triggerTextFontSize
            : desiredTextSizeInPx),
    );
  }
  return fontSizePx;
}
