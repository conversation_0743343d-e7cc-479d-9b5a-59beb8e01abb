import { useGetStoreState } from "@/helpers/useGetStoreState";
import { CharacterBorder, LeaderBorder } from "@/types";

export function useTextBorderColor(
  cardType: "character" | "leader" | "event" | "stage" | "don",
  position: "right" | "left" = "right",
) {
  const leaderBorder = useGetStoreState("leaderBorder") as LeaderBorder;
  const characterBorder = useGetStoreState(
    "characterBorder",
  ) as CharacterBorder;
  const colorArray = useGetStoreState("colorArray");
  const color2 = colorArray[colorArray.length - 1];
  if (cardType === "character") {
    if (characterBorder !== "none") {
      return "text-neutral-950";
    } else {
      return "";
    }
  }
  if (cardType === "leader") {
    if (
      leaderBorder === "standard-white" ||
      leaderBorder === "25th" ||
      leaderBorder === "AA-black-and-white" ||
      leaderBorder === "AA-black" ||
      leaderBorder === "full-art"
    ) {
      return "text-neutral-950";
    }
    if (leaderBorder === "AA-white" && position === "right") {
      return "text-neutral-950";
    }
    if (
      leaderBorder === "standard" &&
      color2 === "yellow" &&
      position === "right"
    ) {
      return "text-neutral-950";
    } else if (
      leaderBorder === "standard" &&
      color2 !== "yellow" &&
      position === "right"
    ) {
      return "text-neutral-50";
    }

    if (leaderBorder === "rainbow") {
      if (position === "right") {
        return "text-neutral-950";
      } else {
        return "";
      }
    }
  }
  if (cardType === "event") {
    return "text-neutral-950";
  }
}
