/*
"use client";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import { useDispatch } from "react-redux";
import { handleColorChange } from "@/app/helpers/storeUpdaterFunctions";
import { useRouter } from "next/navigation";
import { Color } from "@/types";

export function useUpdateColorOnNavigate() {
  const color = useGetStoreState("color") as Color;
  const router = useRouter();
  const dispatch = useDispatch();

  color.split("").forEach((letter, i) => {
    if (isUpperCase(letter)) {
      const newColor = color.slice(0, i) as Color;
      handleColorChange(dispatch, newColor);
      router.prefetch("/character");
    }
  });
}
function isUpperCase(char: string) {
  return char === char.toUpperCase();
}
*/
