import PQueue from "p-queue";

let queueInstance: PQueue;

export function getQueue() {
  if (!queueInstance) {
    queueInstance = new PQueue({ concurrency: 10 });

    // Optional: Add logging for debugging
    // queueInstance.on("add", () =>
    //   console.log(`Queue size: ${queueInstance.size}`),
    // );
    // queueInstance.on("next", () =>
    //   console.log(
    //     `Queue size: ${queueInstance.size}, Running: ${queueInstance.pending}`,
    //   ),
    // );
  }

  return queueInstance;
}
