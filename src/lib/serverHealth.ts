/**
 * Server-side utility to check if the card generation server is online
 */
export async function checkServerHealth(): Promise<boolean> {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_GET_CARD_API_V2_URL}/api/health`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
        // Short timeout to prevent long waiting times if server is down
        signal: AbortSignal.timeout(5000),
      },
    );

    return response.ok;
  } catch {
    return false;
  }
}
