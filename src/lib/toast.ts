import { toast, ToastT, ExternalToast } from "sonner";
import * as React from "react";

// Use the ExternalToast type from sonner for better type safety
type ToastOptions = ExternalToast;

// Export types for use in other files
export type { ToastT, ExternalToast };

// Default duration for all toasts
const DEFAULT_DURATION = 6000;

/**
 * Show a success toast notification
 */
export function showSuccessToast(
  title: React.ReactNode,
  options?: ToastOptions,
) {
  return toast.success(title, {
    duration: DEFAULT_DURATION,
    ...options,
  });
}

/**
 * Show an error toast notification
 */
export function showErrorToast(title: React.ReactNode, options?: ToastOptions) {
  return toast.error(title, {
    duration: DEFAULT_DURATION,
    ...options,
  });
}

/**
 * Show a warning toast notification
 */
export function showWarningToast(
  title: React.ReactNode,
  options?: ToastOptions,
) {
  return toast.warning(title, {
    duration: DEFAULT_DURATION,
    ...options,
  });
}

/**
 * Show an info toast notification
 */
export function showInfoToast(title: React.ReactNode, options?: ToastOptions) {
  return toast.info(title, {
    duration: DEFAULT_DURATION,
    ...options,
  });
}

/**
 * Show a loading toast notification
 */
export function showLoadingToast(
  title: React.ReactNode,
  options?: ToastOptions,
) {
  return toast.loading(title, {
    duration: DEFAULT_DURATION,
    ...options,
  });
}

/**
 * Show a promise toast notification
 */
export function showPromiseToast<T>(
  promise: Promise<T> | (() => Promise<T>),
  {
    loading,
    success,
    error,
    finally: onFinally,
  }: {
    loading: React.ReactNode;
    success:
      | React.ReactNode
      | ((data: T) => React.ReactNode | Promise<React.ReactNode>);
    error:
      | React.ReactNode
      | ((error: unknown) => React.ReactNode | Promise<React.ReactNode>);
    finally?: () => void | Promise<void>;
  },
  options?: ToastOptions,
) {
  return toast.promise(promise, {
    loading,
    success,
    error,
    finally: onFinally,
    duration: DEFAULT_DURATION,
    ...options,
  });
}

/**
 * Show a custom toast notification
 */
export function showCustomToast(
  title: React.ReactNode,
  options?: ToastOptions,
) {
  return toast(title, {
    duration: DEFAULT_DURATION,
    ...options,
  });
}

/**
 * Dismiss a toast by ID
 */
export function dismissToast(id?: string | number) {
  return toast.dismiss(id);
}

/**
 * Get all active toasts
 */
export function getToasts() {
  return toast.getToasts();
}
