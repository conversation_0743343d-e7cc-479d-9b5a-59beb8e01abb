import { NextResponse, type NextRequest } from "next/server";
import { updateSession } from "@/utils/supabase/middleware";

const apiPathRegex =
  /^\/api\/(?!health$|auth\/callback$|auth\/signout$|paddle\/webhooks$|paypal\/subs$).*/;
const trpcPathRegex = /^\/trpc\//;
const accountPathRegex = /^\/account$|^\/reset-password\/.*/;

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const origin = request.headers.get("origin") ?? "";

  // 1. Global origin whitelist check
  const allowedDomains =
    process.env.NODE_ENV === "development"
      ? [
          "paddle.com",
          "profitwell.com",
          "ultimatetcgcm.com",
          "preview.ultimatetcgcm.com",
          "dev.meguminrs.com",
          "dev2.meguminrs.com",
          "localhost:3000",
          "localhost:5173",
          "localhost:3001",
          "localhost",
          "***************:5173",
          "127.0.0.1:3000",
          "127.0.0.1:3001",
          "127.0.0.1",
          "ultimatetcgcm-vercel.vercel.app",
        ]
      : [
          "paddle.com",
          "profitwell.com",
          "ultimatetcgcm.com",
          "preview.ultimatetcgcm.com",
        ];
  if (origin && !allowedDomains.some((d) => origin.includes(d))) {
    return new NextResponse(null, { status: 403 });
  }

  // 2. Session handling (applies to all matched routes)
  if (accountPathRegex.test(pathname)) {
    return await updateSession(request);
  }
  const res = NextResponse.next({
    request,
  });

  // 3. Conditionally add CORS only for API / tRPC
  if (apiPathRegex.test(pathname) || trpcPathRegex.test(pathname)) {
    res.headers.set("Access-Control-Allow-Origin", origin);
    res.headers.set("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
    res.headers.set("Access-Control-Allow-Headers", "*");
    res.headers.set("Access-Control-Allow-Credentials", "true");
  }

  return res;
}

export const config = {
  matcher: [
    // All page routes except _next, favicon, and image assets
    // "/:path((?!api|_next/static|_next/image|favicon\\.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)",
    "/account",
    "/reset-password",
    "/account/:path*",
    // Your API routes (excluding health, auth callbacks, etc.)
    "/api/:path((?!health$|auth/callback$|auth/signout$|paddle/webhooks$|paypal/subs$).*)",
    // All tRPC routes
    "/trpc/:path*",
  ],
};
