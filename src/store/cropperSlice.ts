import { createSlice, PayloadAction } from "@reduxjs/toolkit";

export type cropperState = {
  zoom: number;
  crop: { x: number; y: number };
  fileUrl: string | null;
  // fileType: string | null;
  // imageType: "foreground" | "background" | null;
};

// export type CropperStateKeys = keyof cropperState;

const initialState: cropperState = {
  zoom: 1,
  crop: { x: 0, y: 0 },
  fileUrl: null,
  // fileType: null,
  // imageType: null,
};

const cropperSlice = createSlice({
  name: "Cropper",
  initialState,
  reducers: {
    onCrop(state, action: PayloadAction<{ x: number; y: number }>) {
      state.crop = action.payload;
    },
    onZoom(state, action: PayloadAction<number>) {
      state.zoom = action.payload;
    },
    onFileUrl(state, action: PayloadAction<string | null>) {
      state.fileUrl = action.payload;
    },
    // onFileType(state, action: PayloadAction<string | null>) {
    //   state.fileType = action.payload;
    // },
    // onImageType(
    //   state,
    //   action: PayloadAction<"foreground" | "background" | null>,
    // ) {
    //   state.imageType = action.payload;
    // },
  },
});

export default cropperSlice.reducer;

export const { onCrop, onZoom, onFileUrl } = cropperSlice.actions;
