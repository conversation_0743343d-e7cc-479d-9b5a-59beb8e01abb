import { createSlice, PayloadAction } from "@reduxjs/toolkit";

import {
  CardAttribute,
  CharacterBorder,
  Color,
  EventBorder,
  LeaderBorder,
} from "@/types";

export type state = {
  colorArray: Color[];
  multiColorMode: boolean;
  attribute: CardAttribute;
  name: string;
  donText: string;
  cardType: string;
  cost: string;
  power: string;
  donPower: string;
  donAbility: boolean;
  ability: string;
  abilityBackground: boolean;
  trigger: boolean;
  triggerText: string;
  counter: boolean;
  counterText: string;
  set: string;
  rarity: string;
  rarity2: string;
  aaStar: boolean;
  printReady: boolean;
  cardNum: string;
  printWave: string;
  image: string;
  imageFile: File | null;
  backgroundImageFile: File | null;
  imageUrl: string;
  backgroundImageUrl: string;
  imageError: string;
  artist: string;
  life: string;
  foilBorder: boolean;
  dropShadow: boolean;
  abilityDropShadow: boolean;
  abilityTextSize: number;
  triggerTextFontSize: number;
  typeFontSize: number;
  nameFontSize: number;
  donFontSize: number;
  imageFull: boolean;
  blackBorder: boolean;
  ["op1/2 AA"]: boolean;
  ["op3 AA"]: boolean;
  rainbow: boolean;
  powerBlack: boolean;
  leaderBorder: LeaderBorder;
  eventBorder: EventBorder;
  leaderBorderEnabled: boolean;
  cardKind?: string;
  cardKindRoute?: "character" | "leader" | "event" | "stage" | "don";
  isCropping: boolean;
  isCroppingBackground: boolean;
  helpScreen: boolean;
  editorState: {
    type: "doc";
    content: [{ type: "paragraph"; attrs: { textAlign: "left" } }];
  };
  triggerEditorState: {
    type: "doc";
    content: [{ type: "paragraph"; attrs: { textAlign: "left" } }];
  };
  editorDefaultState: {
    type: "doc";
    content: [{ type: "paragraph"; attrs: { textAlign: "left" } }];
  };
  characterBorder: CharacterBorder;
  SPTop: boolean;
};

export type DownloadState = state & { cardSubscriptionStatus: boolean };
export type StateKeys = keyof state;

type InputForStateKeys =
  | "name"
  | "donText"
  | "cardType"
  | "cost"
  | "powerBlack"
  | "power"
  | "donPower"
  | "donAbility"
  | "multiColorMode"
  | "blackBorder"
  | "op3 AA"
  | "op1/2 AA"
  | "rainbow"
  | "foilBorder"
  | "trigger"
  | "triggerText"
  | "counter"
  | "counterText"
  | "set"
  | "rarity"
  | "rarity2"
  | "cardNum"
  | "printWave"
  | "imageFull"
  | "imageUrl"
  | "artist"
  | "abilityBackground"
  | "leaderBorderEnabled"
  | "aaStar"
  | "printReady"
  | "life";
/*const stringKeys = [
  "name",
  "cardType",
  "cost",

  "power",

  "triggerText",

  "counterText",
  "set",
  "rarity",
  "cardNum",
  "printWave",

  "imageUrl",
  "artist",
];*/

/*const booleanKeys = [
  "powerBlack",
  "blackBorder",
  "op3 AA",
  "op1/2 AA",
  "rainbow",
  "foilBorder",
  "trigger",
  "counter",
  "imageFull",
  "abilityBackground",
];*/

const initialState: state = {
  colorArray: ["red"],
  multiColorMode: false,
  donText: "",
  attribute: "ranged",
  name: "",
  cardType: "",
  cost: "1",
  power: "0",
  donPower: "1000",
  ability: "<p></p>",
  donAbility: false,
  abilityBackground: true,
  trigger: false,
  triggerText: "<p></p>",
  counter: true,
  counterText: "",
  set: "",
  rarity: "",
  rarity2: "",
  aaStar: false,
  printReady: false,
  cardNum: "",
  printWave: "",
  image: "",
  imageFile: null,
  backgroundImageFile: null,
  imageUrl: "",
  backgroundImageUrl: "",
  imageError: "",
  artist: "",
  life: "5",
  foilBorder: false,
  dropShadow: false,
  abilityDropShadow: false,
  abilityTextSize: 20,
  triggerTextFontSize: 19.8,
  typeFontSize: 17.3,
  nameFontSize: 41.4,
  donFontSize: 45.4,
  imageFull: false,
  blackBorder: true,
  ["op1/2 AA"]: false,
  ["op3 AA"]: false,
  rainbow: false,
  powerBlack: false,
  leaderBorder: "standard",
  eventBorder: "standard",
  leaderBorderEnabled: true,
  cardKindRoute: "character",
  isCropping: false,
  isCroppingBackground: false,
  helpScreen: false,
  SPTop: false,
  editorState: {
    type: "doc",
    content: [{ type: "paragraph", attrs: { textAlign: "left" } }],
  },
  triggerEditorState: {
    type: "doc",
    content: [{ type: "paragraph", attrs: { textAlign: "left" } }],
  },
  editorDefaultState: {
    type: "doc",
    content: [{ type: "paragraph", attrs: { textAlign: "left" } }],
  },
  characterBorder: "standard",
};

const mainFormSlice = createSlice({
  name: "MainForm",
  initialState,
  reducers: {
    onAbility(state, action) {
      state.ability = action.payload;
    },
    onTriggerText(state, action) {
      state.triggerText = action.payload;
      if (action.payload.length > 7) {
        if (state.trigger !== true) {
          state.trigger = true;
        }
      } else {
        state.trigger = false;
      }
    },
    onEditorState(state, action) {
      state.editorState = action.payload;
    },
    onTypeFontSize(state, action) {
      state.typeFontSize = action.payload;
    },
    onNameFontSize(state, action) {
      state.nameFontSize = action.payload;
    },
    onDonFontSize(state, action) {
      state.donFontSize = action.payload;
    },
    onTriggerEditorState(state, action) {
      state.triggerEditorState = action.payload;
    },
    onDropShadow(state) {
      state.dropShadow = !state.dropShadow;
    },
    onAbilityDropShadow(state) {
      state.abilityDropShadow = !state.abilityDropShadow;
    },
    onInputField: {
      prepare(
        inputFor: InputForStateKeys,
        value?: string | boolean | undefined,
      ) {
        return { payload: { inputFor, value } };
      },
      reducer(
        state,
        action: PayloadAction<{
          inputFor: InputForStateKeys;
          value: boolean | string | undefined;
        }>,
      ) {
        if (
          action.payload.inputFor === "blackBorder" ||
          action.payload.inputFor === "op1/2 AA" ||
          action.payload.inputFor === "op3 AA" ||
          action.payload.inputFor === "rainbow"
        ) {
          state["blackBorder"] = false;
          state["op1/2 AA"] = false;
          state["op3 AA"] = false;
          state["rainbow"] = false;
          state[action.payload.inputFor] =
            typeof action.payload.value === "boolean" && action.payload.value;
        } else if (action.payload.inputFor === "abilityBackground") {
          state[action.payload.inputFor] = !state[action.payload.inputFor];
        } else if (
          (action.payload.inputFor === "name" ||
            action.payload.inputFor === "donText" ||
            action.payload.inputFor === "cardType" ||
            action.payload.inputFor === "cost" ||
            action.payload.inputFor === "life" ||
            action.payload.inputFor === "power" ||
            action.payload.inputFor === "donPower" ||
            action.payload.inputFor === "triggerText" ||
            action.payload.inputFor === "counterText" ||
            action.payload.inputFor === "set" ||
            action.payload.inputFor === "rarity" ||
            action.payload.inputFor === "rarity2" ||
            action.payload.inputFor === "cardNum" ||
            action.payload.inputFor === "printWave" ||
            action.payload.inputFor === "imageUrl" ||
            action.payload.inputFor === "artist") &&
          typeof action.payload.value === "string"
        ) {
          if (action.payload.value !== state[action.payload.inputFor])
            state[action.payload.inputFor] = action.payload.value;
        } else if (
          (action.payload.inputFor === "powerBlack" ||
            action.payload.inputFor === "leaderBorderEnabled" ||
            action.payload.inputFor === "aaStar" ||
            action.payload.inputFor === "printReady" ||
            action.payload.inputFor === "foilBorder" ||
            action.payload.inputFor === "donAbility" ||
            action.payload.inputFor === "multiColorMode" ||
            action.payload.inputFor === "trigger" ||
            action.payload.inputFor === "counter" ||
            action.payload.inputFor === "imageFull") &&
          typeof action.payload.value === "boolean"
        ) {
          if (action.payload.value !== state[action.payload.inputFor]) {
            state[action.payload.inputFor] = action.payload.value;
          }
        }
      },
    },
    onAbilityTextSize(state, action) {
      if (action.payload === "default") {
        state.abilityTextSize = initialState.abilityTextSize;
      } else if (
        Number((state.abilityTextSize + action.payload).toFixed(1)) < 1
      ) {
      } else {
        state.abilityTextSize = Number(
          (state.abilityTextSize + action.payload).toFixed(1),
        );
      }
    },
    onTriggerTextFontSize(state, action) {
      if (action.payload === "default") {
        state.triggerTextFontSize = initialState.triggerTextFontSize;
      } else if (
        Number((state.triggerTextFontSize + action.payload).toFixed(1)) < 1
      ) {
      } else {
        state.triggerTextFontSize = Number(
          (state.triggerTextFontSize + action.payload).toFixed(1),
        );
      }
    },
    onAbilityScreenshotTextSize(state, action) {
      state.abilityTextSize = action.payload;
    },
    onAttribute(state, action: { payload: CardAttribute; type: string }) {
      state.attribute = action.payload;
    },
    onColorArray(state, action: { payload: Color | Color[]; type: string }) {
      if (Array.isArray(action.payload)) {
        // If it's already an array, use it directly
        state.colorArray = action.payload;
      } else {
        // If it's a single color, convert to array with one item
        state.colorArray = [action.payload];
      }
    },
    onLeaderBorderType(state, action: { payload: LeaderBorder; type: string }) {
      state.leaderBorder = action.payload;
    },
    onEventBorderType(state, action: { payload: EventBorder; type: string }) {
      state.eventBorder = action.payload;
    },
    onCharacterBorderType(
      state,
      action: { payload: CharacterBorder; type: string },
    ) {
      state.characterBorder = action.payload;
    },
    onImageFull(state) {
      state.dropShadow = !state.dropShadow;
    },
    onForegroundImageFileUpload(state, action) {
      state.imageFile = action.payload;
    },
    onBackgroundImageFileUpload(state, action) {
      state.backgroundImageFile = action.payload;
    },
    onImageUrl(state, action) {
      state.imageUrl = action.payload;
    },
    onBackgroundImageUrl(state, action) {
      state.backgroundImageUrl = action.payload;
    },
    /*    onChangeRoute(state) {
      state.color = sliceFromFirstUppercase(state.color)
        .split(" ")[0]
        .toLocaleLowerCase();
    },*/
    onIsCropping(state, action) {
      state.isCropping = action.payload;
    },
    onIsCroppingBackground(state, action) {
      state.isCroppingBackground = action.payload;
    },
    onLeaderBorderEnabled(state, action) {
      state.leaderBorderEnabled = action.payload;
    },
    onAAStar(state, action) {
      state.aaStar = action.payload;
    },
    onPrintReady(state, action) {
      state.printReady = action.payload;
    },
    onHelpScreen(state, action) {
      state.helpScreen = action.payload;
    },
    onCardKindRoute(state, action) {
      state.cardKindRoute = action.payload;
    },
    onSPTop(state, action) {
      state.SPTop = action.payload;
    },
    onSetDefaultStore(state, action) {
      if (action.payload.colorArray) {
        const colorArray = action.payload.colorArray;

        if (Array.isArray(colorArray)) {
          state.colorArray = colorArray;
        } else {
          const decoded = decodeURIComponent(colorArray);

          state.colorArray = JSON.parse(decoded);
        }
      }
      if (
        action.payload.color &&
        !action.payload.colorArray &&
        action.payload.color2
      ) {
        if (action.payload.color === action.payload.color2) {
          state.colorArray = [action.payload.color];
        } else {
          state.colorArray = [action.payload.color, action.payload.color2];
        }
      }
      if (action.payload.attribute) {
        state.attribute = action.payload.attribute;
      }
      if (action.payload.name) {
        state.name = decodeURIComponent(action.payload.name);
      }
      if (action.payload.donText) {
        state.donText = decodeURIComponent(action.payload.donText);
      }
      if (action.payload.cardType) {
        state.cardType = decodeURIComponent(action.payload.cardType);
      }
      if (action.payload.cost) {
        state.cost = action.payload.cost;
      }
      if (action.payload.cardKindRoute) {
        state.cardKindRoute = action.payload.cardKindRoute;
      }
      if (action.payload.power) {
        state.power = action.payload.power;
      }
      if (action.payload.donPower) {
        state.donPower = action.payload.donPower;
      }

      if (action.payload.abilityBackground !== undefined) {
        state.abilityBackground = JSON.parse(action.payload.abilityBackground);
      } else {
        state.abilityBackground = true;
      }
      if (action.payload.trigger !== undefined) {
        if (
          (typeof JSON.parse(action.payload.trigger) === "boolean" &&
            state.cardKindRoute === "character") ||
          state.cardKindRoute === "stage" ||
          state.cardKindRoute === "event"
        ) {
          state.trigger = JSON.parse(action.payload.trigger);
        }
      } else {
        state.trigger = true;
      }

      if (action.payload.triggerText) {
        state.triggerText = decodeURIComponent(action.payload.triggerText);
      }
      if (action.payload.counter !== undefined) {
        if (
          typeof JSON.parse(action.payload.counter) === "boolean" &&
          state.cardKindRoute === "character"
        ) {
          state.counter = JSON.parse(action.payload.counter);
        }
      } else {
        state.counter = true;
      }
      if (action.payload.multiColorMode !== undefined) {
        if (typeof JSON.parse(action.payload.multiColorMode) === "boolean") {
          state.multiColorMode = JSON.parse(action.payload.multiColorMode);
        }
      } else {
        if (
          action.payload?.colorArray &&
          action.payload?.colorArray?.length > 2
        ) {
          state.multiColorMode = true;
        }
      }
      if (action.payload.editorState) {
        state.editorState = action.payload.editorState;
      }
      if (action.payload.editorState) {
        state.editorDefaultState = action.payload.editorState;
      }
      if (action.payload.triggerEditorState) {
        state.triggerEditorState = action.payload.triggerEditorState;
      }

      if (action.payload.ability) {
        state.ability = decodeURIComponent(action.payload.ability).replaceAll(
          "__className_8a5225",
          "font-one-piece-italic-bold",
        );
      }
      if (action.payload.triggerText) {
        state.triggerText = action.payload.triggerText;

        if (action.payload.triggerText.length > 7) {
          state.trigger = true;
        } else {
          state.trigger = false;
        }
      }
      if (action.payload.counterText) {
        state.counterText = decodeURIComponent(action.payload.counterText);
      }
      if (action.payload.set) {
        state.set = decodeURIComponent(action.payload.set);
      }
      if (action.payload.rarity) {
        state.rarity = decodeURIComponent(action.payload.rarity);
      }
      if (action.payload.rarity2) {
        state.rarity2 = decodeURIComponent(action.payload.rarity2);
      }

      if (action.payload.cardNum) {
        state.cardNum = decodeURIComponent(action.payload.cardNum);
      }
      if (action.payload.printWave) {
        state.printWave = action.payload.printWave;
      }

      if (action.payload.imageUrl) {
        state.imageUrl = action.payload.imageUrl;
      }
      if (action.payload.backgroundImageUrl) {
        state.backgroundImageUrl = action.payload.backgroundImageUrl;
      }

      if (action.payload.artist) {
        state.artist = decodeURIComponent(action.payload.artist);
      }
      if (action.payload.characterBorder) {
        state.characterBorder = decodeURIComponent(
          action.payload.characterBorder,
        ) as CharacterBorder;
      }
      if (action.payload.life) {
        state.life = decodeURIComponent(action.payload.life);
      }

      if (action.payload.foilBorder !== undefined) {
        if (
          typeof JSON.parse(action.payload.foilBorder) === "boolean" &&
          (state.cardKindRoute === "stage" || state.cardKindRoute === "event")
        ) {
          state.foilBorder = JSON.parse(action.payload.foilBorder);
        }
      } else {
        state.foilBorder = false;
      }
      if (action.payload.donAbility !== undefined) {
        if (
          typeof JSON.parse(action.payload.donAbility) === "boolean" &&
          state.cardKindRoute === "don"
        ) {
          state.donAbility = JSON.parse(action.payload.donAbility);
        }
      } else {
        state.donAbility = false;
      }

      if (action.payload.dropShadow !== undefined) {
        state.dropShadow = JSON.parse(action.payload.dropShadow);
      } else {
        state.dropShadow = false;
      }
      if (action.payload.abilityDropShadow !== undefined) {
        state.abilityDropShadow = JSON.parse(action.payload.abilityDropShadow);
      } else {
        state.abilityDropShadow = false;
      }

      if (action.payload.abilityTextSize) {
        state.abilityTextSize = JSON.parse(action.payload.abilityTextSize);
      }
      if (action.payload.triggerTextFontSize) {
        state.triggerTextFontSize = JSON.parse(
          action.payload.triggerTextFontSize,
        );
      }
      // if (action.payload.typeFontSize) {
      //   state.typeFontSize = JSON.parse(action.payload.typeFontSize);
      // }
      if (action.payload.nameFontSize) {
        state.nameFontSize = JSON.parse(action.payload.nameFontSize);
      }
      if (action.payload.donFontSize) {
        state.donFontSize = JSON.parse(action.payload.donFontSize);
      }
      if (action.payload.blackBorder) {
        state.blackBorder = JSON.parse(action.payload.blackBorder);
      }

      if (action.payload.powerBlack) {
        state.powerBlack = JSON.parse(action.payload.powerBlack);
      }
      if (action.payload.leaderBorderEnabled !== undefined) {
        if (
          typeof JSON.parse(action.payload.leaderBorderEnabled) === "boolean"
        ) {
          state.leaderBorderEnabled = JSON.parse(
            action.payload.leaderBorderEnabled,
          );
        }
      } else {
        state.leaderBorderEnabled = true;
      }
      if (action.payload.aaStar !== undefined) {
        if (typeof JSON.parse(action.payload.aaStar) === "boolean") {
          state.aaStar = JSON.parse(action.payload.aaStar);
        }
      }
      if (action.payload.printReady !== undefined) {
        if (typeof JSON.parse(action.payload.printReady) === "boolean") {
          state.printReady = JSON.parse(action.payload.printReady);
        }
      }
      if (action.payload.leaderBorder) {
        state.leaderBorder = decodeURIComponent(
          action.payload.leaderBorder,
        ) as LeaderBorder;
      }
      if (action.payload.eventBorder) {
        state.eventBorder = decodeURIComponent(
          action.payload.eventBorder,
        ) as EventBorder;
      }
    },
  },
});

export default mainFormSlice.reducer;

export const {
  onAbility,
  onDropShadow,
  onAbilityDropShadow,
  onInputField,
  onAbilityTextSize,
  onTriggerTextFontSize,
  onAttribute,
  onColorArray,
  onLeaderBorderType,
  onCharacterBorderType,
  onEventBorderType,
  onTypeFontSize,
  onNameFontSize,
  onDonFontSize,
  onForegroundImageFileUpload,
  onBackgroundImageFileUpload,
  onImageUrl,
  onBackgroundImageUrl,
  onSetDefaultStore,
  onIsCropping,
  onIsCroppingBackground,
  onEditorState,
  onCardKindRoute,

  onTriggerText,
  onTriggerEditorState,
  onSPTop,
} = mainFormSlice.actions;
