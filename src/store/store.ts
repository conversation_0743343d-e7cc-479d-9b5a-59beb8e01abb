import { configureStore } from "@reduxjs/toolkit";
import mainFormSlice, { state } from "./formSlice";
import cropperSlice, { cropperState } from "./cropperSlice";
import thunk from "redux-thunk";

const store = configureStore({
  devTools: false,
  reducer: {
    mainFormSlice,
    cropperSlice,
  },
  middleware: [thunk],
});

export default store;

export type storeState = {
  mainFormSlice: state;
  cropperSlice: cropperState;
};
