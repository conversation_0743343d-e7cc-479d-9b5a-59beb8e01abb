import "server-only";
import { NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { SupabaseClient } from "@supabase/supabase-js";
import { User } from "@supabase/supabase-js";

/**
 * Utility function to authenticate API requests
 * @returns An object containing the authenticated user and Supabase client, or a NextResponse error
 */

type AuthResult =
  | {
      authenticated: true;
      user: User;
      supabase: SupabaseClient;
      error: null;
    }
  | {
      authenticated: false;
      error: NextResponse;
    };
export async function authenticateApiRequest(): Promise<AuthResult> {
  const supabase = await createClient();

  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return {
      error: new NextResponse(
        JSON.stringify({
          error: { message: "Unauthorized. Authentication required." },
        }),
        {
          status: 401,
          statusText: "Unauthorized",
          headers: {
            "Content-Type": "application/json",
          },
        },
      ),
      authenticated: false,
    };
  }

  return {
    user,
    supabase,
    authenticated: true,
    error: null,
  };
}
