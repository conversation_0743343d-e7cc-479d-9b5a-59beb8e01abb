/**
 * Replaces spaces with &nbsp; only in text content, not within HTML tags
 * This preserves spaces in HTML attributes while ensuring text content doesn't break
 */
export function replaceSpacesInTextContent(html: string): string {
  if (!html) return html;

  // Split the HTML into parts: tags and text content
  const parts: string[] = [];
  let inTag = false;
  let currentPart = "";

  for (let i = 0; i < html.length; i++) {
    const char = html[i];

    if (char === "<") {
      // We're entering a tag
      if (currentPart) {
        parts.push(inTag ? currentPart : currentPart.replace(/ /g, "&nbsp;"));
        currentPart = "";
      }
      inTag = true;
      currentPart += char;
    } else if (char === ">") {
      // We're exiting a tag
      currentPart += char;
      parts.push(currentPart);
      currentPart = "";
      inTag = false;
    } else {
      // We're in the middle of a tag or text content
      currentPart += char;
    }
  }

  // Don't forget the last part
  if (currentPart) {
    parts.push(inTag ? currentPart : currentPart.replace(/ /g, "&nbsp;"));
  }

  return parts.join("");
}
